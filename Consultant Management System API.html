<!DOCTYPE html>
<!-- saved from url=(0049)https://staging.api.erp.fsli-group.com/api/v1/doc -->
<html><head><meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
            
        <title>Consultant Management System API</title>

                <link rel="stylesheet" href="./Consultant Management System API_files/swagger-ui.css">
        <link rel="stylesheet" href="./Consultant Management System API_files/style.css">
    
    <link rel="stylesheet" href="./Consultant Management System API_files/swagger-custom.css">
    <link rel="stylesheet" href="./Consultant Management System API_files/all.min.css">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Consultant Management System API Documentation - Interactive API reference for developers">

                    <script id="swagger-data" type="application/json">{"spec":{"openapi":"3.0.0","info":{"title":"Consultant Management System API","description":"A comprehensive RESTful API for efficient consultant management, time tracking, expense reporting, and leave management","contact":{"name":"API Support","url":"https://consultantmanagementsystem.com/support","email":"<EMAIL>"},"version":"1.0.0"},"paths":{"/api/v1/auth/login":{"post":{"tags":["Authentication"],"summary":"Login to get a JWT token","operationId":"post_api_login","requestBody":{"description":"Login credentials","required":true,"content":{"application/json":{"schema":{"$ref":"#/components/schemas/LoginRequest"}}}},"responses":{"200":{"description":"Returns JWT token and user information","content":{"application/json":{"schema":{"$ref":"#/components/schemas/LoginResponse"}}}},"401":{"description":"Invalid credentials","content":{"application/json":{"schema":{"properties":{"error":{"type":"string","example":"Invalid credentials"}},"type":"object"}}}}}}},"/api/v1/auth/register":{"post":{"tags":["Authentication"],"summary":"Register a new user","operationId":"post_api_register","requestBody":{"description":"Registration data","required":true,"content":{"application/json":{"schema":{"$ref":"#/components/schemas/RegistrationRequest"}}}},"responses":{"201":{"description":"User registered successfully","content":{"application/json":{"schema":{"$ref":"#/components/schemas/RegistrationResponse"}}}},"400":{"description":"Invalid input data","content":{"application/json":{"schema":{"properties":{"error":{"type":"string","example":"Email already in use"}},"type":"object"}}}}}}},"/api/v1/clients":{"get":{"tags":["Clients"],"summary":"Get all clients","operationId":"get_app_client_index","parameters":[{"name":"active","in":"query","description":"Filter by active status","required":false,"schema":{"type":"boolean"}}],"responses":{"200":{"description":"List of clients","content":{"application/json":{"schema":{"type":"array","items":{"properties":{"id":{"type":"integer","example":1},"name":{"type":"string","example":"Acme Corporation"},"code":{"type":"string","example":"ACME"},"isActive":{"type":"boolean","example":true},"address":{"type":"string","example":"123 Main St","nullable":true},"phone":{"type":"string","example":"+1234567890","nullable":true},"email":{"type":"string","example":"<EMAIL>","nullable":true},"website":{"type":"string","example":"https://acme.com","nullable":true}},"type":"object"}}}}}}},"post":{"tags":["Clients"],"summary":"Create a new client (admin only)","operationId":"post_app_client_create","requestBody":{"description":"Client data","required":true,"content":{"application/json":{"schema":{"required":["name","code"],"properties":{"name":{"type":"string","example":"Acme Corporation"},"code":{"type":"string","example":"ACME"},"address":{"type":"string","example":"123 Main St","nullable":true},"phone":{"type":"string","example":"+1234567890","nullable":true},"email":{"type":"string","example":"<EMAIL>","nullable":true},"website":{"type":"string","example":"https://acme.com","nullable":true},"isActive":{"type":"boolean","example":true}},"type":"object"}}}},"responses":{"201":{"description":"Client created successfully"},"400":{"description":"Invalid data"}}}},"/api/v1/clients/{id}":{"get":{"tags":["Clients"],"summary":"Get a specific client","operationId":"get_app_client_show","parameters":[{"name":"id","in":"path","required":true,"schema":{"type":"string"}}],"responses":{"200":{"description":"Client details","content":{"application/json":{"schema":{"properties":{"id":{"type":"integer","example":1},"name":{"type":"string","example":"Acme Corporation"},"code":{"type":"string","example":"ACME"},"isActive":{"type":"boolean","example":true},"address":{"type":"string","example":"123 Main St","nullable":true},"phone":{"type":"string","example":"+1234567890","nullable":true},"email":{"type":"string","example":"<EMAIL>","nullable":true},"website":{"type":"string","example":"https://acme.com","nullable":true}},"type":"object"}}}},"404":{"description":"Client not found"}}},"put":{"tags":["Clients"],"summary":"Update a client (admin only)","operationId":"put_app_client_update","parameters":[{"name":"id","in":"path","description":"Client ID","required":true,"schema":{"type":"integer"}}],"requestBody":{"description":"Client data for updating an existing client. Only administrators can update client information.","required":true,"content":{"application/json":{"schema":{"properties":{"name":{"type":"string","example":"Updated Acme Corporation"},"code":{"type":"string","example":"ACME_NEW"},"address":{"type":"string","example":"456 Updated St","nullable":true},"phone":{"type":"string","example":"+1987654321","nullable":true},"email":{"type":"string","example":"<EMAIL>","nullable":true},"website":{"type":"string","example":"https://updated.acme.com","nullable":true},"isActive":{"type":"boolean","example":false}},"type":"object"}}}},"responses":{"200":{"description":"Client updated successfully","content":{"application/json":{"schema":{"properties":{"id":{"type":"integer","example":1},"name":{"type":"string","example":"Updated Acme Corporation"},"code":{"type":"string","example":"ACME_NEW"},"isActive":{"type":"boolean","example":false},"address":{"type":"string","example":"456 Updated St","nullable":true},"phone":{"type":"string","example":"+1987654321","nullable":true},"email":{"type":"string","example":"<EMAIL>","nullable":true},"website":{"type":"string","example":"https://updated.acme.com","nullable":true},"success":{"type":"boolean","example":true}},"type":"object"}}}},"400":{"description":"Invalid data provided"},"401":{"description":"Authentication required"},"403":{"description":"Access denied - Admin role required"},"404":{"description":"Client not found"}}},"delete":{"tags":["Clients"],"summary":"Delete a client (admin only)","operationId":"delete_app_client_delete","parameters":[{"name":"id","in":"path","description":"Client ID","required":true,"schema":{"type":"integer"}}],"responses":{"200":{"description":"Client deleted successfully","content":{"application/json":{"schema":{"properties":{"id":{"type":"integer","example":1},"name":{"type":"string","example":"Acme Corporation"},"message":{"type":"string","example":"Client successfully deleted"},"success":{"type":"boolean","example":true}},"type":"object"}}}},"400":{"description":"Cannot delete client with associated data"},"401":{"description":"Authentication required"},"403":{"description":"Access denied - Admin role required"},"404":{"description":"Client not found"}}}},"/api/v1/company":{"get":{"tags":["Company"],"summary":"Get company information","operationId":"get_app_company_show","responses":{"200":{"description":"Returns company information","content":{"application/json":{"schema":{"properties":{"id":{"type":"integer","example":1},"name":{"type":"string","example":"TechConsult Solutions"},"address":{"type":"string","example":"15 Avenue des Champs-\u00c9lys\u00e9es, 75008 Paris, France","nullable":true},"phone":{"type":"string","example":"+33 1 42 86 83 00","nullable":true},"email":{"type":"string","example":"<EMAIL>","nullable":true},"website":{"type":"string","example":"https://www.techconsult-solutions.fr","nullable":true},"siret":{"type":"string","example":"85234567890123","nullable":true},"annualLeaveDays":{"description":"Annual leave days for consultants","type":"integer","example":25},"workHoursPerDay":{"description":"Standard work hours per day","type":"number","format":"float","example":7.5},"maxRemoteWorkPercentage":{"description":"Maximum allowed remote work percentage","type":"number","format":"float","example":60},"defaultRemoteWorkPercentage":{"description":"Default remote work percentage for new entries","type":"number","format":"float","example":30},"workTimeValidationRequired":{"description":"Whether work time entries require admin validation","type":"boolean","example":false}},"type":"object"}}}}}},"put":{"tags":["Company"],"summary":"Update company information (admin only)","operationId":"put_app_company_update","requestBody":{"description":"Company information to update","required":true,"content":{"application/json":{"schema":{"required":["name"],"properties":{"name":{"type":"string","example":"TechConsult Solutions"},"address":{"type":"string","example":"15 Avenue des Champs-\u00c9lys\u00e9es, 75008 Paris, France","nullable":true},"phone":{"type":"string","example":"+33 1 42 86 83 00","nullable":true},"email":{"type":"string","example":"<EMAIL>","nullable":true},"website":{"type":"string","example":"https://www.techconsult-solutions.fr","nullable":true},"siret":{"type":"string","example":"85234567890123","nullable":true},"annualLeaveDays":{"description":"Annual leave days for consultants","type":"integer","example":25},"workHoursPerDay":{"description":"Standard work hours per day","type":"number","format":"float","example":7.5},"maxRemoteWorkPercentage":{"description":"Maximum allowed remote work percentage","type":"number","format":"float","example":60},"defaultRemoteWorkPercentage":{"description":"Default remote work percentage for new entries","type":"number","format":"float","example":30},"workTimeValidationRequired":{"description":"Set to true to require admin validation for work time entries","type":"boolean","example":true}},"type":"object"}}}},"responses":{"200":{"description":"Company information updated successfully","content":{"application/json":{"schema":{"properties":{"id":{"type":"integer","example":1},"name":{"type":"string","example":"TechConsult Solutions"},"address":{"type":"string","example":"15 Avenue des Champs-\u00c9lys\u00e9es, 75008 Paris, France","nullable":true},"phone":{"type":"string","example":"+33 1 42 86 83 00","nullable":true},"email":{"type":"string","example":"<EMAIL>","nullable":true},"website":{"type":"string","example":"https://www.techconsult-solutions.fr","nullable":true},"siret":{"type":"string","example":"85234567890123","nullable":true},"annualLeaveDays":{"type":"integer","example":25},"workHoursPerDay":{"type":"number","format":"float","example":7.5},"maxRemoteWorkPercentage":{"type":"number","format":"float","example":60},"defaultRemoteWorkPercentage":{"type":"number","format":"float","example":30},"workTimeValidationRequired":{"type":"boolean","example":true}},"type":"object"}}}},"400":{"description":"Invalid input data","content":{"application/json":{"schema":{"properties":{"error":{"type":"string","example":"Company name is required"},"success":{"type":"boolean","example":false}},"type":"object"}}}},"403":{"description":"Access denied - Admin role required","content":{"application/json":{"schema":{"properties":{"error":{"type":"string","example":"Admin access required"},"success":{"type":"boolean","example":false}},"type":"object"}}}}}}},"/api/v1/consultants":{"get":{"tags":["Consultants"],"summary":"Get all consultants (admin only)","operationId":"get_app_consultant_index","responses":{"200":{"description":"Returns the list of all consultants","content":{"application/json":{"schema":{"type":"array","items":{"$ref":"#/components/schemas/Consultant"}}}}},"403":{"description":"Access denied - Admin role required"}}},"post":{"tags":["Consultants"],"summary":"Create a new consultant (admin only)","operationId":"post_app_consultant_create","requestBody":{"description":"Consultant data","required":true,"content":{"application/json":{"schema":{"required":["email","password","firstName","lastName"],"properties":{"email":{"type":"string","format":"email","example":"<EMAIL>"},"password":{"type":"string","format":"password","example":"securePassword123"},"firstName":{"type":"string","example":"John"},"lastName":{"type":"string","example":"Doe"},"phone":{"type":"string","example":"+33123456789"},"isAdmin":{"type":"boolean","example":false}},"type":"object"}}}},"responses":{"201":{"description":"Consultant created successfully","content":{"application/json":{"schema":{"properties":{"message":{"type":"string","example":"Consultant created successfully"},"id":{"type":"integer","example":1}},"type":"object"}}}},"400":{"description":"Invalid input data"},"403":{"description":"Access denied - Admin role required"}}}},"/api/v1/consultants/me":{"get":{"tags":["Consultants"],"summary":"Get the current consultant's profile","operationId":"get_app_consultant_current","responses":{"200":{"description":"Returns the current consultant's profile","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Consultant"}}}},"401":{"description":"User not authenticated"}}}},"/api/v1/consultants/{id}":{"get":{"tags":["Consultants"],"summary":"Get a specific consultant","operationId":"get_app_consultant_show","parameters":[{"name":"id","in":"path","description":"The consultant ID","required":true,"schema":{"type":"integer"}}],"responses":{"200":{"description":"Returns the consultant details","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Consultant"}}}},"403":{"description":"Access denied"},"404":{"description":"Consultant not found"}}},"put":{"tags":["Consultants"],"summary":"Update an existing consultant","operationId":"put_app_consultant_update","parameters":[{"name":"id","in":"path","description":"The consultant ID","required":true,"schema":{"type":"integer"}}],"requestBody":{"description":"Updated consultant data","required":true,"content":{"application/json":{"schema":{"properties":{"email":{"type":"string","format":"email","example":"<EMAIL>"},"password":{"type":"string","format":"password","example":"newPassword123"},"firstName":{"type":"string","example":"John"},"lastName":{"type":"string","example":"Doe"},"phone":{"type":"string","example":"+33123456789"},"isAdmin":{"type":"boolean","example":false}},"type":"object"}}}},"responses":{"200":{"description":"Consultant updated successfully","content":{"application/json":{"schema":{"properties":{"message":{"type":"string","example":"Consultant updated successfully"},"id":{"type":"integer","example":1}},"type":"object"}}}},"400":{"description":"Invalid input data"},"403":{"description":"Access denied"},"404":{"description":"Consultant not found"}}},"delete":{"tags":["Consultants"],"summary":"Delete a consultant (admin only)","operationId":"delete_app_consultant_delete","parameters":[{"name":"id","in":"path","description":"The consultant ID","required":true,"schema":{"type":"integer"}}],"responses":{"200":{"description":"Consultant deleted successfully","content":{"application/json":{"schema":{"properties":{"message":{"type":"string","example":"Consultant deleted successfully"}},"type":"object"}}}},"403":{"description":"Access denied - Admin role required"},"404":{"description":"Consultant not found"}}}},"/api/v1/dashboard/consultant/{consultantId}":{"get":{"tags":["Dashboard"],"summary":"Get dashboard data for a specific consultant","operationId":"get_app_dashboard_consultant","parameters":[{"name":"consultantId","in":"path","description":"The consultant ID","required":true,"schema":{"type":"integer"}},{"name":"debut","in":"query","description":"Start date (format: Y-m-d)","required":false,"schema":{"type":"string","format":"date"}},{"name":"fin","in":"query","description":"End date (format: Y-m-d)","required":false,"schema":{"type":"string","format":"date"}}],"responses":{"200":{"description":"Returns dashboard data for the consultant","content":{"application/json":{"schema":{"properties":{"consultant":{"properties":{"id":{"type":"integer","example":1},"lastName":{"type":"string","example":"Doe"},"firstName":{"type":"string","example":"John"},"email":{"type":"string","format":"email","example":"<EMAIL>"}},"type":"object"},"period":{"properties":{"start":{"type":"string","format":"date","example":"2023-06-01"},"end":{"type":"string","format":"date","example":"2023-06-30"}},"type":"object"},"workTime":{"properties":{"totalHours":{"type":"number","format":"float","example":160},"remoteHours":{"type":"number","format":"float","example":80},"remoteWorkPercentage":{"type":"number","format":"float","example":50}},"type":"object"},"expenses":{"properties":{"totalEur":{"type":"number","format":"float","example":450.75}},"type":"object"},"leaves":{"properties":{"remainingDays":{"type":"number","format":"float","example":15.5},"year":{"type":"integer","example":2023}},"type":"object"}},"type":"object"}}}},"400":{"description":"Invalid input data","content":{"application/json":{"schema":{"properties":{"message":{"type":"string","example":"Invalid date format"},"success":{"type":"boolean","example":false}},"type":"object"}}}},"403":{"description":"Access denied","content":{"application/json":{"schema":{"properties":{"message":{"type":"string","example":"You can only access your own dashboard"},"success":{"type":"boolean","example":false}},"type":"object"}}}},"404":{"description":"Consultant not found","content":{"application/json":{"schema":{"properties":{"message":{"type":"string","example":"Consultant not found"},"success":{"type":"boolean","example":false}},"type":"object"}}}}}}},"/api/v1/dashboard/admin":{"get":{"tags":["Dashboard"],"summary":"Get admin dashboard data (admin only)","operationId":"get_app_dashboard_admin","parameters":[{"name":"debut","in":"query","description":"Start date (format: Y-m-d)","required":false,"schema":{"type":"string","format":"date"}},{"name":"fin","in":"query","description":"End date (format: Y-m-d)","required":false,"schema":{"type":"string","format":"date"}}],"responses":{"200":{"description":"Returns admin dashboard data","content":{"application/json":{"schema":{"properties":{"period":{"properties":{"start":{"type":"string","format":"date","example":"2023-06-01"},"end":{"type":"string","format":"date","example":"2023-06-30"}},"type":"object"},"global":{"properties":{"consultantCount":{"type":"integer","example":10},"totalHours":{"type":"number","format":"float","example":1600},"remoteHours":{"type":"number","format":"float","example":800},"remoteWorkPercentage":{"type":"number","format":"float","example":50},"totalExpenses":{"type":"number","format":"float","example":4500.75}},"type":"object"},"consultants":{"type":"array","items":{"properties":{"consultant":{"properties":{"id":{"type":"integer","example":1},"lastName":{"type":"string","example":"Doe"},"firstName":{"type":"string","example":"John"}},"type":"object"},"totalHours":{"type":"number","format":"float","example":160},"remoteHours":{"type":"number","format":"float","example":80},"remoteWorkPercentage":{"type":"number","format":"float","example":50},"totalExpenses":{"type":"number","format":"float","example":450.75}},"type":"object"}}},"type":"object"}}}},"400":{"description":"Invalid input data","content":{"application/json":{"schema":{"properties":{"message":{"type":"string","example":"Invalid date format"},"success":{"type":"boolean","example":false}},"type":"object"}}}},"403":{"description":"Access denied - Admin role required","content":{"application/json":{"schema":{"properties":{"message":{"type":"string","example":"Admin access required"},"success":{"type":"boolean","example":false}},"type":"object"}}}}}}},"/api/v1/expense-categories":{"get":{"tags":["Expense Categories"],"summary":"Get all expense categories","operationId":"get_app_expense_category_index","parameters":[{"name":"active","in":"query","description":"Filter by active status","required":false,"schema":{"type":"boolean"}},{"name":"root_only","in":"query","description":"Get only root categories (no parent)","required":false,"schema":{"type":"boolean"}}],"responses":{"200":{"description":"List of expense categories","content":{"application/json":{"schema":{"type":"array","items":{"properties":{"id":{"type":"integer","example":1},"name":{"type":"string","example":"Transportation"},"code":{"type":"string","example":"TRANSPORT"},"description":{"type":"string","example":"All transportation related expenses","nullable":true},"requiresReceipt":{"type":"boolean","example":true},"isActive":{"type":"boolean","example":true},"fullPath":{"type":"string","example":"Transportation \u003E Flight Tickets"},"isRootCategory":{"type":"boolean","example":true},"parentCategory":{"properties":{"id":{"type":"integer","example":1},"name":{"type":"string","example":"Transportation"},"code":{"type":"string","example":"TRANSPORT"}},"type":"object","nullable":true},"subcategories":{"type":"array","items":{"properties":{"id":{"type":"integer","example":2},"name":{"type":"string","example":"Flight Tickets"},"code":{"type":"string","example":"FLIGHT"}},"type":"object"}}},"type":"object"}}}}}}},"post":{"tags":["Expense Categories"],"summary":"Create a new expense category (admin only)","operationId":"post_app_expense_category_create","requestBody":{"description":"Expense category data for creating a new category. Categories can be root categories or subcategories with a parent.","required":true,"content":{"application/json":{"schema":{"required":["name","code"],"properties":{"name":{"description":"Category name","type":"string","example":"Transportation"},"code":{"description":"Unique category code (uppercase, no spaces)","type":"string","example":"TRANSPORT"},"description":{"description":"Category description","type":"string","example":"All transportation related expenses","nullable":true},"requiresReceipt":{"description":"Whether expenses in this category require receipts","type":"boolean","example":true},"parentCategoryId":{"description":"ID of parent category (null for root categories)","type":"integer","example":1,"nullable":true},"isActive":{"description":"Whether the category is active","type":"boolean","example":true}},"type":"object"}}}},"responses":{"201":{"description":"Expense category created successfully","content":{"application/json":{"schema":{"properties":{"id":{"type":"integer","example":1},"name":{"type":"string","example":"Transportation"},"code":{"type":"string","example":"TRANSPORT"},"description":{"type":"string","example":"All transportation related expenses","nullable":true},"requiresReceipt":{"type":"boolean","example":true},"isActive":{"type":"boolean","example":true},"fullPath":{"type":"string","example":"Transportation"},"isRootCategory":{"type":"boolean","example":true},"parentCategory":{"properties":{"id":{"type":"integer","example":1},"name":{"type":"string","example":"Transportation"},"code":{"type":"string","example":"TRANSPORT"}},"type":"object","nullable":true},"success":{"type":"boolean","example":true}},"type":"object"}}}},"400":{"description":"Invalid data provided"},"401":{"description":"Authentication required"},"403":{"description":"Admin access required"}}}},"/api/v1/expense-categories/{id}":{"get":{"tags":["Expense Categories"],"summary":"Get a specific expense category","operationId":"get_app_expense_category_show","parameters":[{"name":"id","in":"path","required":true,"schema":{"type":"string"}}],"responses":{"200":{"description":"Expense category details"},"404":{"description":"Category not found"}}},"put":{"tags":["Expense Categories"],"summary":"Update an expense category (admin only)","operationId":"put_app_expense_category_update","parameters":[{"name":"id","in":"path","description":"Expense category ID","required":true,"schema":{"type":"integer"}}],"requestBody":{"description":"Updated expense category data","required":true,"content":{"application/json":{"schema":{"properties":{"name":{"type":"string","example":"Transportation"},"code":{"type":"string","example":"TRANSPORT"},"description":{"type":"string","example":"All transportation related expenses","nullable":true},"requiresReceipt":{"type":"boolean","example":true},"parentCategoryId":{"type":"integer","example":1,"nullable":true},"isActive":{"type":"boolean","example":true}},"type":"object"}}}},"responses":{"200":{"description":"Category updated successfully"},"400":{"description":"Invalid data"},"404":{"description":"Category not found"},"403":{"description":"Admin access required"}}},"delete":{"tags":["Expense Categories"],"summary":"Delete an expense category (admin only)","operationId":"delete_app_expense_category_delete","parameters":[{"name":"id","in":"path","description":"Expense category ID","required":true,"schema":{"type":"integer"}}],"responses":{"200":{"description":"Category deleted successfully","content":{"application/json":{"schema":{"properties":{"id":{"type":"integer","example":1},"name":{"type":"string","example":"Transportation"},"message":{"type":"string","example":"Expense category successfully deleted"},"success":{"type":"boolean","example":true}},"type":"object"}}}},"404":{"description":"Category not found"},"400":{"description":"Cannot delete category with subcategories or expenses"},"403":{"description":"Admin access required"}}}},"/api/v1/expense-categories/{id}/subcategories":{"get":{"tags":["Expense Categories"],"summary":"Get subcategories of a parent category","operationId":"get_app_expense_category_subcategories","parameters":[{"name":"id","in":"path","required":true,"schema":{"type":"string"}}],"responses":{"200":{"description":"List of subcategories","content":{"application/json":{"schema":{"type":"array","items":{"properties":{"id":{"type":"integer","example":2},"name":{"type":"string","example":"Flight Tickets"},"code":{"type":"string","example":"FLIGHT"},"description":{"type":"string","example":"Airline tickets and fees","nullable":true},"requiresReceipt":{"type":"boolean","example":true},"isActive":{"type":"boolean","example":true}},"type":"object"}}}}},"404":{"description":"Category not found"}}}},"/api/v1/expenses":{"get":{"tags":["Expenses"],"summary":"Get all expenses (admin only)","operationId":"get_app_expense_index","parameters":[{"name":"start","in":"query","description":"Start date (format: Y-m-d)","required":false,"schema":{"type":"string","format":"date"}},{"name":"end","in":"query","description":"End date (format: Y-m-d)","required":false,"schema":{"type":"string","format":"date"}}],"responses":{"200":{"description":"Returns all expenses","content":{"application/json":{"schema":{"type":"array","items":{"properties":{"id":{"type":"integer","example":1},"date":{"type":"string","format":"date","example":"2023-06-15"},"description":{"type":"string","example":"Train ticket to client site"},"amount":{"type":"string","example":"125.50"},"currency":{"type":"string","example":"EUR"},"amountEur":{"type":"string","example":"125.50"},"exchangeRate":{"type":"string","example":"1.0000"},"receipt":{"type":"string","example":"receipts/2023/06/15/receipt-123.pdf","nullable":true},"validated":{"type":"boolean","example":false},"consultant":{"properties":{"id":{"type":"integer","example":1},"firstName":{"type":"string","example":"John"},"lastName":{"type":"string","example":"Doe"}},"type":"object"}},"type":"object"}}}}},"403":{"description":"Access denied - Admin role required"}}},"post":{"tags":["Expenses"],"summary":"Create a new expense","description":"This endpoint allows consultants to record business expenses with comprehensive categorization and trip association.\n\n## Travel Expense Workflow:\n1. **Create a Trip** (optional but recommended): Use POST /api/v1/trips to create a trip container\n2. **Add Individual Expenses**: Link each expense to the trip using tripId\n3. **Automatic Client Assignment**: If trip has a client, it's automatically assigned to the expense\n4. **Category Selection**: Choose appropriate expense category for proper reporting\n5. **Receipt Management**: Upload receipts for categories that require them\n\n## Example Travel Expenses:\n- Flight: categoryId=5 (Flight Tickets), requires receipt\n- Hotel: categoryId=8 (Hotel), requires receipt\n- Taxi: categoryId=7 (Taxi & Rideshare), requires receipt\n- Meals: categoryId=12 (Business Meals), requires receipt\n- Parking: categoryId=9 (Parking), receipt optional","operationId":"post_app_expense_create","requestBody":{"description":"Expense data for recording business expenses. Can be linked to trips and clients for better organization. When tripId is provided and the trip has a client, the client is automatically assigned to the expense.","required":true,"content":{"application/json":{"schema":{"required":["consultantId","description","amount","currency"],"properties":{"consultantId":{"description":"ID of the consultant recording the expense","type":"integer","example":1},"clientId":{"description":"ID of the client (optional, auto-assigned if trip has client)","type":"integer","example":1,"nullable":true},"tripId":{"description":"ID of the trip to associate this expense with (recommended for travel expenses)","type":"integer","example":1,"nullable":true},"categoryId":{"description":"ID of the expense category (e.g., 5=Flight, 7=Taxi, 8=Hotel, 12=Business Meals)","type":"integer","example":5,"nullable":true},"date":{"description":"Date when the expense occurred (format: Y-m-d)","type":"string","format":"date","example":"2023-06-15"},"description":{"description":"Detailed description of the expense","type":"string","example":"Round-trip flight Paris-Zurich for client meeting"},"amount":{"description":"Expense amount in the specified currency","type":"string","example":"450.00"},"currency":{"description":"Currency code (EUR, USD, CHF, etc.)","type":"string","example":"EUR"},"receipt":{"description":"Path to uploaded receipt file (required for some categories)","type":"string","example":"receipts/flight-AF1234.pdf","nullable":true}},"type":"object"}}}},"responses":{"201":{"description":"Expense created successfully","content":{"application/json":{"schema":{"properties":{"id":{"type":"integer","example":1},"date":{"type":"string","format":"date","example":"2023-06-15"},"description":{"type":"string","example":"Train ticket to client site"},"amount":{"type":"string","example":"125.50"},"currency":{"type":"string","example":"EUR"},"amountEur":{"type":"string","example":"125.50"},"exchangeRate":{"type":"string","example":"1.0000"},"receipt":{"type":"string","example":"receipts/2023/06/15/receipt-123.pdf","nullable":true},"validated":{"type":"boolean","example":false},"consultant":{"properties":{"id":{"type":"integer","example":1},"firstName":{"type":"string","example":"John"},"lastName":{"type":"string","example":"Doe"}},"type":"object"},"client":{"properties":{"id":{"type":"integer","example":1},"name":{"type":"string","example":"Acme Corporation"},"code":{"type":"string","example":"ACME"}},"type":"object","nullable":true},"trip":{"properties":{"id":{"type":"integer","example":1},"title":{"type":"string","example":"Client Meeting in Paris"},"destination":{"type":"string","example":"Paris, France"}},"type":"object","nullable":true},"category":{"properties":{"id":{"type":"integer","example":1},"name":{"type":"string","example":"Transportation"},"code":{"type":"string","example":"TRANSPORT"}},"type":"object","nullable":true}},"type":"object"}}}},"400":{"description":"Invalid input data","content":{"application/json":{"schema":{"properties":{"error":{"type":"string","example":"Description is required"},"success":{"type":"boolean","example":false}},"type":"object"}}}},"404":{"description":"Consultant not found","content":{"application/json":{"schema":{"properties":{"error":{"type":"string","example":"Consultant not found"},"message":{"type":"string","example":"The consultant with ID 999 was not found"},"success":{"type":"boolean","example":false}},"type":"object"}}}}}}},"/api/v1/expenses/non-validated":{"get":{"tags":["Expenses"],"summary":"Get all non-validated expenses (admin only)","operationId":"get_app_expense_non_validated","responses":{"200":{"description":"Returns all non-validated expenses","content":{"application/json":{"schema":{"type":"array","items":{"properties":{"id":{"type":"integer","example":1},"date":{"type":"string","format":"date","example":"2023-06-15"},"description":{"type":"string","example":"Train ticket to client site"},"amount":{"type":"string","example":"125.50"},"currency":{"type":"string","example":"EUR"},"amountEur":{"type":"string","example":"125.50"},"exchangeRate":{"type":"string","example":"1.0000"},"receipt":{"type":"string","example":"receipts/2023/06/15/receipt-123.pdf","nullable":true},"validated":{"type":"boolean","example":false},"consultant":{"properties":{"id":{"type":"integer","example":1},"firstName":{"type":"string","example":"John"},"lastName":{"type":"string","example":"Doe"}},"type":"object"}},"type":"object"}}}}},"403":{"description":"Access denied - Admin role required"}}}},"/api/v1/expenses/my":{"get":{"tags":["Expenses"],"summary":"Get all expenses for the current user","description":"This endpoint allows consultants to view their own expenses without specifying their consultant ID.\nSupports the same filtering options as the consultant-specific endpoint for detailed expense reporting.\n\n## Travel Expense Filtering Examples:\n- Get all expenses for a trip: `/my?tripId=1`\n- Get all flight expenses: `/my?categoryId=5`\n- Get expenses for a specific client: `/my?clientId=1`\n- Get expenses for a date range: `/my?start=2023-06-01&end=2023-06-30`","operationId":"get_app_expense_my","parameters":[{"name":"start","in":"query","description":"Start date filter (format: Y-m-d)","required":false,"schema":{"type":"string","format":"date"}},{"name":"end","in":"query","description":"End date filter (format: Y-m-d)","required":false,"schema":{"type":"string","format":"date"}},{"name":"tripId","in":"query","description":"Filter by trip ID","required":false,"schema":{"type":"integer"}},{"name":"clientId","in":"query","description":"Filter by client ID","required":false,"schema":{"type":"integer"}},{"name":"categoryId","in":"query","description":"Filter by expense category ID","required":false,"schema":{"type":"integer"}}],"responses":{"200":{"description":"List of current user expenses","content":{"application/json":{"schema":{"type":"array","items":{"properties":{"id":{"type":"integer","example":1},"date":{"type":"string","format":"date","example":"2023-06-15"},"description":{"type":"string","example":"Flight to Paris"},"amount":{"type":"string","example":"450.00"},"currency":{"type":"string","example":"EUR"},"amountEur":{"type":"string","example":"450.00"},"exchangeRate":{"type":"string","example":"1.0000","nullable":true},"receipt":{"type":"string","example":"receipt_123.pdf","nullable":true},"validated":{"type":"boolean","example":false}},"type":"object"}}}}},"401":{"description":"Authentication required"}}}},"/api/v1/expenses/{id}":{"get":{"tags":["Expenses"],"summary":"Get a specific expense by ID","operationId":"get_app_expense_show","parameters":[{"name":"id","in":"path","description":"Expense ID","required":true,"schema":{"type":"integer"}}],"responses":{"200":{"description":"Returns the expense details","content":{"application/json":{"schema":{"properties":{"id":{"type":"integer","example":1},"date":{"type":"string","format":"date","example":"2023-06-15"},"description":{"type":"string","example":"Train ticket to client site"},"amount":{"type":"string","example":"125.50"},"currency":{"type":"string","example":"EUR"},"amountEur":{"type":"string","example":"125.50"},"exchangeRate":{"type":"string","example":"1.0000"},"receipt":{"type":"string","example":"receipts/2023/06/15/receipt-123.pdf","nullable":true},"validated":{"type":"boolean","example":false},"consultant":{"properties":{"id":{"type":"integer","example":1},"firstName":{"type":"string","example":"John"},"lastName":{"type":"string","example":"Doe"}},"type":"object"}},"type":"object"}}}},"404":{"description":"Expense not found","content":{"application/json":{"schema":{"properties":{"error":{"type":"string","example":"Expense not found"},"message":{"type":"string","example":"The expense with ID 999 was not found"},"success":{"type":"boolean","example":false}},"type":"object"}}}}}},"put":{"tags":["Expenses"],"summary":"Update an existing expense","operationId":"put_app_expense_update","parameters":[{"name":"id","in":"path","description":"Expense ID","required":true,"schema":{"type":"integer"}}],"requestBody":{"description":"Updated expense data","required":true,"content":{"application/json":{"schema":{"properties":{"date":{"type":"string","format":"date","example":"2023-06-15"},"description":{"type":"string","example":"Train ticket to client site"},"amount":{"type":"string","example":"125.50"},"currency":{"type":"string","example":"EUR"},"receipt":{"type":"string","example":"receipts/2023/06/15/receipt-123.pdf","nullable":true}},"type":"object"}}}},"responses":{"200":{"description":"Expense updated successfully","content":{"application/json":{"schema":{"properties":{"id":{"type":"integer","example":1},"date":{"type":"string","format":"date","example":"2023-06-15"},"description":{"type":"string","example":"Train ticket to client site"},"amount":{"type":"string","example":"125.50"},"currency":{"type":"string","example":"EUR"},"amountEur":{"type":"string","example":"125.50"},"exchangeRate":{"type":"string","example":"1.0000"},"receipt":{"type":"string","example":"receipts/2023/06/15/receipt-123.pdf","nullable":true},"validated":{"type":"boolean","example":false},"consultant":{"properties":{"id":{"type":"integer","example":1},"firstName":{"type":"string","example":"John"},"lastName":{"type":"string","example":"Doe"}},"type":"object"}},"type":"object"}}}},"404":{"description":"Expense not found","content":{"application/json":{"schema":{"properties":{"error":{"type":"string","example":"Expense not found"},"message":{"type":"string","example":"The expense with ID 999 was not found"},"success":{"type":"boolean","example":false}},"type":"object"}}}},"403":{"description":"Access denied","content":{"application/json":{"schema":{"properties":{"error":{"type":"string","example":"You cannot modify a validated expense"},"success":{"type":"boolean","example":false}},"type":"object"}}}}}},"delete":{"tags":["Expenses"],"summary":"Delete an expense","operationId":"delete_app_expense_delete","parameters":[{"name":"id","in":"path","description":"Expense ID","required":true,"schema":{"type":"integer"}}],"responses":{"200":{"description":"Expense deleted successfully","content":{"application/json":{"schema":{"properties":{"id":{"type":"integer","example":1},"date":{"type":"string","format":"date","example":"2023-06-15"},"message":{"type":"string","example":"Expense successfully deleted"},"success":{"type":"boolean","example":true}},"type":"object"}}}},"404":{"description":"Expense not found","content":{"application/json":{"schema":{"properties":{"error":{"type":"string","example":"Expense not found"},"message":{"type":"string","example":"The expense with ID 999 was not found"},"success":{"type":"boolean","example":false}},"type":"object"}}}},"403":{"description":"Access denied","content":{"application/json":{"schema":{"properties":{"error":{"type":"string","example":"You cannot delete a validated expense"},"success":{"type":"boolean","example":false}},"type":"object"}}}}}}},"/api/v1/expenses/consultant/{consultantId}":{"get":{"tags":["Expenses"],"summary":"Get expenses for a specific consultant","description":"This endpoint allows consultants to view their own expenses or administrators to view any consultant's expenses.\nSupports filtering by date range, trip, client, and category for detailed expense reporting.\n\n## Travel Expense Filtering Examples:\n- Get all expenses for a trip: `/consultant/1?tripId=1`\n- Get all flight expenses: `/consultant/1?categoryId=5`\n- Get expenses for a specific client: `/consultant/1?clientId=1`\n- Get expenses for a date range: `/consultant/1?start=2023-06-01&end=2023-06-30`","operationId":"get_app_expense_by_consultant","parameters":[{"name":"consultantId","in":"path","description":"Consultant ID - consultants can only access their own expenses, admins can access any","required":true,"schema":{"type":"integer"},"example":1},{"name":"start","in":"query","description":"Start date filter (format: Y-m-d) - useful for monthly/quarterly reports","required":false,"schema":{"type":"string","format":"date"},"example":"2023-06-01"},{"name":"end","in":"query","description":"End date filter (format: Y-m-d) - useful for monthly/quarterly reports","required":false,"schema":{"type":"string","format":"date"},"example":"2023-06-30"},{"name":"tripId","in":"query","description":"Filter by specific trip ID - get all expenses for a particular business trip","required":false,"schema":{"type":"integer"},"example":1},{"name":"clientId","in":"query","description":"Filter by specific client ID - useful for client-specific expense reports","required":false,"schema":{"type":"integer"},"example":1},{"name":"categoryId","in":"query","description":"Filter by expense category - useful for viewing specific types of expenses (e.g., all flights, hotels)","required":false,"schema":{"type":"integer"},"example":5}],"responses":{"200":{"description":"Returns expenses for the consultant with full relationship information including trip, client, and category details","content":{"application/json":{"schema":{"type":"array","items":{"properties":{"id":{"type":"integer","example":1},"date":{"type":"string","format":"date","example":"2023-06-15"},"description":{"type":"string","example":"Round-trip flight Paris-Zurich"},"amount":{"type":"string","example":"450.00"},"currency":{"type":"string","example":"EUR"},"amountEur":{"type":"string","example":"450.00"},"exchangeRate":{"type":"string","example":"1.0000"},"receipt":{"type":"string","example":"receipts/flight-AF1234.pdf","nullable":true},"validated":{"type":"boolean","example":false},"consultant":{"properties":{"id":{"type":"integer","example":1},"firstName":{"type":"string","example":"John"},"lastName":{"type":"string","example":"Smith"}},"type":"object"},"client":{"properties":{"id":{"type":"integer","example":1},"name":{"type":"string","example":"Acme Corporation"},"code":{"type":"string","example":"ACME"}},"type":"object","nullable":true},"trip":{"properties":{"id":{"type":"integer","example":1},"title":{"type":"string","example":"Client Meeting in Paris"},"destination":{"type":"string","example":"Paris, France"}},"type":"object","nullable":true},"category":{"properties":{"id":{"type":"integer","example":5},"name":{"type":"string","example":"Flight Tickets"},"code":{"type":"string","example":"FLIGHT"}},"type":"object","nullable":true}},"type":"object"}}}}},"404":{"description":"Consultant not found","content":{"application/json":{"schema":{"properties":{"error":{"type":"string","example":"Consultant not found"},"message":{"type":"string","example":"The consultant with ID 999 was not found"},"success":{"type":"boolean","example":false}},"type":"object"}}}}}}},"/api/v1/expenses/{id}/validate":{"put":{"tags":["Expenses"],"summary":"Validate an expense (admin only)","operationId":"put_app_expense_validate","parameters":[{"name":"id","in":"path","description":"Expense ID","required":true,"schema":{"type":"integer"}}],"responses":{"200":{"description":"Expense validated successfully","content":{"application/json":{"schema":{"properties":{"id":{"type":"integer","example":1},"date":{"type":"string","format":"date","example":"2023-06-15"},"description":{"type":"string","example":"Train ticket to client site"},"amount":{"type":"string","example":"125.50"},"currency":{"type":"string","example":"EUR"},"amountEur":{"type":"string","example":"125.50"},"exchangeRate":{"type":"string","example":"1.0000"},"receipt":{"type":"string","example":"receipts/2023/06/15/receipt-123.pdf","nullable":true},"validated":{"type":"boolean","example":true},"consultant":{"properties":{"id":{"type":"integer","example":1},"firstName":{"type":"string","example":"John"},"lastName":{"type":"string","example":"Doe"}},"type":"object"}},"type":"object"}}}},"404":{"description":"Expense not found","content":{"application/json":{"schema":{"properties":{"error":{"type":"string","example":"Expense not found"},"message":{"type":"string","example":"The expense with ID 999 was not found"},"success":{"type":"boolean","example":false}},"type":"object"}}}},"403":{"description":"Access denied - Admin role required","content":{"application/json":{"schema":{"properties":{"error":{"type":"string","example":"Admin access required"},"success":{"type":"boolean","example":false}},"type":"object"}}}}}}},"/api/v1/expenses/total/{consultantId}":{"get":{"tags":["Expenses"],"summary":"Calculate total expenses for a consultant","operationId":"get_app_expense_total","parameters":[{"name":"consultantId","in":"path","description":"Consultant ID","required":true,"schema":{"type":"integer"}},{"name":"start","in":"query","description":"Start date (format: Y-m-d)","required":false,"schema":{"type":"string","format":"date"}},{"name":"end","in":"query","description":"End date (format: Y-m-d)","required":false,"schema":{"type":"string","format":"date"}}],"responses":{"200":{"description":"Returns the total expenses amount in EUR","content":{"application/json":{"schema":{"properties":{"total":{"type":"number","format":"float","example":1250.75}},"type":"object"}}}},"404":{"description":"Consultant not found","content":{"application/json":{"schema":{"properties":{"error":{"type":"string","example":"Consultant not found"},"message":{"type":"string","example":"The consultant with ID 999 was not found"},"success":{"type":"boolean","example":false}},"type":"object"}}}}}}},"/api/v1/leaves":{"get":{"tags":["Leaves"],"summary":"Get all leave requests (admin only)","operationId":"get_app_leave_index","responses":{"200":{"description":"Returns all leave requests","content":{"application/json":{"schema":{"type":"array","items":{"properties":{"id":{"type":"integer","example":1},"startDate":{"type":"string","format":"date","example":"2023-07-10"},"endDate":{"type":"string","format":"date","example":"2023-07-15"},"numberOfDays":{"type":"number","format":"float","example":5},"type":{"type":"string","example":"annual"},"comment":{"type":"string","example":"Summer vacation","nullable":true},"status":{"type":"string","example":"pending"},"consultant":{"properties":{"id":{"type":"integer","example":1},"firstName":{"type":"string","example":"John"},"lastName":{"type":"string","example":"Doe"}},"type":"object"}},"type":"object"}}}}},"403":{"description":"Access denied - Admin role required"}}},"post":{"tags":["Leaves"],"summary":"Create a new leave request","operationId":"post_app_leave_create","requestBody":{"description":"Leave request data","required":true,"content":{"application/json":{"schema":{"required":["consultantId","startDate","endDate","numberOfDays","type"],"properties":{"consultantId":{"type":"integer","example":1},"startDate":{"type":"string","format":"date","example":"2023-07-10"},"endDate":{"type":"string","format":"date","example":"2023-07-15"},"numberOfDays":{"type":"number","format":"float","example":5},"type":{"type":"string","example":"annual"},"comment":{"type":"string","example":"Summer vacation","nullable":true}},"type":"object"}}}},"responses":{"201":{"description":"Leave request created successfully","content":{"application/json":{"schema":{"properties":{"id":{"type":"integer","example":1},"startDate":{"type":"string","format":"date","example":"2023-07-10"},"endDate":{"type":"string","format":"date","example":"2023-07-15"},"numberOfDays":{"type":"number","format":"float","example":5},"type":{"type":"string","example":"annual"},"comment":{"type":"string","example":"Summer vacation","nullable":true},"status":{"type":"string","example":"pending"},"consultant":{"properties":{"id":{"type":"integer","example":1},"firstName":{"type":"string","example":"John"},"lastName":{"type":"string","example":"Doe"}},"type":"object"}},"type":"object"}}}},"400":{"description":"Invalid input data","content":{"application/json":{"schema":{"properties":{"error":{"type":"string","example":"Start date and end date are required"},"success":{"type":"boolean","example":false}},"type":"object"}}}},"404":{"description":"Consultant not found","content":{"application/json":{"schema":{"properties":{"error":{"type":"string","example":"Consultant not found"},"message":{"type":"string","example":"The consultant with ID 999 was not found"},"success":{"type":"boolean","example":false}},"type":"object"}}}},"403":{"description":"Access denied","content":{"application/json":{"schema":{"properties":{"error":{"type":"string","example":"You can only access your own data"},"success":{"type":"boolean","example":false}},"type":"object"}}}}}}},"/api/v1/leaves/pending":{"get":{"tags":["Leaves"],"summary":"Get all pending leave requests (admin only)","operationId":"get_app_leave_pending","responses":{"200":{"description":"Returns all pending leave requests","content":{"application/json":{"schema":{"type":"array","items":{"properties":{"id":{"type":"integer","example":1},"startDate":{"type":"string","format":"date","example":"2023-07-10"},"endDate":{"type":"string","format":"date","example":"2023-07-15"},"numberOfDays":{"type":"number","format":"float","example":5},"type":{"type":"string","example":"annual"},"comment":{"type":"string","example":"Summer vacation","nullable":true},"status":{"type":"string","example":"pending"},"consultant":{"properties":{"id":{"type":"integer","example":1},"firstName":{"type":"string","example":"John"},"lastName":{"type":"string","example":"Doe"}},"type":"object"}},"type":"object"}}}}},"403":{"description":"Access denied - Admin role required"}}}},"/api/v1/leaves/{id}":{"get":{"tags":["Leaves"],"summary":"Get a specific leave request by ID","operationId":"get_app_leave_show","parameters":[{"name":"id","in":"path","description":"Leave request ID","required":true,"schema":{"type":"integer"}}],"responses":{"200":{"description":"Returns the leave request details","content":{"application/json":{"schema":{"properties":{"id":{"type":"integer","example":1},"startDate":{"type":"string","format":"date","example":"2023-07-10"},"endDate":{"type":"string","format":"date","example":"2023-07-15"},"numberOfDays":{"type":"number","format":"float","example":5},"type":{"type":"string","example":"annual"},"comment":{"type":"string","example":"Summer vacation","nullable":true},"status":{"type":"string","example":"pending"},"consultant":{"properties":{"id":{"type":"integer","example":1},"firstName":{"type":"string","example":"John"},"lastName":{"type":"string","example":"Doe"}},"type":"object"}},"type":"object"}}}},"404":{"description":"Leave request not found","content":{"application/json":{"schema":{"properties":{"error":{"type":"string","example":"Leave request not found"},"message":{"type":"string","example":"The leave request with ID 999 was not found"},"success":{"type":"boolean","example":false}},"type":"object"}}}}}},"put":{"tags":["Leaves"],"summary":"Update an existing leave request","operationId":"put_app_leave_update","parameters":[{"name":"id","in":"path","description":"Leave request ID","required":true,"schema":{"type":"integer"}}],"requestBody":{"description":"Updated leave request data","required":true,"content":{"application/json":{"schema":{"properties":{"startDate":{"type":"string","format":"date","example":"2023-07-10"},"endDate":{"type":"string","format":"date","example":"2023-07-15"},"numberOfDays":{"type":"number","format":"float","example":5},"type":{"type":"string","example":"annual"},"comment":{"type":"string","example":"Summer vacation","nullable":true}},"type":"object"}}}},"responses":{"200":{"description":"Leave request updated successfully","content":{"application/json":{"schema":{"properties":{"id":{"type":"integer","example":1},"startDate":{"type":"string","format":"date","example":"2023-07-10"},"endDate":{"type":"string","format":"date","example":"2023-07-15"},"numberOfDays":{"type":"number","format":"float","example":5},"type":{"type":"string","example":"annual"},"comment":{"type":"string","example":"Summer vacation","nullable":true},"status":{"type":"string","example":"pending"},"consultant":{"properties":{"id":{"type":"integer","example":1},"firstName":{"type":"string","example":"John"},"lastName":{"type":"string","example":"Doe"}},"type":"object"}},"type":"object"}}}},"400":{"description":"Invalid input data","content":{"application/json":{"schema":{"properties":{"error":{"type":"string","example":"Not enough leave days available"},"success":{"type":"boolean","example":false}},"type":"object"}}}},"404":{"description":"Leave request not found","content":{"application/json":{"schema":{"properties":{"error":{"type":"string","example":"Leave request not found"},"message":{"type":"string","example":"The leave request with ID 999 was not found"},"success":{"type":"boolean","example":false}},"type":"object"}}}},"403":{"description":"Access denied","content":{"application/json":{"schema":{"properties":{"error":{"type":"string","example":"You cannot modify a leave that is not pending"},"success":{"type":"boolean","example":false}},"type":"object"}}}}}},"delete":{"tags":["Leaves"],"summary":"Delete a leave request","operationId":"delete_app_leave_delete","parameters":[{"name":"id","in":"path","description":"Leave request ID","required":true,"schema":{"type":"integer"}}],"responses":{"200":{"description":"Leave request deleted successfully","content":{"application/json":{"schema":{"properties":{"id":{"type":"integer","example":1},"startDate":{"type":"string","format":"date","example":"2023-07-10"},"message":{"type":"string","example":"Leave request successfully deleted"},"success":{"type":"boolean","example":true}},"type":"object"}}}},"404":{"description":"Leave request not found","content":{"application/json":{"schema":{"properties":{"error":{"type":"string","example":"Leave request not found"},"message":{"type":"string","example":"The leave request with ID 999 was not found"},"success":{"type":"boolean","example":false}},"type":"object"}}}},"403":{"description":"Access denied","content":{"application/json":{"schema":{"properties":{"error":{"type":"string","example":"You cannot delete a leave that is not pending"},"success":{"type":"boolean","example":false}},"type":"object"}}}}}}},"/api/v1/leaves/consultant/{consultantId}":{"get":{"tags":["Leaves"],"summary":"Get leave requests for a specific consultant","operationId":"get_app_leave_by_consultant","parameters":[{"name":"consultantId","in":"path","description":"Consultant ID","required":true,"schema":{"type":"integer"}},{"name":"start","in":"query","description":"Start date (format: Y-m-d)","required":false,"schema":{"type":"string","format":"date"}},{"name":"end","in":"query","description":"End date (format: Y-m-d)","required":false,"schema":{"type":"string","format":"date"}}],"responses":{"200":{"description":"Returns leave requests for the consultant","content":{"application/json":{"schema":{"type":"array","items":{"properties":{"id":{"type":"integer","example":1},"startDate":{"type":"string","format":"date","example":"2023-07-10"},"endDate":{"type":"string","format":"date","example":"2023-07-15"},"numberOfDays":{"type":"number","format":"float","example":5},"type":{"type":"string","example":"annual"},"comment":{"type":"string","example":"Summer vacation","nullable":true},"status":{"type":"string","example":"pending"}},"type":"object"}}}}},"404":{"description":"Consultant not found","content":{"application/json":{"schema":{"properties":{"error":{"type":"string","example":"Consultant not found"},"message":{"type":"string","example":"The consultant with ID 999 was not found"},"success":{"type":"boolean","example":false}},"type":"object"}}}},"403":{"description":"Access denied","content":{"application/json":{"schema":{"properties":{"error":{"type":"string","example":"You can only access your own data"},"success":{"type":"boolean","example":false}},"type":"object"}}}}}}},"/api/v1/leaves/{id}/approve":{"put":{"tags":["Leaves"],"summary":"Approve a leave request (admin only)","operationId":"put_app_leave_approve","parameters":[{"name":"id","in":"path","description":"Leave request ID","required":true,"schema":{"type":"integer"}}],"responses":{"200":{"description":"Leave request approved successfully","content":{"application/json":{"schema":{"properties":{"id":{"type":"integer","example":1},"startDate":{"type":"string","format":"date","example":"2023-07-10"},"endDate":{"type":"string","format":"date","example":"2023-07-15"},"numberOfDays":{"type":"number","format":"float","example":5},"type":{"type":"string","example":"annual"},"comment":{"type":"string","example":"Summer vacation","nullable":true},"status":{"type":"string","example":"approved"},"consultant":{"properties":{"id":{"type":"integer","example":1},"firstName":{"type":"string","example":"John"},"lastName":{"type":"string","example":"Doe"}},"type":"object"}},"type":"object"}}}},"404":{"description":"Leave request not found","content":{"application/json":{"schema":{"properties":{"error":{"type":"string","example":"Leave request not found"},"message":{"type":"string","example":"The leave request with ID 999 was not found"},"success":{"type":"boolean","example":false}},"type":"object"}}}},"400":{"description":"Invalid operation","content":{"application/json":{"schema":{"properties":{"error":{"type":"string","example":"Only pending leaves can be approved"},"success":{"type":"boolean","example":false}},"type":"object"}}}},"403":{"description":"Access denied - Admin role required"}}}},"/api/v1/leaves/{id}/reject":{"put":{"tags":["Leaves"],"summary":"Reject a leave request (admin only)","operationId":"put_app_leave_reject","parameters":[{"name":"id","in":"path","description":"Leave request ID","required":true,"schema":{"type":"integer"}}],"requestBody":{"description":"Rejection reason","required":false,"content":{"application/json":{"schema":{"properties":{"reason":{"type":"string","example":"Resource constraints"}},"type":"object"}}}},"responses":{"200":{"description":"Leave request rejected successfully","content":{"application/json":{"schema":{"properties":{"id":{"type":"integer","example":1},"startDate":{"type":"string","format":"date","example":"2023-07-10"},"endDate":{"type":"string","format":"date","example":"2023-07-15"},"numberOfDays":{"type":"number","format":"float","example":5},"type":{"type":"string","example":"annual"},"comment":{"type":"string","example":"Summer vacation\\n\\nRejection reason: Resource constraints","nullable":true},"status":{"type":"string","example":"rejected"},"consultant":{"properties":{"id":{"type":"integer","example":1},"firstName":{"type":"string","example":"John"},"lastName":{"type":"string","example":"Doe"}},"type":"object"}},"type":"object"}}}},"404":{"description":"Leave request not found","content":{"application/json":{"schema":{"properties":{"error":{"type":"string","example":"Leave request not found"},"message":{"type":"string","example":"The leave request with ID 999 was not found"},"success":{"type":"boolean","example":false}},"type":"object"}}}},"400":{"description":"Invalid operation","content":{"application/json":{"schema":{"properties":{"error":{"type":"string","example":"Only pending leaves can be rejected"},"success":{"type":"boolean","example":false}},"type":"object"}}}},"403":{"description":"Access denied - Admin role required"}}}},"/api/v1/leaves/balance/{consultantId}":{"get":{"tags":["Leaves"],"summary":"Calculate the remaining leave balance for a consultant","operationId":"get_app_leave_balance","parameters":[{"name":"consultantId","in":"path","description":"Consultant ID","required":true,"schema":{"type":"integer"}}],"responses":{"200":{"description":"Returns the remaining leave balance","content":{"application/json":{"schema":{"properties":{"balance":{"type":"number","format":"float","example":15.5}},"type":"object"}}}},"404":{"description":"Consultant not found","content":{"application/json":{"schema":{"properties":{"error":{"type":"string","example":"Consultant not found"},"message":{"type":"string","example":"The consultant with ID 999 was not found"},"success":{"type":"boolean","example":false}},"type":"object"}}}},"403":{"description":"Access denied","content":{"application/json":{"schema":{"properties":{"error":{"type":"string","example":"You can only access your own data"},"success":{"type":"boolean","example":false}},"type":"object"}}}}}}},"/api/v1/trips":{"get":{"tags":["Trips"],"summary":"Get all trips (admin) or consultant's trips","operationId":"get_app_trip_index","parameters":[{"name":"start","in":"query","description":"Start date filter (format: Y-m-d)","required":false,"schema":{"type":"string","format":"date"}},{"name":"end","in":"query","description":"End date filter (format: Y-m-d)","required":false,"schema":{"type":"string","format":"date"}}],"responses":{"200":{"description":"List of trips","content":{"application/json":{"schema":{"type":"array","items":{"properties":{"id":{"type":"integer","example":1},"title":{"type":"string","example":"Client Meeting in Paris"},"destination":{"type":"string","example":"Paris, France","nullable":true},"purpose":{"type":"string","example":"Client consultation","nullable":true},"startDate":{"type":"string","format":"date","example":"2023-06-15"},"endDate":{"type":"string","format":"date","example":"2023-06-17"},"notes":{"type":"string","example":"Important project kickoff","nullable":true},"totalExpenses":{"type":"number","format":"float","example":1250.5},"durationInDays":{"type":"integer","example":3},"consultant":{"properties":{"id":{"type":"integer","example":1},"firstName":{"type":"string","example":"John"},"lastName":{"type":"string","example":"Doe"}},"type":"object"},"client":{"properties":{"id":{"type":"integer","example":1},"name":{"type":"string","example":"Acme Corporation"},"code":{"type":"string","example":"ACME"}},"type":"object","nullable":true}},"type":"object"}}}}}}},"post":{"tags":["Trips"],"summary":"Create a new trip","operationId":"post_app_trip_create","requestBody":{"description":"Trip data for creating a new business trip. This endpoint allows consultants to create trips that can group multiple related expenses together for better organization and reporting.","required":true,"content":{"application/json":{"schema":{"required":["title","startDate","endDate"],"properties":{"title":{"description":"Descriptive title for the trip","type":"string","example":"Client Meeting in Paris"},"destination":{"description":"Trip destination city/country","type":"string","example":"Paris, France","nullable":true},"purpose":{"description":"Business purpose of the trip","type":"string","example":"Quarterly business review with client","nullable":true},"startDate":{"description":"Trip start date (format: Y-m-d)","type":"string","format":"date","example":"2023-06-15"},"endDate":{"description":"Trip end date (format: Y-m-d)","type":"string","format":"date","example":"2023-06-17"},"clientId":{"description":"ID of the client associated with this trip (optional)","type":"integer","example":1,"nullable":true},"notes":{"description":"Additional notes about the trip","type":"string","example":"Important Q2 review meeting with key stakeholders","nullable":true}},"type":"object"}}}},"responses":{"201":{"description":"Trip created successfully. The response includes the complete trip information with calculated fields like duration and total expenses (initially 0).","content":{"application/json":{"schema":{"properties":{"id":{"type":"integer","example":1},"title":{"type":"string","example":"Client Meeting in Paris"},"destination":{"type":"string","example":"Paris, France","nullable":true},"purpose":{"type":"string","example":"Quarterly business review with client","nullable":true},"startDate":{"type":"string","format":"date","example":"2023-06-15"},"endDate":{"type":"string","format":"date","example":"2023-06-17"},"notes":{"type":"string","example":"Important Q2 review meeting","nullable":true},"totalExpenses":{"type":"number","format":"float","example":0},"durationInDays":{"type":"integer","example":3},"consultant":{"properties":{"id":{"type":"integer","example":1},"firstName":{"type":"string","example":"John"},"lastName":{"type":"string","example":"Smith"}},"type":"object"},"client":{"properties":{"id":{"type":"integer","example":1},"name":{"type":"string","example":"Acme Corporation"},"code":{"type":"string","example":"ACME"}},"type":"object","nullable":true},"success":{"type":"boolean","example":true}},"type":"object"}}}},"400":{"description":"Invalid data provided"},"401":{"description":"Authentication required"},"403":{"description":"Access denied"}}}},"/api/v1/trips/{id}":{"get":{"tags":["Trips"],"summary":"Get a specific trip","operationId":"get_app_trip_show","parameters":[{"name":"id","in":"path","required":true,"schema":{"type":"string"}}],"responses":{"200":{"description":"Trip details"},"404":{"description":"Trip not found"},"403":{"description":"Access denied"}}},"put":{"tags":["Trips"],"summary":"Update a trip","operationId":"put_app_trip_update","parameters":[{"name":"id","in":"path","description":"Trip ID","required":true,"schema":{"type":"integer"}}],"requestBody":{"description":"Trip data for updating an existing business trip. Consultants can only update their own trips, while administrators can update any trip.","required":true,"content":{"application/json":{"schema":{"properties":{"title":{"description":"Descriptive title for the trip","type":"string","example":"Updated Client Meeting in Paris"},"destination":{"description":"Trip destination city/country","type":"string","example":"Paris, France","nullable":true},"purpose":{"description":"Business purpose of the trip","type":"string","example":"Updated quarterly business review with client","nullable":true},"startDate":{"description":"Trip start date (format: Y-m-d)","type":"string","format":"date","example":"2023-06-15"},"endDate":{"description":"Trip end date (format: Y-m-d)","type":"string","format":"date","example":"2023-06-17"},"clientId":{"description":"ID of the client associated with this trip (optional)","type":"integer","example":1,"nullable":true},"notes":{"description":"Additional notes or comments about the trip","type":"string","example":"Updated notes about the trip","nullable":true}},"type":"object"}}}},"responses":{"200":{"description":"Trip updated successfully","content":{"application/json":{"schema":{"properties":{"id":{"type":"integer","example":1},"title":{"type":"string","example":"Updated Client Meeting in Paris"},"destination":{"type":"string","example":"Paris, France","nullable":true},"purpose":{"type":"string","example":"Updated quarterly business review","nullable":true},"startDate":{"type":"string","format":"date","example":"2023-06-15"},"endDate":{"type":"string","format":"date","example":"2023-06-17"},"notes":{"type":"string","example":"Updated notes","nullable":true},"totalExpenses":{"type":"string","example":"0.00"},"durationInDays":{"type":"integer","example":3},"consultant":{"properties":{"id":{"type":"integer","example":1},"firstName":{"type":"string","example":"John"},"lastName":{"type":"string","example":"Doe"}},"type":"object"},"client":{"properties":{"id":{"type":"integer","example":1},"name":{"type":"string","example":"Acme Corporation"},"code":{"type":"string","example":"ACME"}},"type":"object","nullable":true},"success":{"type":"boolean","example":true}},"type":"object"}}}},"400":{"description":"Invalid data provided"},"401":{"description":"Authentication required"},"403":{"description":"Access denied - can only update own trips"},"404":{"description":"Trip not found"}}},"delete":{"tags":["Trips"],"summary":"Delete a trip","operationId":"delete_app_trip_delete","parameters":[{"name":"id","in":"path","description":"Trip ID","required":true,"schema":{"type":"integer"}}],"responses":{"200":{"description":"Trip deleted successfully","content":{"application/json":{"schema":{"properties":{"id":{"type":"integer","example":1},"title":{"type":"string","example":"Client Meeting in Paris"},"message":{"type":"string","example":"Trip successfully deleted"},"success":{"type":"boolean","example":true}},"type":"object"}}}},"404":{"description":"Trip not found","content":{"application/json":{"schema":{"properties":{"error":{"type":"string","example":"Trip not found"},"message":{"type":"string","example":"The trip with ID 999 was not found"},"success":{"type":"boolean","example":false}},"type":"object"}}}},"403":{"description":"Access denied","content":{"application/json":{"schema":{"properties":{"error":{"type":"string","example":"You can only delete your own trips"},"success":{"type":"boolean","example":false}},"type":"object"}}}},"400":{"description":"Cannot delete trip with expenses","content":{"application/json":{"schema":{"properties":{"error":{"type":"string","example":"Cannot delete trip with associated expenses"},"message":{"type":"string","example":"Please delete or reassign expenses first"},"success":{"type":"boolean","example":false}},"type":"object"}}}}}}},"/api/v1/trips/my":{"get":{"tags":["Trips"],"summary":"Get all trips for the current user","description":"This endpoint allows consultants to view their own trips without specifying their consultant ID.\nSupports the same filtering options as the main trips endpoint.","operationId":"get_app_trip_my","parameters":[{"name":"start","in":"query","description":"Start date filter (format: Y-m-d)","required":false,"schema":{"type":"string","format":"date"}},{"name":"end","in":"query","description":"End date filter (format: Y-m-d)","required":false,"schema":{"type":"string","format":"date"}}],"responses":{"200":{"description":"List of current user trips","content":{"application/json":{"schema":{"type":"array","items":{"properties":{"id":{"type":"integer","example":1},"title":{"type":"string","example":"Client Meeting in Paris"},"destination":{"type":"string","example":"Paris, France","nullable":true},"purpose":{"type":"string","example":"Client consultation","nullable":true},"startDate":{"type":"string","format":"date","example":"2023-06-15"},"endDate":{"type":"string","format":"date","example":"2023-06-17"},"notes":{"type":"string","example":"Important project kickoff","nullable":true},"totalExpenses":{"type":"number","format":"float","example":1250.5},"durationInDays":{"type":"integer","example":3},"consultant":{"properties":{"id":{"type":"integer","example":1},"firstName":{"type":"string","example":"John"},"lastName":{"type":"string","example":"Doe"}},"type":"object"},"client":{"properties":{"id":{"type":"integer","example":1},"name":{"type":"string","example":"Acme Corporation"},"code":{"type":"string","example":"ACME"}},"type":"object","nullable":true}},"type":"object"}}}}},"401":{"description":"Authentication required"}}}},"/api/v1/work-time":{"get":{"tags":["Work Time"],"summary":"Get all work time entries (admin only)","operationId":"get_app_work_time_index","parameters":[{"name":"start","in":"query","description":"Start date (format: Y-m-d)","required":false,"schema":{"type":"string","format":"date"}},{"name":"end","in":"query","description":"End date (format: Y-m-d)","required":false,"schema":{"type":"string","format":"date"}}],"responses":{"200":{"description":"Returns all work time entries","content":{"application/json":{"schema":{"type":"array","items":{"properties":{"id":{"type":"integer","example":1},"startDate":{"type":"string","format":"date","example":"2024-01-01"},"endDate":{"type":"string","format":"date","example":"2024-01-31"},"totalHours":{"type":"number","format":"float","example":168},"remoteWorkPercentage":{"type":"number","format":"float","example":40},"notes":{"type":"string","example":"January 2024 work period"},"isValidated":{"type":"boolean","example":true},"validationStatus":{"type":"string","example":"Validated"},"periodIdentifier":{"type":"string","example":"2024-01-01 to 2024-01-31"},"durationInDays":{"type":"integer","example":31},"activities":{"type":"array","items":{"properties":{"id":{"type":"integer","example":1},"activityName":{"type":"string","example":"Backend Development"},"hours":{"type":"number","format":"float","example":120},"description":{"type":"string","example":"API development"},"isBillable":{"type":"boolean","example":true},"percentageOfTotal":{"type":"number","format":"float","example":71.4}},"type":"object"}},"consultant":{"properties":{"id":{"type":"integer","example":1},"firstName":{"type":"string","example":"Marie"},"lastName":{"type":"string","example":"Dubois"}},"type":"object"}},"type":"object"}}}}},"403":{"description":"Access denied - Admin role required"}}},"post":{"tags":["Work Time"],"summary":"Create a new work time entry with period and activities","operationId":"post_app_work_time_create","requestBody":{"description":"Work time data with period and activities","required":true,"content":{"application/json":{"schema":{"required":["startDate","endDate","totalHours"],"properties":{"consultantId":{"description":"Optional: Only for admins. If not provided, current user's ID will be used","type":"integer","example":1},"startDate":{"description":"Start date of the work period","type":"string","format":"date","example":"2024-01-01"},"endDate":{"description":"End date of the work period","type":"string","format":"date","example":"2024-01-31"},"totalHours":{"description":"Total hours worked during the period","type":"number","format":"float","example":168},"remoteWorkPercentage":{"description":"Percentage of remote work (0-100)","type":"number","format":"float","example":40},"notes":{"description":"Additional notes about the work period","type":"string","example":"January 2024 work period - focused on API development and client meetings"},"activities":{"type":"array","items":{"properties":{"activityName":{"description":"Name of the activity","type":"string","example":"Backend Development"},"hours":{"description":"Hours spent on this activity","type":"number","format":"float","example":120},"description":{"description":"Detailed description of the activity","type":"string","example":"REST API development and database optimization"},"isBillable":{"description":"Whether this activity is billable to a client","type":"boolean","example":true},"clientId":{"description":"ID of the client this activity is for (optional)","type":"integer","example":1,"nullable":true}},"type":"object"}}},"type":"object"}}}},"responses":{"201":{"description":"Work time entry created successfully","content":{"application/json":{"schema":{"properties":{"id":{"type":"integer","example":1},"startDate":{"type":"string","format":"date","example":"2024-01-01"},"endDate":{"type":"string","format":"date","example":"2024-01-31"},"totalHours":{"type":"number","format":"float","example":168},"remoteWorkPercentage":{"type":"number","format":"float","example":40},"notes":{"type":"string","example":"January 2024 work period - focused on API development and client meetings"},"isValidated":{"description":"Auto-validated if company setting allows","type":"boolean","example":true},"validationStatus":{"type":"string","example":"Validated"},"periodIdentifier":{"type":"string","example":"2024-01-01 to 2024-01-31"},"durationInDays":{"type":"integer","example":31},"activities":{"type":"array","items":{"properties":{"id":{"type":"integer","example":1},"activityName":{"type":"string","example":"Backend Development"},"hours":{"type":"number","format":"float","example":120},"description":{"type":"string","example":"REST API development and database optimization"},"isBillable":{"type":"boolean","example":true},"percentageOfTotal":{"type":"number","format":"float","example":71.4},"client":{"properties":{"id":{"type":"integer","example":1},"name":{"type":"string","example":"TechCorp Solutions"},"code":{"type":"string","example":"TECH"}},"type":"object","nullable":true}},"type":"object"}},"consultant":{"properties":{"id":{"type":"integer","example":1},"firstName":{"type":"string","example":"Marie"},"lastName":{"type":"string","example":"Dubois"}},"type":"object"}},"type":"object"}}}},"400":{"description":"Invalid input data","content":{"application/json":{"schema":{"properties":{"error":{"type":"string","example":"Start date cannot be after end date"},"success":{"type":"boolean","example":false}},"type":"object"}}}},"403":{"description":"Access denied","content":{"application/json":{"schema":{"properties":{"error":{"type":"string","example":"You must be logged in as a consultant to create work time entries"}},"type":"object"}}}}}}},"/api/v1/work-time/{id}":{"get":{"tags":["Work Time"],"summary":"Get a specific work time entry","operationId":"get_app_work_time_show","parameters":[{"name":"id","in":"path","description":"Work time entry ID","required":true,"schema":{"type":"integer"}}],"responses":{"200":{"description":"Returns the work time entry","content":{"application/json":{"schema":{"properties":{"id":{"type":"integer","example":1},"startDate":{"type":"string","format":"date","example":"2024-01-01"},"endDate":{"type":"string","format":"date","example":"2024-01-31"},"totalHours":{"type":"number","format":"float","example":168},"remoteWorkPercentage":{"type":"number","format":"float","example":40},"notes":{"type":"string","example":"January 2024 work period - focused on API development and client meetings"},"isValidated":{"type":"boolean","example":true},"validationStatus":{"type":"string","example":"Validated"},"periodIdentifier":{"type":"string","example":"2024-01-01 to 2024-01-31"},"durationInDays":{"type":"integer","example":31},"activities":{"type":"array","items":{"properties":{"id":{"type":"integer","example":1},"activityName":{"type":"string","example":"Backend Development"},"hours":{"type":"number","format":"float","example":120},"description":{"type":"string","example":"REST API development and database optimization"},"isBillable":{"type":"boolean","example":true},"percentageOfTotal":{"type":"number","format":"float","example":71.4},"client":{"properties":{"id":{"type":"integer","example":1},"name":{"type":"string","example":"TechCorp Solutions"},"code":{"type":"string","example":"TECH"}},"type":"object","nullable":true}},"type":"object"}},"consultant":{"properties":{"id":{"type":"integer","example":1},"firstName":{"type":"string","example":"Marie"},"lastName":{"type":"string","example":"Dubois"}},"type":"object"}},"type":"object"}}}},"404":{"description":"Work time entry not found","content":{"application/json":{"schema":{"properties":{"error":{"type":"string","example":"Work time entry not found"}},"type":"object"}}}}}},"put":{"tags":["Work Time"],"summary":"Update an existing work time entry","operationId":"put_app_work_time_update","parameters":[{"name":"id","in":"path","description":"Work time entry ID","required":true,"schema":{"type":"integer"}}],"requestBody":{"description":"Updated work time data with period and activities","required":true,"content":{"application/json":{"schema":{"properties":{"startDate":{"description":"Updated start date","type":"string","format":"date","example":"2024-01-01"},"endDate":{"description":"Updated end date","type":"string","format":"date","example":"2024-01-31"},"totalHours":{"description":"Updated total hours","type":"number","format":"float","example":175},"remoteWorkPercentage":{"description":"Updated remote work percentage","type":"number","format":"float","example":45},"notes":{"description":"Updated notes","type":"string","example":"Updated notes: January work completed with additional overtime"},"activities":{"description":"Updated activities list","type":"array","items":{"properties":{"activityName":{"type":"string","example":"Frontend Development"},"hours":{"type":"number","format":"float","example":100},"description":{"type":"string","example":"React components and UI improvements"},"isBillable":{"type":"boolean","example":true},"clientId":{"type":"integer","example":2,"nullable":true}},"type":"object"}}},"type":"object"}}}},"responses":{"200":{"description":"Work time entry updated successfully","content":{"application/json":{"schema":{"properties":{"id":{"type":"integer","example":1},"startDate":{"type":"string","format":"date","example":"2024-01-01"},"endDate":{"type":"string","format":"date","example":"2024-01-31"},"totalHours":{"type":"number","format":"float","example":175},"remoteWorkPercentage":{"type":"number","format":"float","example":45},"notes":{"type":"string","example":"Updated notes: January work completed with additional overtime"},"isValidated":{"type":"boolean","example":true},"validationStatus":{"type":"string","example":"Validated"},"periodIdentifier":{"type":"string","example":"2024-01-01 to 2024-01-31"},"durationInDays":{"type":"integer","example":31},"activities":{"type":"array","items":{"properties":{"id":{"type":"integer","example":2},"activityName":{"type":"string","example":"Frontend Development"},"hours":{"type":"number","format":"float","example":100},"description":{"type":"string","example":"React components and UI improvements"},"isBillable":{"type":"boolean","example":true},"percentageOfTotal":{"type":"number","format":"float","example":57.1},"client":{"properties":{"id":{"type":"integer","example":2},"name":{"type":"string","example":"Digital Innovations Ltd"},"code":{"type":"string","example":"DIGI"}},"type":"object","nullable":true}},"type":"object"}},"consultant":{"properties":{"id":{"type":"integer","example":1},"firstName":{"type":"string","example":"Marie"},"lastName":{"type":"string","example":"Dubois"}},"type":"object"}},"type":"object"}}}},"404":{"description":"Work time entry not found","content":{"application/json":{"schema":{"properties":{"error":{"type":"string","example":"Work time entry not found"}},"type":"object"}}}}}},"delete":{"tags":["Work Time"],"summary":"Delete a work time entry","operationId":"delete_app_work_time_delete","parameters":[{"name":"id","in":"path","description":"Work time entry ID","required":true,"schema":{"type":"integer"}}],"responses":{"200":{"description":"Work time entry deleted successfully","content":{"application/json":{"schema":{"properties":{"id":{"type":"integer","example":1},"periodIdentifier":{"type":"string","example":"2024-01-01 to 2024-01-31"},"message":{"type":"string","example":"Work time entry successfully deleted"},"success":{"type":"boolean","example":true}},"type":"object"}}}},"404":{"description":"Work time entry not found","content":{"application/json":{"schema":{"properties":{"error":{"type":"string","example":"Work time entry not found"}},"type":"object"}}}}}}},"/api/v1/work-time/consultant/{consultantId}":{"get":{"tags":["Work Time"],"summary":"Get work time entries for a specific consultant","operationId":"get_app_work_time_by_consultant","parameters":[{"name":"consultantId","in":"path","description":"Consultant ID","required":true,"schema":{"type":"integer"}},{"name":"start","in":"query","description":"Start date (format: Y-m-d)","required":false,"schema":{"type":"string","format":"date"}},{"name":"end","in":"query","description":"End date (format: Y-m-d)","required":false,"schema":{"type":"string","format":"date"}}],"responses":{"200":{"description":"Returns work time entries for the consultant","content":{"application/json":{"schema":{"type":"array","items":{"properties":{"id":{"type":"integer","example":1},"startDate":{"type":"string","format":"date","example":"2024-01-01"},"endDate":{"type":"string","format":"date","example":"2024-01-31"},"totalHours":{"type":"number","format":"float","example":168},"remoteWorkPercentage":{"type":"number","format":"float","example":40},"notes":{"type":"string","example":"January 2024 work period"},"isValidated":{"type":"boolean","example":true},"validationStatus":{"type":"string","example":"Validated"},"periodIdentifier":{"type":"string","example":"2024-01-01 to 2024-01-31"},"durationInDays":{"type":"integer","example":31},"activities":{"type":"array","items":{"properties":{"id":{"type":"integer","example":1},"activityName":{"type":"string","example":"Backend Development"},"hours":{"type":"number","format":"float","example":120},"description":{"type":"string","example":"API development"},"isBillable":{"type":"boolean","example":true},"percentageOfTotal":{"type":"number","format":"float","example":71.4}},"type":"object"}},"consultant":{"properties":{"id":{"type":"integer","example":1},"firstName":{"type":"string","example":"Marie"},"lastName":{"type":"string","example":"Dubois"}},"type":"object"}},"type":"object"}}}}},"404":{"description":"Consultant not found","content":{"application/json":{"schema":{"properties":{"error":{"type":"string","example":"Consultant not found"}},"type":"object"}}}}}}},"/api/v1/work-time/statistics/{consultantId}":{"get":{"tags":["Work Time"],"summary":"Get work time statistics for a consultant","operationId":"get_app_work_time_statistics","parameters":[{"name":"consultantId","in":"path","description":"Consultant ID","required":true,"schema":{"type":"integer"}},{"name":"start","in":"query","description":"Start date (format: Y-m-d)","required":false,"schema":{"type":"string","format":"date"}},{"name":"end","in":"query","description":"End date (format: Y-m-d)","required":false,"schema":{"type":"string","format":"date"}}],"responses":{"200":{"description":"Returns work time statistics","content":{"application/json":{"schema":{"properties":{"totalHours":{"description":"Total hours worked in the period","type":"number","format":"float","example":343},"remoteHours":{"description":"Hours worked remotely","type":"number","format":"float","example":137.2},"remotePercentage":{"description":"Percentage of remote work","type":"number","format":"float","example":40},"averageHoursPerDay":{"description":"Average hours per working day","type":"number","format":"float","example":7.8},"daysWorked":{"description":"Number of working days","type":"integer","example":44},"periodsCount":{"description":"Number of work time periods","type":"integer","example":2},"activitiesBreakdown":{"description":"Breakdown of hours by activity type","type":"object","example":{"Backend Development":200,"Frontend Development":100,"Meetings":30,"Documentation":13}}},"type":"object"}}}},"404":{"description":"Consultant not found","content":{"application/json":{"schema":{"properties":{"error":{"type":"string","example":"Consultant not found"}},"type":"object"}}}}}}},"/api/v1/work-time/{id}/validate":{"post":{"tags":["Work Time"],"summary":"Validate a work time entry (admin only)","operationId":"post_app_work_time_validate","parameters":[{"name":"id","in":"path","description":"Work time entry ID","required":true,"schema":{"type":"integer"}}],"responses":{"200":{"description":"Work time entry validated successfully","content":{"application/json":{"schema":{"properties":{"id":{"type":"integer","example":1},"isValidated":{"type":"boolean","example":true},"validatedAt":{"type":"string","format":"datetime","example":"2024-02-01T14:30:00Z"},"validatedBy":{"properties":{"id":{"type":"integer","example":2},"firstName":{"type":"string","example":"Pierre"},"lastName":{"type":"string","example":"Martin"}},"type":"object"},"message":{"type":"string","example":"Work time entry validated successfully"}},"type":"object"}}}}}}}},"components":{"schemas":{"Consultant":{"description":"Consultant model","required":["id","email","firstName","lastName","isAdmin","roles","createdAt","updatedAt"],"properties":{"id":{"description":"Consultant ID","type":"integer","example":1},"email":{"description":"Consultant email","type":"string","example":"<EMAIL>"},"firstName":{"description":"Consultant first name","type":"string","example":"John"},"lastName":{"description":"Consultant last name","type":"string","example":"Doe"},"phone":{"description":"Consultant phone number","type":"string","example":"+33123456789","nullable":true},"isAdmin":{"description":"Whether the consultant is an admin","type":"boolean","example":false},"roles":{"description":"Consultant roles","type":"array","items":{"type":"string"},"example":["ROLE_USER"]},"createdAt":{"description":"Creation date","type":"string","format":"date-time","example":"2023-01-01T12:00:00+00:00"},"updatedAt":{"description":"Last update date","type":"string","format":"date-time","example":"2023-01-01T12:00:00+00:00"}},"type":"object"},"RegistrationUserInfo":{"description":"User information after registration","required":["id","email","firstName","lastName"],"properties":{"id":{"description":"User ID","type":"integer","example":1},"email":{"description":"User email","type":"string","example":"<EMAIL>"},"firstName":{"description":"User first name","type":"string","example":"John"},"lastName":{"description":"User last name","type":"string","example":"Doe"}},"type":"object"},"RegistrationResponse":{"description":"Registration response model","required":["message","user"],"properties":{"message":{"description":"Success message","type":"string","example":"User registered successfully"},"user":{"$ref":"#/components/schemas/RegistrationUserInfo"}},"type":"object"},"RegistrationRequest":{"description":"Registration request model","required":["email","password","firstName","lastName"],"properties":{"email":{"description":"User email","type":"string","example":"<EMAIL>"},"password":{"description":"User password","type":"string","example":"password123"},"firstName":{"description":"User first name","type":"string","example":"John"},"lastName":{"description":"User last name","type":"string","example":"Doe"},"phone":{"description":"User phone number","type":"string","example":"+33123456789","nullable":true}},"type":"object"},"UserInfo":{"description":"User information","required":["id","email","firstName","lastName","roles","isAdmin"],"properties":{"id":{"description":"User ID","type":"integer","example":1},"email":{"description":"User email","type":"string","example":"<EMAIL>"},"firstName":{"description":"User first name","type":"string","example":"John"},"lastName":{"description":"User last name","type":"string","example":"Doe"},"roles":{"description":"User roles","type":"array","items":{"type":"string"},"example":["ROLE_USER"]},"isAdmin":{"description":"Whether the user is an admin","type":"boolean","example":false}},"type":"object"},"LoginResponse":{"description":"Login response model","required":["token","user"],"properties":{"token":{"description":"JWT token","type":"string","example":"eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiJ9..."},"user":{"$ref":"#/components/schemas/UserInfo"}},"type":"object"},"LoginRequest":{"description":"Login request model","required":["email","password"],"properties":{"email":{"description":"User email","type":"string","example":"<EMAIL>"},"password":{"description":"User password","type":"string","example":"password123"}},"type":"object"}},"securitySchemes":{"Bearer":{"type":"http","description":"Enter JWT token in the format: Bearer {token}","bearerFormat":"JWT","scheme":"bearer"}}},"security":[{"Bearer":[]}],"tags":[{"name":"Authentication","description":"User authentication and token management"},{"name":"Consultants","description":"Consultant profile management"},{"name":"Work Time","description":"Work time tracking and management"},{"name":"Expenses","description":"Expense reporting and management"},{"name":"Leaves","description":"Leave request management"},{"name":"Company","description":"Company information management"},{"name":"Dashboard","description":"Analytics and reporting"},{"name":"Clients","description":"Client management and information"},{"name":"Trips","description":"Business trip planning and tracking"},{"name":"Expense Categories","description":"Expense category management and hierarchy"}]}}</script>
    </head>
<body cz-shortcut-listen="true" style="padding-top: 175px;">
            <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" id="swagger-ui-logos">
            <defs>
                <symbol viewBox="0 0 20 20" id="unlocked">
                    <path d="M15.8 8H14V5.6C14 2.703 12.665 1 10 1 7.334 1 6 2.703 6 5.6V6h2v-.801C8 3.754 8.797 3 10 3c1.203 0 2 .754 2 2.199V8H4c-.553 0-1 .646-1 1.199V17c0 .549.428 1.139.951 1.307l1.197.387C5.672 18.861 6.55 19 7.1 19h5.8c.549 0 1.428-.139 1.951-.307l1.196-.387c.524-.167.953-.757.953-1.306V9.199C17 8.646 16.352 8 15.8 8z"></path>
                </symbol>
                <symbol viewBox="0 0 20 20" id="locked">
                    <path d="M15.8 8H14V5.6C14 2.703 12.665 1 10 1 7.334 1 6 2.703 6 5.6V8H4c-.553 0-1 .646-1 1.199V17c0 .549.428 1.139.951 1.307l1.197.387C5.672 18.861 6.55 19 7.1 19h5.8c.549 0 1.428-.139 1.951-.307l1.196-.387c.524-.167.953-.757.953-1.306V9.199C17 8.646 16.352 8 15.8 8zM12 8H8V5.199C8 3.754 8.797 3 10 3c1.203 0 2 .754 2 2.199V8z"></path>
                </symbol>
                <symbol viewBox="0 0 20 20" id="close">
                    <path d="M14.348 14.849c-.469.469-1.229.469-1.697 0L10 11.819l-2.651 3.029c-.469.469-1.229.469-1.697 0-.469-.469-.469-1.229 0-1.697l2.758-3.15-2.759-3.152c-.469-.469-.469-1.228 0-1.697.469-.469 1.228-.469 1.697 0L10 8.183l2.651-3.031c.469-.469 1.228-.469 1.697 0 .469.469.469 1.229 0 1.697l-2.758 3.152 2.758 3.15c.469.469.469 1.229 0 1.698z"></path>
                </symbol>
                <symbol viewBox="0 0 20 20" id="large-arrow">
                    <path d="M13.25 10L6.109 2.58c-.268-.27-.268-.707 0-.979.268-.27.701-.27.969 0l7.83 7.908c.268.271.268.709 0 .979l-7.83 7.908c-.268.271-.701.27-.969 0-.268-.269-.268-.707 0-.979L13.25 10z"></path>
                </symbol>
                <symbol viewBox="0 0 20 20" id="large-arrow-down">
                    <path d="M17.418 6.109c.272-.268.709-.268.979 0s.271.701 0 .969l-7.908 7.83c-.27.268-.707.268-.979 0l-7.908-7.83c-.27-.268-.27-.701 0-.969.271-.268.709-.268.979 0L10 13.25l7.418-7.141z"></path>
                </symbol>
                <symbol viewBox="0 0 24 24" id="jump-to">
                    <path d="M19 7v4H5.83l3.58-3.59L8 6l-6 6 6 6 1.41-1.41L5.83 13H21V7z"></path>
                </symbol>
                <symbol viewBox="0 0 24 24" id="expand">
                    <path d="M10 18h4v-2h-4v2zM3 6v2h18V6H3zm3 7h12v-2H6v2z"></path>
                </symbol>
            </defs>
        </svg>
        
            <header>
                <div class="custom-swagger-header">
        <div style="max-width: 1200px; margin: 0 auto; padding: 0 20px;">
            <div class="header-top">
                <h1><i class="fas fa-book"></i> Consultant Management System API</h1>
                <div class="version-badges">
                    <p class="version-badge">API 1.0.0</p>
                    <p class="version-badge">OAS 3.0</p>
                </div>
            </div>
            <p class="header-description">A comprehensive RESTful API for efficient consultant management, time tracking, expense reporting, and leave management</p>

            <div class="header-info">
                <div class="header-info-section">
                    <h3><i class="fas fa-info-circle"></i> About</h3>
                    <p>This API provides efficient consultant management, time tracking, expense reporting, and leave management capabilities.</p>
                </div>
                <div class="header-info-section">
                    <h3><i class="fas fa-link"></i> Quick Links</h3>
                    <div class="header-links">
                        <a href="https://staging.api.erp.fsli-group.com/"><i class="fas fa-home"></i> Home</a>
                        <a href="https://staging.api.erp.fsli-group.com/api/v1/doc#" onclick="authorizeModal()"><i class="fas fa-lock"></i> Authenticate</a>
                    </div>
                </div>
                <div class="header-info-section">
                    <h3><i class="fas fa-code"></i> API Details</h3>
                    <p class="base-url"><code>Base URL: /api/v1</code></p>
                </div>
            </div>
        </div>
    </div>
        </header>
    
                <div id="swagger-ui" class="api-platform"><section class="swagger-ui swagger-container"><div class="topbar"><div class="wrapper"><div class="topbar-wrapper"><a rel="noopener noreferrer" class="link"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 407 116" height="40"><defs><clippath id="logo_small_svg__clip-SW_TM-logo-on-dark"><path d="M0 0h407v116H0z"></path></clippath><style>.logo_small_svg__cls-2{fill:#fff}.logo_small_svg__cls-3{fill:#85ea2d}</style></defs><g id="logo_small_svg__SW_TM-logo-on-dark" style="clip-path: url(&quot;#logo_small_svg__clip-SW_TM-logo-on-dark&quot;);"><g id="logo_small_svg__SW_In-Product" transform="translate(-.301)"><path id="logo_small_svg__Path_2936" d="M359.15 70.674h-.7v-3.682h-1.26v-.6h3.219v.6h-1.259Z" class="logo_small_svg__cls-2" data-name="Path 2936"></path><path id="logo_small_svg__Path_2937" d="m363.217 70.674-1.242-3.574h-.023q.05.8.05 1.494v2.083h-.636v-4.286h.987l1.19 3.407h.017l1.225-3.407h.99v4.283h-.675v-2.118a30 30 0 0 1 .044-1.453h-.023l-1.286 3.571Z" class="logo_small_svg__cls-2" data-name="Path 2937"></path><path id="logo_small_svg__Path_2938" d="M50.328 97.669a47.642 47.642 0 1 1 47.643-47.642 47.64 47.64 0 0 1-47.643 47.642" class="logo_small_svg__cls-3" data-name="Path 2938"></path><path id="logo_small_svg__Path_2939" d="M50.328 4.769A45.258 45.258 0 1 1 5.07 50.027 45.26 45.26 0 0 1 50.328 4.769m0-4.769a50.027 50.027 0 1 0 50.027 50.027A50.027 50.027 0 0 0 50.328 0" class="logo_small_svg__cls-3" data-name="Path 2939"></path><path id="logo_small_svg__Path_2940" d="M31.8 33.854c-.154 1.712.058 3.482-.057 5.213a43 43 0 0 1-.693 5.156 9.53 9.53 0 0 1-4.1 5.829c4.079 2.654 4.54 6.771 4.81 10.946.135 2.25.077 4.52.308 6.752.173 1.731.846 2.174 2.636 2.231.73.02 1.48 0 2.327 0v5.349c-5.29.9-9.657-.6-10.734-5.079a31 31 0 0 1-.654-5c-.117-1.789.076-3.578-.058-5.367-.386-4.906-1.02-6.56-5.713-6.791v-6.1a9 9 0 0 1 1.028-.173c2.577-.135 3.674-.924 4.231-3.463a29 29 0 0 0 .481-4.329 82 82 0 0 1 .6-8.406c.673-3.982 3.136-5.906 7.234-6.137 1.154-.057 2.327 0 3.655 0v5.464c-.558.038-1.039.115-1.539.115-3.336-.115-3.51 1.02-3.762 3.79m6.406 12.658h-.077a3.515 3.515 0 1 0-.346 7.021h.231a3.46 3.46 0 0 0 3.655-3.251v-.192a3.523 3.523 0 0 0-3.461-3.578Zm12.062 0a3.373 3.373 0 0 0-3.482 3.251 2 2 0 0 0 .02.327 3.3 3.3 0 0 0 3.578 3.443 3.263 3.263 0 0 0 3.443-3.558 3.308 3.308 0 0 0-3.557-3.463Zm12.351 0a3.59 3.59 0 0 0-3.655 3.482 3.53 3.53 0 0 0 3.536 3.539h.039c1.769.309 3.559-1.4 3.674-3.462a3.57 3.57 0 0 0-3.6-3.559Zm16.948.288c-2.232-.1-3.348-.846-3.9-2.962a21.5 21.5 0 0 1-.635-4.136c-.154-2.578-.135-5.175-.308-7.753-.4-6.117-4.828-8.252-11.254-7.195v5.31c1.019 0 1.808 0 2.6.019 1.366.019 2.4.539 2.539 2.059.135 1.385.135 2.789.27 4.193.269 2.79.422 5.618.9 8.369a8.72 8.72 0 0 0 3.921 5.348c-3.4 2.289-4.406 5.559-4.578 9.234-.1 2.52-.154 5.059-.289 7.6-.115 2.308-.923 3.058-3.251 3.116-.654.019-1.289.077-2.019.115v5.445c1.365 0 2.616.077 3.866 0 3.886-.231 6.233-2.117 7-5.887A49 49 0 0 0 75 63.4c.135-1.923.116-3.866.308-5.771.289-2.982 1.655-4.213 4.636-4.4a4 4 0 0 0 .828-.192v-6.1c-.5-.058-.843-.115-1.208-.135Z" data-name="Path 2940" style="fill: rgb(23, 54, 71);"></path><path id="logo_small_svg__Path_2941" d="M152.273 58.122a11.23 11.23 0 0 1-4.384 9.424q-4.383 3.382-11.9 3.382-8.14 0-12.524-2.1V63.7a33 33 0 0 0 6.137 1.879 32.3 32.3 0 0 0 6.575.689q5.322 0 8.015-2.02a6.63 6.63 0 0 0 2.692-5.62 7.2 7.2 0 0 0-.954-3.9 8.9 8.9 0 0 0-3.194-2.8 44.6 44.6 0 0 0-6.81-2.911q-6.387-2.286-9.126-5.417a11.96 11.96 0 0 1-2.74-8.172A10.16 10.16 0 0 1 128.039 27q3.977-3.131 10.52-3.131a31 31 0 0 1 12.555 2.5L149.455 31a28.4 28.4 0 0 0-11.021-2.38 10.67 10.67 0 0 0-6.606 1.816 5.98 5.98 0 0 0-2.38 5.041 7.7 7.7 0 0 0 .877 3.9 8.24 8.24 0 0 0 2.959 2.786 36.7 36.7 0 0 0 6.371 2.8q7.2 2.566 9.91 5.51a10.84 10.84 0 0 1 2.708 7.649" class="logo_small_svg__cls-2" data-name="Path 2941"></path><path id="logo_small_svg__Path_2942" d="M185.288 70.3 179 50.17q-.594-1.848-2.222-8.391h-.251q-1.252 5.479-2.192 8.453L167.849 70.3h-6.011l-9.361-34.315h5.447q3.318 12.931 5.057 19.693a80 80 0 0 1 1.988 9.111h.25q.345-1.785 1.112-4.618t1.33-4.493l6.294-19.693h5.635l6.137 19.693a66 66 0 0 1 2.379 9.048h.251a33 33 0 0 1 .673-3.475q.548-2.347 6.528-25.266h5.385L191.456 70.3Z" class="logo_small_svg__cls-2" data-name="Path 2942"></path><path id="logo_small_svg__Path_2943" d="m225.115 70.3-1.033-4.885h-.25a14.45 14.45 0 0 1-5.119 4.368 15.6 15.6 0 0 1-6.372 1.143q-5.1 0-8-2.63t-2.9-7.483q0-10.4 16.626-10.9l5.823-.188V47.6q0-4.038-1.738-5.964t-5.552-1.923a22.6 22.6 0 0 0-9.706 2.63l-1.6-3.977a24.4 24.4 0 0 1 5.557-2.16 24 24 0 0 1 6.058-.783q6.136 0 9.1 2.724t2.959 8.735V70.3Zm-11.741-3.663a10.55 10.55 0 0 0 7.626-2.66 9.85 9.85 0 0 0 2.771-7.451v-3.1l-5.2.219q-6.2.219-8.939 1.926a5.8 5.8 0 0 0-2.74 5.306 5.35 5.35 0 0 0 1.707 4.29 7.08 7.08 0 0 0 4.775 1.472Z" class="logo_small_svg__cls-2" data-name="Path 2943"></path><path id="logo_small_svg__Path_2944" d="M264.6 35.987v3.287l-6.356.752a11.16 11.16 0 0 1 2.255 6.856 10.15 10.15 0 0 1-3.444 8.047q-3.444 3-9.456 3a15.7 15.7 0 0 1-2.88-.25Q241.4 59.438 241.4 62.1a2.24 2.24 0 0 0 1.159 2.082 8.46 8.46 0 0 0 3.976.673h6.074q5.573 0 8.563 2.348a8.16 8.16 0 0 1 2.99 6.825 9.74 9.74 0 0 1-4.571 8.688q-4.572 2.989-13.338 2.99-6.732 0-10.379-2.5a8.09 8.09 0 0 1-3.647-7.076 7.95 7.95 0 0 1 2-5.417 10.2 10.2 0 0 1 5.636-3.1 5.43 5.43 0 0 1-2.207-1.847 4.9 4.9 0 0 1-.893-2.912 5.53 5.53 0 0 1 1-3.288 10.5 10.5 0 0 1 3.162-2.723 9.28 9.28 0 0 1-4.336-3.726 10.95 10.95 0 0 1-1.675-6.012q0-5.634 3.382-8.688t9.58-3.052a17.4 17.4 0 0 1 4.853.626Zm-27.367 40.075a4.66 4.66 0 0 0 2.348 4.227 12.97 12.97 0 0 0 6.732 1.44q6.543 0 9.69-1.956a5.99 5.99 0 0 0 3.147-5.307q0-2.787-1.723-3.867t-6.481-1.08h-6.23a8.2 8.2 0 0 0-5.51 1.69 6.04 6.04 0 0 0-1.973 4.853m2.818-29.086a6.98 6.98 0 0 0 2.035 5.448 8.12 8.12 0 0 0 5.667 1.847q7.608 0 7.608-7.389 0-7.733-7.7-7.733a7.63 7.63 0 0 0-5.635 1.972q-1.976 1.973-1.975 5.855" class="logo_small_svg__cls-2" data-name="Path 2944"></path><path id="logo_small_svg__Path_2945" d="M299.136 35.987v3.287l-6.356.752a11.17 11.17 0 0 1 2.254 6.856 10.15 10.15 0 0 1-3.444 8.047q-3.444 3-9.455 3a15.7 15.7 0 0 1-2.88-.25q-3.32 1.754-3.319 4.415a2.24 2.24 0 0 0 1.158 2.082 8.46 8.46 0 0 0 3.976.673h6.074q5.574 0 8.563 2.348a8.16 8.16 0 0 1 2.99 6.825 9.74 9.74 0 0 1-4.571 8.688q-4.57 2.989-13.337 2.99-6.732 0-10.379-2.5a8.09 8.09 0 0 1-3.648-7.076 7.95 7.95 0 0 1 2-5.417 10.2 10.2 0 0 1 5.636-3.1 5.43 5.43 0 0 1-2.208-1.847 4.9 4.9 0 0 1-.892-2.912 5.53 5.53 0 0 1 1-3.288 10.5 10.5 0 0 1 3.162-2.723 9.27 9.27 0 0 1-4.336-3.726 10.95 10.95 0 0 1-1.675-6.012q0-5.634 3.381-8.688t9.581-3.052a17.4 17.4 0 0 1 4.853.626Zm-27.364 40.075a4.66 4.66 0 0 0 2.348 4.227 12.97 12.97 0 0 0 6.731 1.44q6.544 0 9.691-1.956a5.99 5.99 0 0 0 3.146-5.307q0-2.787-1.722-3.867t-6.481-1.08h-6.23a8.2 8.2 0 0 0-5.511 1.69 6.04 6.04 0 0 0-1.972 4.853m2.818-29.086a6.98 6.98 0 0 0 2.035 5.448 8.12 8.12 0 0 0 5.667 1.847q7.607 0 7.608-7.389 0-7.733-7.7-7.733a7.63 7.63 0 0 0-5.635 1.972q-1.975 1.973-1.975 5.855" class="logo_small_svg__cls-2" data-name="Path 2945"></path><path id="logo_small_svg__Path_2946" d="M316.778 70.928q-7.608 0-12.007-4.634t-4.4-12.868q0-8.3 4.086-13.181a13.57 13.57 0 0 1 10.974-4.884 12.94 12.94 0 0 1 10.207 4.239q3.762 4.247 3.762 11.2v3.287h-23.643q.156 6.044 3.053 9.174t8.156 3.131a27.6 27.6 0 0 0 10.958-2.317v4.634a27.5 27.5 0 0 1-5.213 1.706 29.3 29.3 0 0 1-5.933.513m-1.409-31.215a8.49 8.49 0 0 0-6.591 2.692 12.4 12.4 0 0 0-2.9 7.452h17.94q0-4.916-2.191-7.53a7.71 7.71 0 0 0-6.258-2.614" class="logo_small_svg__cls-2" data-name="Path 2946"></path><path id="logo_small_svg__Path_2947" d="M350.9 35.361a20.4 20.4 0 0 1 4.1.375l-.721 4.822a17.7 17.7 0 0 0-3.757-.47 9.14 9.14 0 0 0-7.122 3.382 12.33 12.33 0 0 0-2.959 8.422V70.3h-5.2V35.987h4.29l.6 6.356h.25a15.1 15.1 0 0 1 4.6-5.166 10.36 10.36 0 0 1 5.919-1.816" class="logo_small_svg__cls-2" data-name="Path 2947"></path><path id="logo_small_svg__Path_2948" d="M255.857 96.638s-3.43-.391-4.85-.391c-2.058 0-3.111.735-3.111 2.18 0 1.568.882 1.935 3.748 2.719 3.527.98 4.8 1.911 4.8 4.777 0 3.675-2.3 5.267-5.61 5.267a36 36 0 0 1-5.487-.662l.27-2.18s3.306.441 5.046.441c2.082 0 3.037-.931 3.037-2.7 0-1.421-.759-1.91-3.331-2.523-3.626-.93-5.193-2.033-5.193-4.948 0-3.381 2.229-4.776 5.585-4.776a37 37 0 0 1 5.315.587Z" class="logo_small_svg__cls-2" data-name="Path 2948"></path><path id="logo_small_svg__Path_2949" d="M262.967 94.14h4.733l3.748 13.106L275.2 94.14h4.752v16.78H277.2v-14.5h-.145l-4.191 13.816h-2.842l-4.191-13.816h-.145v14.5h-2.719Z" class="logo_small_svg__cls-2" data-name="Path 2949"></path><path id="logo_small_svg__Path_2950" d="M322.057 94.14H334.3v2.425h-4.728v14.355h-2.743V96.565h-4.777Z" class="logo_small_svg__cls-2" data-name="Path 2950"></path><path id="logo_small_svg__Path_2951" d="M346.137 94.14c3.332 0 5.12 1.249 5.12 4.361 0 2.033-.637 3.037-1.984 3.772 1.445.563 2.4 1.592 2.4 3.9 0 3.43-2.081 4.752-5.339 4.752h-6.566V94.14Zm-3.65 2.352v4.8h3.6c1.666 0 2.4-.832 2.4-2.474 0-1.617-.833-2.327-2.5-2.327Zm0 7.1v4.973h3.7c1.689 0 2.694-.539 2.694-2.548 0-1.911-1.421-2.425-2.744-2.425Z" class="logo_small_svg__cls-2" data-name="Path 2951"></path><path id="logo_small_svg__Path_2952" d="M358.414 94.14H369v2.377h-7.864v4.751h6.394v2.332h-6.394v4.924H369v2.4h-10.586Z" class="logo_small_svg__cls-2" data-name="Path 2952"></path><path id="logo_small_svg__Path_2953" d="M378.747 94.14h5.414l4.164 16.78h-2.744l-1.239-4.92h-5.777l-1.239 4.923h-2.719Zm.361 9.456h4.708l-1.737-7.178h-1.225Z" class="logo_small_svg__cls-2" data-name="Path 2953"></path><path id="logo_small_svg__Path_2954" d="M397.1 105.947v4.973h-2.719V94.14h6.37c3.7 0 5.683 2.12 5.683 5.843 0 2.376-.956 4.519-2.744 5.352l2.769 5.585h-2.989l-2.426-4.973Zm3.651-9.455H397.1v7.1h3.7c2.057 0 2.841-1.85 2.841-3.589 0-1.9-.934-3.511-2.894-3.511Z" class="logo_small_svg__cls-2" data-name="Path 2954"></path><path id="logo_small_svg__Path_2955" d="M290.013 94.14h5.413l4.164 16.78h-2.743l-1.239-4.92h-5.777l-1.239 4.923h-2.719Zm.361 9.456h4.707l-1.737-7.178h-1.225Z" class="logo_small_svg__cls-2" data-name="Path 2955"></path><path id="logo_small_svg__Path_2956" d="M308.362 105.947v4.973h-2.719V94.14h6.369c3.7 0 5.683 2.12 5.683 5.843 0 2.376-.955 4.519-2.743 5.352l2.768 5.585h-2.989l-2.425-4.973Zm3.65-9.455h-3.65v7.1h3.7c2.058 0 2.841-1.85 2.841-3.589-.003-1.903-.931-3.511-2.891-3.511" class="logo_small_svg__cls-2" data-name="Path 2956"></path><path id="logo_small_svg__Path_2957" d="M130.606 107.643a3.02 3.02 0 0 1-1.18 2.537 5.1 5.1 0 0 1-3.2.91 8 8 0 0 1-3.371-.564v-1.383a9 9 0 0 0 1.652.506 8.7 8.7 0 0 0 1.77.186 3.57 3.57 0 0 0 2.157-.544 1.78 1.78 0 0 0 .725-1.512 1.95 1.95 0 0 0-.257-1.05 2.4 2.4 0 0 0-.86-.754 12 12 0 0 0-1.833-.784 5.84 5.84 0 0 1-2.456-1.458 3.2 3.2 0 0 1-.738-2.2 2.74 2.74 0 0 1 1.071-2.267 4.44 4.44 0 0 1 2.831-.843 8.3 8.3 0 0 1 3.38.675l-.447 1.247a7.6 7.6 0 0 0-2.966-.641 2.88 2.88 0 0 0-1.779.489 1.61 1.61 0 0 0-.64 1.357 2.1 2.1 0 0 0 .236 1.049 2.2 2.2 0 0 0 .8.75 10 10 0 0 0 1.715.754 6.8 6.8 0 0 1 2.667 1.483 2.92 2.92 0 0 1 .723 2.057" class="logo_small_svg__cls-2" data-name="Path 2957"></path><path id="logo_small_svg__Path_2958" d="M134.447 101.686v5.991a2.4 2.4 0 0 0 .515 1.686 2.1 2.1 0 0 0 1.609.556 2.63 2.63 0 0 0 2.12-.792 4 4 0 0 0 .67-2.587v-4.854h1.4v9.236H139.6l-.2-1.239h-.075a2.8 2.8 0 0 1-1.193 1.045 4 4 0 0 1-1.74.362 3.53 3.53 0 0 1-2.524-.8 3.4 3.4 0 0 1-.839-2.562v-6.042Z" class="logo_small_svg__cls-2" data-name="Path 2958"></path><path id="logo_small_svg__Path_2959" d="M148.206 111.09a4 4 0 0 1-1.647-.333 3.1 3.1 0 0 1-1.252-1.023h-.1a12 12 0 0 1 .1 1.533v3.8h-1.4v-13.381h1.137l.194 1.264h.067a3.26 3.26 0 0 1 1.256-1.1 3.8 3.8 0 0 1 1.643-.337 3.41 3.41 0 0 1 2.836 1.256 6.68 6.68 0 0 1-.017 7.057 3.42 3.42 0 0 1-2.817 1.264m-.2-8.385a2.48 2.48 0 0 0-2.048.784 4.04 4.04 0 0 0-.649 2.494v.312a4.63 4.63 0 0 0 .649 2.785 2.47 2.47 0 0 0 2.082.839 2.16 2.16 0 0 0 1.875-.969 4.6 4.6 0 0 0 .678-2.671 4.43 4.43 0 0 0-.678-2.651 2.23 2.23 0 0 0-1.915-.923Z" class="logo_small_svg__cls-2" data-name="Path 2959"></path><path id="logo_small_svg__Path_2960" d="M159.039 111.09a4 4 0 0 1-1.647-.333 3.1 3.1 0 0 1-1.252-1.023h-.1a12 12 0 0 1 .1 1.533v3.8h-1.4v-13.381h1.137l.194 1.264h.067a3.26 3.26 0 0 1 1.256-1.1 3.8 3.8 0 0 1 1.643-.337 3.41 3.41 0 0 1 2.836 1.256 6.68 6.68 0 0 1-.017 7.057 3.42 3.42 0 0 1-2.817 1.264m-.2-8.385a2.48 2.48 0 0 0-2.048.784 4.04 4.04 0 0 0-.649 2.494v.312a4.63 4.63 0 0 0 .649 2.785 2.47 2.47 0 0 0 2.082.839 2.16 2.16 0 0 0 1.875-.969 4.6 4.6 0 0 0 .678-2.671 4.43 4.43 0 0 0-.678-2.651 2.23 2.23 0 0 0-1.911-.923Z" class="logo_small_svg__cls-2" data-name="Path 2960"></path><path id="logo_small_svg__Path_2961" d="M173.612 106.3a5.1 5.1 0 0 1-1.137 3.527 4 4 0 0 1-3.143 1.268 4.17 4.17 0 0 1-2.2-.581 3.84 3.84 0 0 1-1.483-1.669 5.8 5.8 0 0 1-.522-2.545 5.1 5.1 0 0 1 1.129-3.518 4 4 0 0 1 3.135-1.26 3.9 3.9 0 0 1 3.08 1.29 5.07 5.07 0 0 1 1.141 3.488m-7.036 0a4.4 4.4 0 0 0 .708 2.7 2.81 2.81 0 0 0 4.167 0 4.37 4.37 0 0 0 .712-2.7 4.3 4.3 0 0 0-.712-2.675 2.5 2.5 0 0 0-2.1-.915 2.46 2.46 0 0 0-2.072.9 4.33 4.33 0 0 0-.7 2.69Z" class="logo_small_svg__cls-2" data-name="Path 2961"></path><path id="logo_small_svg__Path_2962" d="M180.525 101.517a5.5 5.5 0 0 1 1.1.1l-.194 1.3a4.8 4.8 0 0 0-1.011-.127 2.46 2.46 0 0 0-1.917.911 3.32 3.32 0 0 0-.8 2.267v4.955h-1.4v-9.236h1.154l.16 1.71h.068a4.05 4.05 0 0 1 1.238-1.39 2.8 2.8 0 0 1 1.6-.49Z" class="logo_small_svg__cls-2" data-name="Path 2962"></path><path id="logo_small_svg__Path_2963" d="M187.363 109.936a4.5 4.5 0 0 0 .716-.055 4 4 0 0 0 .548-.114v1.07a2.5 2.5 0 0 1-.67.181 5 5 0 0 1-.8.072q-2.68 0-2.68-2.823v-5.494h-1.323v-.673l1.323-.582.59-1.972h.809v2.141h2.68v1.087h-2.68v5.435a1.87 1.87 0 0 0 .4 1.281 1.38 1.38 0 0 0 1.087.446" class="logo_small_svg__cls-2" data-name="Path 2963"></path><path id="logo_small_svg__Path_2964" d="M194.538 111.09a4.24 4.24 0 0 1-3.231-1.247 4.82 4.82 0 0 1-1.184-3.463 5.36 5.36 0 0 1 1.1-3.548 3.65 3.65 0 0 1 2.954-1.315 3.48 3.48 0 0 1 2.747 1.142 4.38 4.38 0 0 1 1.011 3.013v.885h-6.362a3.66 3.66 0 0 0 .822 2.469 2.84 2.84 0 0 0 2.2.843 7.4 7.4 0 0 0 2.949-.624v1.247a7.4 7.4 0 0 1-1.4.459 8 8 0 0 1-1.6.139Zm-.379-8.4a2.29 2.29 0 0 0-1.774.725 3.34 3.34 0 0 0-.779 2.006h4.828a3.07 3.07 0 0 0-.59-2.027 2.08 2.08 0 0 0-1.685-.706Z" class="logo_small_svg__cls-2" data-name="Path 2964"></path><path id="logo_small_svg__Path_2965" d="M206.951 109.683h-.076a3.29 3.29 0 0 1-2.9 1.407 3.43 3.43 0 0 1-2.819-1.239 5.45 5.45 0 0 1-1.006-3.522 5.54 5.54 0 0 1 1.011-3.548 3.4 3.4 0 0 1 2.814-1.264 3.36 3.36 0 0 1 2.883 1.365h.109l-.059-.665-.034-.649v-3.759h1.4v13.113h-1.138Zm-2.8.236a2.55 2.55 0 0 0 2.078-.779 3.95 3.95 0 0 0 .644-2.516v-.3a4.64 4.64 0 0 0-.653-2.8 2.48 2.48 0 0 0-2.086-.839 2.14 2.14 0 0 0-1.883.957 4.76 4.76 0 0 0-.653 2.7 4.55 4.55 0 0 0 .649 2.671 2.2 2.2 0 0 0 1.906.906Z" class="logo_small_svg__cls-2" data-name="Path 2965"></path><path id="logo_small_svg__Path_2966" d="M220.712 101.534a3.44 3.44 0 0 1 2.827 1.243 6.65 6.65 0 0 1-.009 7.053 3.42 3.42 0 0 1-2.818 1.26 4 4 0 0 1-1.648-.333 3.1 3.1 0 0 1-1.251-1.023h-.1l-.295 1.188h-1V97.809h1.4V101q0 1.069-.068 1.921h.068a3.32 3.32 0 0 1 2.894-1.387m-.2 1.171a2.44 2.44 0 0 0-2.064.822 6.34 6.34 0 0 0 .017 5.553 2.46 2.46 0 0 0 2.081.839 2.16 2.16 0 0 0 1.922-.94 4.83 4.83 0 0 0 .632-2.7 4.64 4.64 0 0 0-.632-2.689 2.24 2.24 0 0 0-1.959-.885Z" class="logo_small_svg__cls-2" data-name="Path 2966"></path><path id="logo_small_svg__Path_2967" d="M225.758 101.686h1.5l2.023 5.267a20 20 0 0 1 .826 2.6h.067q.109-.431.459-1.471t2.288-6.4h1.5l-3.969 10.518a5.25 5.25 0 0 1-1.378 2.212 2.93 2.93 0 0 1-1.934.653 5.7 5.7 0 0 1-1.264-.143V113.8a5 5 0 0 0 1.037.1 2.136 2.136 0 0 0 2.056-1.618l.514-1.314Z" class="logo_small_svg__cls-2" data-name="Path 2967"></path></g></g></svg></a><form class="download-url-wrapper"><input class="download-url-input" type="text" id="download-url-input" value=""><button class="download-url-button button">Explore</button></form></div></div></div><div class="swagger-ui"><div><svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" class="svg-assets"><defs><symbol viewBox="0 0 20 20" id="unlocked"><path d="M15.8 8H14V5.6C14 2.703 12.665 1 10 1 7.334 1 6 2.703 6 5.6V6h2v-.801C8 3.754 8.797 3 10 3c1.203 0 2 .754 2 2.199V8H4c-.553 0-1 .646-1 1.199V17c0 .549.428 1.139.951 1.307l1.197.387C5.672 18.861 6.55 19 7.1 19h5.8c.549 0 1.428-.139 1.951-.307l1.196-.387c.524-.167.953-.757.953-1.306V9.199C17 8.646 16.352 8 15.8 8z"></path></symbol><symbol viewBox="0 0 20 20" id="locked"><path d="M15.8 8H14V5.6C14 2.703 12.665 1 10 1 7.334 1 6 2.703 6 5.6V8H4c-.553 0-1 .646-1 1.199V17c0 .549.428 1.139.951 1.307l1.197.387C5.672 18.861 6.55 19 7.1 19h5.8c.549 0 1.428-.139 1.951-.307l1.196-.387c.524-.167.953-.757.953-1.306V9.199C17 8.646 16.352 8 15.8 8zM12 8H8V5.199C8 3.754 8.797 3 10 3c1.203 0 2 .754 2 2.199V8z"></path></symbol><symbol viewBox="0 0 20 20" id="close"><path d="M14.348 14.849c-.469.469-1.229.469-1.697 0L10 11.819l-2.651 3.029c-.469.469-1.229.469-1.697 0-.469-.469-.469-1.229 0-1.697l2.758-3.15-2.759-3.152c-.469-.469-.469-1.228 0-1.697.469-.469 1.228-.469 1.697 0L10 8.183l2.651-3.031c.469-.469 1.228-.469 1.697 0 .469.469.469 1.229 0 1.697l-2.758 3.152 2.758 3.15c.469.469.469 1.229 0 1.698z"></path></symbol><symbol viewBox="0 0 20 20" id="large-arrow"><path d="M13.25 10L6.109 2.58c-.268-.27-.268-.707 0-.979.268-.27.701-.27.969 0l7.83 7.908c.268.271.268.709 0 .979l-7.83 7.908c-.268.271-.701.27-.969 0-.268-.269-.268-.707 0-.979L13.25 10z"></path></symbol><symbol viewBox="0 0 20 20" id="large-arrow-down"><path d="M17.418 6.109c.272-.268.709-.268.979 0s.271.701 0 .969l-7.908 7.83c-.27.268-.707.268-.979 0l-7.908-7.83c-.27-.268-.27-.701 0-.969.271-.268.709-.268.979 0L10 13.25l7.418-7.141z"></path></symbol><symbol viewBox="0 0 20 20" id="large-arrow-up"><path d="M 17.418 14.908 C 17.69 15.176 18.127 15.176 18.397 14.908 C 18.667 14.64 18.668 14.207 18.397 13.939 L 10.489 6.109 C 10.219 5.841 9.782 5.841 9.51 6.109 L 1.602 13.939 C 1.332 14.207 1.332 14.64 1.602 14.908 C 1.873 15.176 2.311 15.176 2.581 14.908 L 10 7.767 L 17.418 14.908 Z"></path></symbol><symbol viewBox="0 0 24 24" id="jump-to"><path d="M19 7v4H5.83l3.58-3.59L8 6l-6 6 6 6 1.41-1.41L5.83 13H21V7z"></path></symbol><symbol viewBox="0 0 24 24" id="expand"><path d="M10 18h4v-2h-4v2zM3 6v2h18V6H3zm3 7h12v-2H6v2z"></path></symbol><symbol viewBox="0 0 15 16" id="copy"><g transform="translate(2, -1)"><path fill="#ffffff" fill-rule="evenodd" d="M2 13h4v1H2v-1zm5-6H2v1h5V7zm2 3V8l-3 3 3 3v-2h5v-2H9zM4.5 9H2v1h2.5V9zM2 12h2.5v-1H2v1zm9 1h1v2c-.02.28-.11.52-.3.7-.19.18-.42.28-.7.3H1c-.55 0-1-.45-1-1V4c0-.55.45-1 1-1h3c0-1.11.89-2 2-2 1.11 0 2 .89 2 2h3c.55 0 1 .45 1 1v5h-1V6H1v9h10v-2zM2 5h8c0-.55-.45-1-1-1H8c-.55 0-1-.45-1-1s-.45-1-1-1-1 .45-1 1-.45 1-1 1H3c-.55 0-1 .45-1 1z"></path></g></symbol></defs></svg></div><div><div class="information-container wrapper"><section class="block col-12"><div><div class="info" style="display: none;"><hgroup class="main"><h2 class="title">Consultant Management System API<span><small><pre class="version"> 1.0.0 </pre></small><small class="version-stamp"><pre class="version">OAS 3.0</pre></small></span></h2></hgroup><div class="description"><div class="renderedMarkdown"><p>A comprehensive RESTful API for efficient consultant management, time tracking, expense reporting, and leave management</p></div></div><div class="info__contact"><div><a href="https://consultantmanagementsystem.com/support" target="_blank" rel="noopener noreferrer" class="link">API Support - Website</a></div><a href="mailto:<EMAIL>" rel="noopener noreferrer" class="link">Send email to API Support</a></div></div></div></section></div><div class="scheme-container"><section class="schemes wrapper block col-12"><div class="auth-wrapper"><button class="btn authorize unlocked"><span>Authorize</span><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" width="20" height="20" aria-hidden="true" focusable="false"><path d="M15.8 8H14V5.6C14 2.703 12.665 1 10 1 7.334 1 6 2.703 6 5.6V6h2v-.801C8 3.754 8.797 3 10 3c1.203 0 2 .754 2 2.199V8H4c-.553 0-1 .646-1 1.199V17c0 .549.428 1.139.951 1.307l1.197.387C5.672 18.861 6.55 19 7.1 19h5.8c.549 0 1.428-.139 1.951-.307l1.196-.387c.524-.167.953-.757.953-1.306V9.199C17 8.646 16.352 8 15.8 8z"></path></svg></button></div></section></div><div></div><div class="wrapper"><section class="block col-12 block-desktop col-12-desktop"><div><span><div class="opblock-tag-section is-open"><h3 class="opblock-tag" id="operations-tag-Authentication" data-tag="Authentication" data-is-open="true"><a class="nostyle"><span>Authentication</span></a><small><div class="renderedMarkdown"><p>User authentication and token management</p></div></small><button aria-expanded="true" class="expand-operation" title="Collapse operation"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" class="arrow" width="20" height="20" aria-hidden="true" focusable="false"><path d="M 17.418 14.908 C 17.69 15.176 18.127 15.176 18.397 14.908 C 18.667 14.64 18.668 14.207 18.397 13.939 L 10.489 6.109 C 10.219 5.841 9.782 5.841 9.51 6.109 L 1.602 13.939 C 1.332 14.207 1.332 14.64 1.602 14.908 C 1.873 15.176 2.311 15.176 2.581 14.908 L 10 7.767 L 17.418 14.908 Z"></path></svg></button></h3><div class="no-margin"> <div class="operation-tag-content"><span><div class="opblock opblock-post" id="operations-Authentication-post_api_login"><div class="opblock-summary opblock-summary-post"><button aria-expanded="false" class="opblock-summary-control"><span class="opblock-summary-method">POST</span><div class="opblock-summary-path-description-wrapper"><span class="opblock-summary-path" data-path="/api/v1/auth/login"><a class="nostyle"><span>/api<wbr>/v1<wbr>/auth<wbr>/login</span></a></span><div class="opblock-summary-description">Login to get a JWT token</div></div></button><div class="view-line-link copy-to-clipboard" title="Copy to clipboard"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 15 16" width="15" height="16" aria-hidden="true" focusable="false"><g transform="translate(2, -1)"><path fill="#ffffff" fill-rule="evenodd" d="M2 13h4v1H2v-1zm5-6H2v1h5V7zm2 3V8l-3 3 3 3v-2h5v-2H9zM4.5 9H2v1h2.5V9zM2 12h2.5v-1H2v1zm9 1h1v2c-.02.28-.11.52-.3.7-.19.18-.42.28-.7.3H1c-.55 0-1-.45-1-1V4c0-.55.45-1 1-1h3c0-1.11.89-2 2-2 1.11 0 2 .89 2 2h3c.55 0 1 .45 1 1v5h-1V6H1v9h10v-2zM2 5h8c0-.55-.45-1-1-1H8c-.55 0-1-.45-1-1s-.45-1-1-1-1 .45-1 1-.45 1-1 1H3c-.55 0-1 .45-1 1z"></path></g></svg></div><button class="authorization__btn" aria-label="authorization button unlocked"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" class="unlocked" width="20" height="20" aria-hidden="true" focusable="false"><path d="M15.8 8H14V5.6C14 2.703 12.665 1 10 1 7.334 1 6 2.703 6 5.6V6h2v-.801C8 3.754 8.797 3 10 3c1.203 0 2 .754 2 2.199V8H4c-.553 0-1 .646-1 1.199V17c0 .549.428 1.139.951 1.307l1.197.387C5.672 18.861 6.55 19 7.1 19h5.8c.549 0 1.428-.139 1.951-.307l1.196-.387c.524-.167.953-.757.953-1.306V9.199C17 8.646 16.352 8 15.8 8z"></path></svg></button><button aria-label="post ​/api​/v1​/auth​/login" class="opblock-control-arrow" aria-expanded="false" tabindex="-1"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" class="arrow" width="20" height="20" aria-hidden="true" focusable="false"><path d="M17.418 6.109c.272-.268.709-.268.979 0s.271.701 0 .969l-7.908 7.83c-.27.268-.707.268-.979 0l-7.908-7.83c-.27-.268-.27-.701 0-.969.271-.268.709-.268.979 0L10 13.25l7.418-7.141z"></path></svg></button></div><noscript></noscript></div></span><span><div class="opblock opblock-post" id="operations-Authentication-post_api_register"><div class="opblock-summary opblock-summary-post"><button aria-expanded="false" class="opblock-summary-control"><span class="opblock-summary-method">POST</span><div class="opblock-summary-path-description-wrapper"><span class="opblock-summary-path" data-path="/api/v1/auth/register"><a class="nostyle"><span>/api<wbr>/v1<wbr>/auth<wbr>/register</span></a></span><div class="opblock-summary-description">Register a new user</div></div></button><div class="view-line-link copy-to-clipboard" title="Copy to clipboard"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 15 16" width="15" height="16" aria-hidden="true" focusable="false"><g transform="translate(2, -1)"><path fill="#ffffff" fill-rule="evenodd" d="M2 13h4v1H2v-1zm5-6H2v1h5V7zm2 3V8l-3 3 3 3v-2h5v-2H9zM4.5 9H2v1h2.5V9zM2 12h2.5v-1H2v1zm9 1h1v2c-.02.28-.11.52-.3.7-.19.18-.42.28-.7.3H1c-.55 0-1-.45-1-1V4c0-.55.45-1 1-1h3c0-1.11.89-2 2-2 1.11 0 2 .89 2 2h3c.55 0 1 .45 1 1v5h-1V6H1v9h10v-2zM2 5h8c0-.55-.45-1-1-1H8c-.55 0-1-.45-1-1s-.45-1-1-1-1 .45-1 1-.45 1-1 1H3c-.55 0-1 .45-1 1z"></path></g></svg></div><button class="authorization__btn" aria-label="authorization button unlocked"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" class="unlocked" width="20" height="20" aria-hidden="true" focusable="false"><path d="M15.8 8H14V5.6C14 2.703 12.665 1 10 1 7.334 1 6 2.703 6 5.6V6h2v-.801C8 3.754 8.797 3 10 3c1.203 0 2 .754 2 2.199V8H4c-.553 0-1 .646-1 1.199V17c0 .549.428 1.139.951 1.307l1.197.387C5.672 18.861 6.55 19 7.1 19h5.8c.549 0 1.428-.139 1.951-.307l1.196-.387c.524-.167.953-.757.953-1.306V9.199C17 8.646 16.352 8 15.8 8z"></path></svg></button><button aria-label="post ​/api​/v1​/auth​/register" class="opblock-control-arrow" aria-expanded="false" tabindex="-1"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" class="arrow" width="20" height="20" aria-hidden="true" focusable="false"><path d="M17.418 6.109c.272-.268.709-.268.979 0s.271.701 0 .969l-7.908 7.83c-.27.268-.707.268-.979 0l-7.908-7.83c-.27-.268-.27-.701 0-.969.271-.268.709-.268.979 0L10 13.25l7.418-7.141z"></path></svg></button></div><noscript></noscript></div></span></div> </div></div></span><span><div class="opblock-tag-section is-open"><h3 class="opblock-tag" id="operations-tag-Consultants" data-tag="Consultants" data-is-open="true"><a class="nostyle"><span>Consultants</span></a><small><div class="renderedMarkdown"><p>Consultant profile management</p></div></small><button aria-expanded="true" class="expand-operation" title="Collapse operation"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" class="arrow" width="20" height="20" aria-hidden="true" focusable="false"><path d="M 17.418 14.908 C 17.69 15.176 18.127 15.176 18.397 14.908 C 18.667 14.64 18.668 14.207 18.397 13.939 L 10.489 6.109 C 10.219 5.841 9.782 5.841 9.51 6.109 L 1.602 13.939 C 1.332 14.207 1.332 14.64 1.602 14.908 C 1.873 15.176 2.311 15.176 2.581 14.908 L 10 7.767 L 17.418 14.908 Z"></path></svg></button></h3><div class="no-margin"> <div class="operation-tag-content"><span><div class="opblock opblock-get" id="operations-Consultants-get_app_consultant_index"><div class="opblock-summary opblock-summary-get"><button aria-expanded="false" class="opblock-summary-control"><span class="opblock-summary-method">GET</span><div class="opblock-summary-path-description-wrapper"><span class="opblock-summary-path" data-path="/api/v1/consultants"><a class="nostyle"><span>/api<wbr>/v1<wbr>/consultants</span></a></span><div class="opblock-summary-description">Get all consultants (admin only)</div></div></button><div class="view-line-link copy-to-clipboard" title="Copy to clipboard"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 15 16" width="15" height="16" aria-hidden="true" focusable="false"><g transform="translate(2, -1)"><path fill="#ffffff" fill-rule="evenodd" d="M2 13h4v1H2v-1zm5-6H2v1h5V7zm2 3V8l-3 3 3 3v-2h5v-2H9zM4.5 9H2v1h2.5V9zM2 12h2.5v-1H2v1zm9 1h1v2c-.02.28-.11.52-.3.7-.19.18-.42.28-.7.3H1c-.55 0-1-.45-1-1V4c0-.55.45-1 1-1h3c0-1.11.89-2 2-2 1.11 0 2 .89 2 2h3c.55 0 1 .45 1 1v5h-1V6H1v9h10v-2zM2 5h8c0-.55-.45-1-1-1H8c-.55 0-1-.45-1-1s-.45-1-1-1-1 .45-1 1-.45 1-1 1H3c-.55 0-1 .45-1 1z"></path></g></svg></div><button class="authorization__btn" aria-label="authorization button unlocked"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" class="unlocked" width="20" height="20" aria-hidden="true" focusable="false"><path d="M15.8 8H14V5.6C14 2.703 12.665 1 10 1 7.334 1 6 2.703 6 5.6V6h2v-.801C8 3.754 8.797 3 10 3c1.203 0 2 .754 2 2.199V8H4c-.553 0-1 .646-1 1.199V17c0 .549.428 1.139.951 1.307l1.197.387C5.672 18.861 6.55 19 7.1 19h5.8c.549 0 1.428-.139 1.951-.307l1.196-.387c.524-.167.953-.757.953-1.306V9.199C17 8.646 16.352 8 15.8 8z"></path></svg></button><button aria-label="get ​/api​/v1​/consultants" class="opblock-control-arrow" aria-expanded="false" tabindex="-1"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" class="arrow" width="20" height="20" aria-hidden="true" focusable="false"><path d="M17.418 6.109c.272-.268.709-.268.979 0s.271.701 0 .969l-7.908 7.83c-.27.268-.707.268-.979 0l-7.908-7.83c-.27-.268-.27-.701 0-.969.271-.268.709-.268.979 0L10 13.25l7.418-7.141z"></path></svg></button></div><noscript></noscript></div></span><span><div class="opblock opblock-post" id="operations-Consultants-post_app_consultant_create"><div class="opblock-summary opblock-summary-post"><button aria-expanded="false" class="opblock-summary-control"><span class="opblock-summary-method">POST</span><div class="opblock-summary-path-description-wrapper"><span class="opblock-summary-path" data-path="/api/v1/consultants"><a class="nostyle"><span>/api<wbr>/v1<wbr>/consultants</span></a></span><div class="opblock-summary-description">Create a new consultant (admin only)</div></div></button><div class="view-line-link copy-to-clipboard" title="Copy to clipboard"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 15 16" width="15" height="16" aria-hidden="true" focusable="false"><g transform="translate(2, -1)"><path fill="#ffffff" fill-rule="evenodd" d="M2 13h4v1H2v-1zm5-6H2v1h5V7zm2 3V8l-3 3 3 3v-2h5v-2H9zM4.5 9H2v1h2.5V9zM2 12h2.5v-1H2v1zm9 1h1v2c-.02.28-.11.52-.3.7-.19.18-.42.28-.7.3H1c-.55 0-1-.45-1-1V4c0-.55.45-1 1-1h3c0-1.11.89-2 2-2 1.11 0 2 .89 2 2h3c.55 0 1 .45 1 1v5h-1V6H1v9h10v-2zM2 5h8c0-.55-.45-1-1-1H8c-.55 0-1-.45-1-1s-.45-1-1-1-1 .45-1 1-.45 1-1 1H3c-.55 0-1 .45-1 1z"></path></g></svg></div><button class="authorization__btn" aria-label="authorization button unlocked"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" class="unlocked" width="20" height="20" aria-hidden="true" focusable="false"><path d="M15.8 8H14V5.6C14 2.703 12.665 1 10 1 7.334 1 6 2.703 6 5.6V6h2v-.801C8 3.754 8.797 3 10 3c1.203 0 2 .754 2 2.199V8H4c-.553 0-1 .646-1 1.199V17c0 .549.428 1.139.951 1.307l1.197.387C5.672 18.861 6.55 19 7.1 19h5.8c.549 0 1.428-.139 1.951-.307l1.196-.387c.524-.167.953-.757.953-1.306V9.199C17 8.646 16.352 8 15.8 8z"></path></svg></button><button aria-label="post ​/api​/v1​/consultants" class="opblock-control-arrow" aria-expanded="false" tabindex="-1"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" class="arrow" width="20" height="20" aria-hidden="true" focusable="false"><path d="M17.418 6.109c.272-.268.709-.268.979 0s.271.701 0 .969l-7.908 7.83c-.27.268-.707.268-.979 0l-7.908-7.83c-.27-.268-.27-.701 0-.969.271-.268.709-.268.979 0L10 13.25l7.418-7.141z"></path></svg></button></div><noscript></noscript></div></span><span><div class="opblock opblock-get" id="operations-Consultants-get_app_consultant_current"><div class="opblock-summary opblock-summary-get"><button aria-expanded="false" class="opblock-summary-control"><span class="opblock-summary-method">GET</span><div class="opblock-summary-path-description-wrapper"><span class="opblock-summary-path" data-path="/api/v1/consultants/me"><a class="nostyle"><span>/api<wbr>/v1<wbr>/consultants<wbr>/me</span></a></span><div class="opblock-summary-description">Get the current consultant's profile</div></div></button><div class="view-line-link copy-to-clipboard" title="Copy to clipboard"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 15 16" width="15" height="16" aria-hidden="true" focusable="false"><g transform="translate(2, -1)"><path fill="#ffffff" fill-rule="evenodd" d="M2 13h4v1H2v-1zm5-6H2v1h5V7zm2 3V8l-3 3 3 3v-2h5v-2H9zM4.5 9H2v1h2.5V9zM2 12h2.5v-1H2v1zm9 1h1v2c-.02.28-.11.52-.3.7-.19.18-.42.28-.7.3H1c-.55 0-1-.45-1-1V4c0-.55.45-1 1-1h3c0-1.11.89-2 2-2 1.11 0 2 .89 2 2h3c.55 0 1 .45 1 1v5h-1V6H1v9h10v-2zM2 5h8c0-.55-.45-1-1-1H8c-.55 0-1-.45-1-1s-.45-1-1-1-1 .45-1 1-.45 1-1 1H3c-.55 0-1 .45-1 1z"></path></g></svg></div><button class="authorization__btn" aria-label="authorization button unlocked"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" class="unlocked" width="20" height="20" aria-hidden="true" focusable="false"><path d="M15.8 8H14V5.6C14 2.703 12.665 1 10 1 7.334 1 6 2.703 6 5.6V6h2v-.801C8 3.754 8.797 3 10 3c1.203 0 2 .754 2 2.199V8H4c-.553 0-1 .646-1 1.199V17c0 .549.428 1.139.951 1.307l1.197.387C5.672 18.861 6.55 19 7.1 19h5.8c.549 0 1.428-.139 1.951-.307l1.196-.387c.524-.167.953-.757.953-1.306V9.199C17 8.646 16.352 8 15.8 8z"></path></svg></button><button aria-label="get ​/api​/v1​/consultants​/me" class="opblock-control-arrow" aria-expanded="false" tabindex="-1"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" class="arrow" width="20" height="20" aria-hidden="true" focusable="false"><path d="M17.418 6.109c.272-.268.709-.268.979 0s.271.701 0 .969l-7.908 7.83c-.27.268-.707.268-.979 0l-7.908-7.83c-.27-.268-.27-.701 0-.969.271-.268.709-.268.979 0L10 13.25l7.418-7.141z"></path></svg></button></div><noscript></noscript></div></span><span><div class="opblock opblock-get" id="operations-Consultants-get_app_consultant_show"><div class="opblock-summary opblock-summary-get"><button aria-expanded="false" class="opblock-summary-control"><span class="opblock-summary-method">GET</span><div class="opblock-summary-path-description-wrapper"><span class="opblock-summary-path" data-path="/api/v1/consultants/{id}"><a class="nostyle"><span>/api<wbr>/v1<wbr>/consultants<wbr>/{id}</span></a></span><div class="opblock-summary-description">Get a specific consultant</div></div></button><div class="view-line-link copy-to-clipboard" title="Copy to clipboard"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 15 16" width="15" height="16" aria-hidden="true" focusable="false"><g transform="translate(2, -1)"><path fill="#ffffff" fill-rule="evenodd" d="M2 13h4v1H2v-1zm5-6H2v1h5V7zm2 3V8l-3 3 3 3v-2h5v-2H9zM4.5 9H2v1h2.5V9zM2 12h2.5v-1H2v1zm9 1h1v2c-.02.28-.11.52-.3.7-.19.18-.42.28-.7.3H1c-.55 0-1-.45-1-1V4c0-.55.45-1 1-1h3c0-1.11.89-2 2-2 1.11 0 2 .89 2 2h3c.55 0 1 .45 1 1v5h-1V6H1v9h10v-2zM2 5h8c0-.55-.45-1-1-1H8c-.55 0-1-.45-1-1s-.45-1-1-1-1 .45-1 1-.45 1-1 1H3c-.55 0-1 .45-1 1z"></path></g></svg></div><button class="authorization__btn" aria-label="authorization button unlocked"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" class="unlocked" width="20" height="20" aria-hidden="true" focusable="false"><path d="M15.8 8H14V5.6C14 2.703 12.665 1 10 1 7.334 1 6 2.703 6 5.6V6h2v-.801C8 3.754 8.797 3 10 3c1.203 0 2 .754 2 2.199V8H4c-.553 0-1 .646-1 1.199V17c0 .549.428 1.139.951 1.307l1.197.387C5.672 18.861 6.55 19 7.1 19h5.8c.549 0 1.428-.139 1.951-.307l1.196-.387c.524-.167.953-.757.953-1.306V9.199C17 8.646 16.352 8 15.8 8z"></path></svg></button><button aria-label="get ​/api​/v1​/consultants​/{id}" class="opblock-control-arrow" aria-expanded="false" tabindex="-1"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" class="arrow" width="20" height="20" aria-hidden="true" focusable="false"><path d="M17.418 6.109c.272-.268.709-.268.979 0s.271.701 0 .969l-7.908 7.83c-.27.268-.707.268-.979 0l-7.908-7.83c-.27-.268-.27-.701 0-.969.271-.268.709-.268.979 0L10 13.25l7.418-7.141z"></path></svg></button></div><noscript></noscript></div></span><span><div class="opblock opblock-put" id="operations-Consultants-put_app_consultant_update"><div class="opblock-summary opblock-summary-put"><button aria-expanded="false" class="opblock-summary-control"><span class="opblock-summary-method">PUT</span><div class="opblock-summary-path-description-wrapper"><span class="opblock-summary-path" data-path="/api/v1/consultants/{id}"><a class="nostyle"><span>/api<wbr>/v1<wbr>/consultants<wbr>/{id}</span></a></span><div class="opblock-summary-description">Update an existing consultant</div></div></button><div class="view-line-link copy-to-clipboard" title="Copy to clipboard"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 15 16" width="15" height="16" aria-hidden="true" focusable="false"><g transform="translate(2, -1)"><path fill="#ffffff" fill-rule="evenodd" d="M2 13h4v1H2v-1zm5-6H2v1h5V7zm2 3V8l-3 3 3 3v-2h5v-2H9zM4.5 9H2v1h2.5V9zM2 12h2.5v-1H2v1zm9 1h1v2c-.02.28-.11.52-.3.7-.19.18-.42.28-.7.3H1c-.55 0-1-.45-1-1V4c0-.55.45-1 1-1h3c0-1.11.89-2 2-2 1.11 0 2 .89 2 2h3c.55 0 1 .45 1 1v5h-1V6H1v9h10v-2zM2 5h8c0-.55-.45-1-1-1H8c-.55 0-1-.45-1-1s-.45-1-1-1-1 .45-1 1-.45 1-1 1H3c-.55 0-1 .45-1 1z"></path></g></svg></div><button class="authorization__btn" aria-label="authorization button unlocked"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" class="unlocked" width="20" height="20" aria-hidden="true" focusable="false"><path d="M15.8 8H14V5.6C14 2.703 12.665 1 10 1 7.334 1 6 2.703 6 5.6V6h2v-.801C8 3.754 8.797 3 10 3c1.203 0 2 .754 2 2.199V8H4c-.553 0-1 .646-1 1.199V17c0 .549.428 1.139.951 1.307l1.197.387C5.672 18.861 6.55 19 7.1 19h5.8c.549 0 1.428-.139 1.951-.307l1.196-.387c.524-.167.953-.757.953-1.306V9.199C17 8.646 16.352 8 15.8 8z"></path></svg></button><button aria-label="put ​/api​/v1​/consultants​/{id}" class="opblock-control-arrow" aria-expanded="false" tabindex="-1"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" class="arrow" width="20" height="20" aria-hidden="true" focusable="false"><path d="M17.418 6.109c.272-.268.709-.268.979 0s.271.701 0 .969l-7.908 7.83c-.27.268-.707.268-.979 0l-7.908-7.83c-.27-.268-.27-.701 0-.969.271-.268.709-.268.979 0L10 13.25l7.418-7.141z"></path></svg></button></div><noscript></noscript></div></span><span><div class="opblock opblock-delete" id="operations-Consultants-delete_app_consultant_delete"><div class="opblock-summary opblock-summary-delete"><button aria-expanded="false" class="opblock-summary-control"><span class="opblock-summary-method">DELETE</span><div class="opblock-summary-path-description-wrapper"><span class="opblock-summary-path" data-path="/api/v1/consultants/{id}"><a class="nostyle"><span>/api<wbr>/v1<wbr>/consultants<wbr>/{id}</span></a></span><div class="opblock-summary-description">Delete a consultant (admin only)</div></div></button><div class="view-line-link copy-to-clipboard" title="Copy to clipboard"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 15 16" width="15" height="16" aria-hidden="true" focusable="false"><g transform="translate(2, -1)"><path fill="#ffffff" fill-rule="evenodd" d="M2 13h4v1H2v-1zm5-6H2v1h5V7zm2 3V8l-3 3 3 3v-2h5v-2H9zM4.5 9H2v1h2.5V9zM2 12h2.5v-1H2v1zm9 1h1v2c-.02.28-.11.52-.3.7-.19.18-.42.28-.7.3H1c-.55 0-1-.45-1-1V4c0-.55.45-1 1-1h3c0-1.11.89-2 2-2 1.11 0 2 .89 2 2h3c.55 0 1 .45 1 1v5h-1V6H1v9h10v-2zM2 5h8c0-.55-.45-1-1-1H8c-.55 0-1-.45-1-1s-.45-1-1-1-1 .45-1 1-.45 1-1 1H3c-.55 0-1 .45-1 1z"></path></g></svg></div><button class="authorization__btn" aria-label="authorization button unlocked"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" class="unlocked" width="20" height="20" aria-hidden="true" focusable="false"><path d="M15.8 8H14V5.6C14 2.703 12.665 1 10 1 7.334 1 6 2.703 6 5.6V6h2v-.801C8 3.754 8.797 3 10 3c1.203 0 2 .754 2 2.199V8H4c-.553 0-1 .646-1 1.199V17c0 .549.428 1.139.951 1.307l1.197.387C5.672 18.861 6.55 19 7.1 19h5.8c.549 0 1.428-.139 1.951-.307l1.196-.387c.524-.167.953-.757.953-1.306V9.199C17 8.646 16.352 8 15.8 8z"></path></svg></button><button aria-label="delete ​/api​/v1​/consultants​/{id}" class="opblock-control-arrow" aria-expanded="false" tabindex="-1"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" class="arrow" width="20" height="20" aria-hidden="true" focusable="false"><path d="M17.418 6.109c.272-.268.709-.268.979 0s.271.701 0 .969l-7.908 7.83c-.27.268-.707.268-.979 0l-7.908-7.83c-.27-.268-.27-.701 0-.969.271-.268.709-.268.979 0L10 13.25l7.418-7.141z"></path></svg></button></div><noscript></noscript></div></span></div> </div></div></span><span><div class="opblock-tag-section is-open"><h3 class="opblock-tag" id="operations-tag-Work_Time" data-tag="Work Time" data-is-open="true"><a class="nostyle"><span>Work Time</span></a><small><div class="renderedMarkdown"><p>Work time tracking and management</p></div></small><button aria-expanded="true" class="expand-operation" title="Collapse operation"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" class="arrow" width="20" height="20" aria-hidden="true" focusable="false"><path d="M 17.418 14.908 C 17.69 15.176 18.127 15.176 18.397 14.908 C 18.667 14.64 18.668 14.207 18.397 13.939 L 10.489 6.109 C 10.219 5.841 9.782 5.841 9.51 6.109 L 1.602 13.939 C 1.332 14.207 1.332 14.64 1.602 14.908 C 1.873 15.176 2.311 15.176 2.581 14.908 L 10 7.767 L 17.418 14.908 Z"></path></svg></button></h3><div class="no-margin"> <div class="operation-tag-content"><span><div class="opblock opblock-get" id="operations-Work_Time-get_app_work_time_index"><div class="opblock-summary opblock-summary-get"><button aria-expanded="false" class="opblock-summary-control"><span class="opblock-summary-method">GET</span><div class="opblock-summary-path-description-wrapper"><span class="opblock-summary-path" data-path="/api/v1/work-time"><a class="nostyle"><span>/api<wbr>/v1<wbr>/work-time</span></a></span><div class="opblock-summary-description">Get all work time entries (admin only)</div></div></button><div class="view-line-link copy-to-clipboard" title="Copy to clipboard"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 15 16" width="15" height="16" aria-hidden="true" focusable="false"><g transform="translate(2, -1)"><path fill="#ffffff" fill-rule="evenodd" d="M2 13h4v1H2v-1zm5-6H2v1h5V7zm2 3V8l-3 3 3 3v-2h5v-2H9zM4.5 9H2v1h2.5V9zM2 12h2.5v-1H2v1zm9 1h1v2c-.02.28-.11.52-.3.7-.19.18-.42.28-.7.3H1c-.55 0-1-.45-1-1V4c0-.55.45-1 1-1h3c0-1.11.89-2 2-2 1.11 0 2 .89 2 2h3c.55 0 1 .45 1 1v5h-1V6H1v9h10v-2zM2 5h8c0-.55-.45-1-1-1H8c-.55 0-1-.45-1-1s-.45-1-1-1-1 .45-1 1-.45 1-1 1H3c-.55 0-1 .45-1 1z"></path></g></svg></div><button class="authorization__btn" aria-label="authorization button unlocked"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" class="unlocked" width="20" height="20" aria-hidden="true" focusable="false"><path d="M15.8 8H14V5.6C14 2.703 12.665 1 10 1 7.334 1 6 2.703 6 5.6V6h2v-.801C8 3.754 8.797 3 10 3c1.203 0 2 .754 2 2.199V8H4c-.553 0-1 .646-1 1.199V17c0 .549.428 1.139.951 1.307l1.197.387C5.672 18.861 6.55 19 7.1 19h5.8c.549 0 1.428-.139 1.951-.307l1.196-.387c.524-.167.953-.757.953-1.306V9.199C17 8.646 16.352 8 15.8 8z"></path></svg></button><button aria-label="get ​/api​/v1​/work-time" class="opblock-control-arrow" aria-expanded="false" tabindex="-1"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" class="arrow" width="20" height="20" aria-hidden="true" focusable="false"><path d="M17.418 6.109c.272-.268.709-.268.979 0s.271.701 0 .969l-7.908 7.83c-.27.268-.707.268-.979 0l-7.908-7.83c-.27-.268-.27-.701 0-.969.271-.268.709-.268.979 0L10 13.25l7.418-7.141z"></path></svg></button></div><noscript></noscript></div></span><span><div class="opblock opblock-post is-open" id="operations-Work_Time-post_app_work_time_create"><div class="opblock-summary opblock-summary-post"><button aria-expanded="true" class="opblock-summary-control"><span class="opblock-summary-method">POST</span><div class="opblock-summary-path-description-wrapper"><span class="opblock-summary-path" data-path="/api/v1/work-time"><a class="nostyle"><span>/api<wbr>/v1<wbr>/work-time</span></a></span><div class="opblock-summary-description">Create a new work time entry with period and activities</div></div></button><div class="view-line-link copy-to-clipboard" title="Copy to clipboard"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 15 16" width="15" height="16" aria-hidden="true" focusable="false"><g transform="translate(2, -1)"><path fill="#ffffff" fill-rule="evenodd" d="M2 13h4v1H2v-1zm5-6H2v1h5V7zm2 3V8l-3 3 3 3v-2h5v-2H9zM4.5 9H2v1h2.5V9zM2 12h2.5v-1H2v1zm9 1h1v2c-.02.28-.11.52-.3.7-.19.18-.42.28-.7.3H1c-.55 0-1-.45-1-1V4c0-.55.45-1 1-1h3c0-1.11.89-2 2-2 1.11 0 2 .89 2 2h3c.55 0 1 .45 1 1v5h-1V6H1v9h10v-2zM2 5h8c0-.55-.45-1-1-1H8c-.55 0-1-.45-1-1s-.45-1-1-1-1 .45-1 1-.45 1-1 1H3c-.55 0-1 .45-1 1z"></path></g></svg></div><button class="authorization__btn" aria-label="authorization button unlocked"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" class="unlocked" width="20" height="20" aria-hidden="true" focusable="false"><path d="M15.8 8H14V5.6C14 2.703 12.665 1 10 1 7.334 1 6 2.703 6 5.6V6h2v-.801C8 3.754 8.797 3 10 3c1.203 0 2 .754 2 2.199V8H4c-.553 0-1 .646-1 1.199V17c0 .549.428 1.139.951 1.307l1.197.387C5.672 18.861 6.55 19 7.1 19h5.8c.549 0 1.428-.139 1.951-.307l1.196-.387c.524-.167.953-.757.953-1.306V9.199C17 8.646 16.352 8 15.8 8z"></path></svg></button><button aria-label="post ​/api​/v1​/work-time" class="opblock-control-arrow" aria-expanded="true" tabindex="-1"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" class="arrow" width="20" height="20" aria-hidden="true" focusable="false"><path d="M 17.418 14.908 C 17.69 15.176 18.127 15.176 18.397 14.908 C 18.667 14.64 18.668 14.207 18.397 13.939 L 10.489 6.109 C 10.219 5.841 9.782 5.841 9.51 6.109 L 1.602 13.939 C 1.332 14.207 1.332 14.64 1.602 14.908 C 1.873 15.176 2.311 15.176 2.581 14.908 L 10 7.767 L 17.418 14.908 Z"></path></svg></button></div><div class="no-margin"> <div class="opblock-body"><div class="opblock-section"><div class="opblock-section-header"><div class="tab-header"><div class="tab-item active"><h4 class="opblock-title"><span>Parameters</span></h4></div></div><div class="try-out"><button class="btn try-out__btn">Try it out </button></div></div><div class="parameters-container"><div class="opblock-description-wrapper"><p>No parameters</p></div></div><div class="opblock-section opblock-section-request-body"><div class="opblock-section-header"><h4 class="opblock-title parameter__name required">Request body</h4><label id="post_api_v1_work-time_requests_select"><div class="content-type-wrapper body-param-content-type"><select aria-label="Request content type" class="content-type" id="post_api_v1_work-time_requests_select"><option value="application/json">application/json</option></select></div></label></div><div class="opblock-description-wrapper"><div><div class="renderedMarkdown"><p>Work time data with period and activities</p></div><div class="model-example"><ul class="tab" role="tablist"><li class="tabitem active" role="presentation"><button aria-controls="qF7J/aM=" aria-selected="true" class="tablinks" data-name="example" id="5Y7cpGk=" role="tab">Example Value</button></li><li class="tabitem" role="presentation"><button aria-controls="XjLegng=" aria-selected="false" class="tablinks" data-name="model" id="3/pSF2s=" role="tab">Schema</button></li></ul><div aria-hidden="false" aria-labelledby="5Y7cpGk=" data-name="examplePanel" id="qF7J/aM=" role="tabpanel" tabindex="0"><div class="highlight-code"><pre class="body-param__example microlight" style="display: block; overflow-x: auto; padding: 0.5em; background: rgb(51, 51, 51); color: white;"><code class="language-json" style="white-space: pre;"><span>{
</span><span>  </span><span class="hljs-attr">"consultantId"</span><span>: </span><span style="color: rgb(211, 99, 99);">1</span><span>,
</span><span>  </span><span class="hljs-attr">"startDate"</span><span>: </span><span style="color: rgb(162, 252, 162);">"2024-01-01"</span><span>,
</span><span>  </span><span class="hljs-attr">"endDate"</span><span>: </span><span style="color: rgb(162, 252, 162);">"2024-01-31"</span><span>,
</span><span>  </span><span class="hljs-attr">"totalHours"</span><span>: </span><span style="color: rgb(211, 99, 99);">168</span><span>,
</span><span>  </span><span class="hljs-attr">"remoteWorkPercentage"</span><span>: </span><span style="color: rgb(211, 99, 99);">40</span><span>,
</span><span>  </span><span class="hljs-attr">"notes"</span><span>: </span><span style="color: rgb(162, 252, 162);">"January 2024 work period - focused on API development and client meetings"</span><span>,
</span><span>  </span><span class="hljs-attr">"activities"</span><span>: [
</span>    {
<span>      </span><span class="hljs-attr">"activityName"</span><span>: </span><span style="color: rgb(162, 252, 162);">"Backend Development"</span><span>,
</span><span>      </span><span class="hljs-attr">"hours"</span><span>: </span><span style="color: rgb(211, 99, 99);">120</span><span>,
</span><span>      </span><span class="hljs-attr">"description"</span><span>: </span><span style="color: rgb(162, 252, 162);">"REST API development and database optimization"</span><span>,
</span><span>      </span><span class="hljs-attr">"isBillable"</span><span>: </span><span style="color: rgb(252, 194, 140);">true</span><span>,
</span><span>      </span><span class="hljs-attr">"clientId"</span><span>: </span><span style="color: rgb(211, 99, 99);">1</span><span>
</span>    }
  ]
}</code></pre></div></div></div></div></div></div></div><div class="execute-wrapper"></div><div class="responses-wrapper"><div class="opblock-section-header"><h4>Responses</h4></div><div class="responses-inner"><table aria-live="polite" class="responses-table" id="post_api_v1_work-time_responses" role="region"><thead><tr class="responses-header"><td class="col_header response-col_status">Code</td><td class="col_header response-col_description">Description</td><td class="col col_header response-col_links">Links</td></tr></thead><tbody><tr class="response " data-code="201"><td class="response-col_status">201</td><td class="response-col_description"><div class="response-col_description__inner"><div class="renderedMarkdown"><p>Work time entry created successfully</p></div></div><section class="response-controls"><div class="response-control-media-type response-control-media-type--accept-controller"><small class="response-control-media-type__title">Media type</small><div class="content-type-wrapper "><select aria-label="Media Type" class="content-type"><option value="application/json">application/json</option></select></div><small class="response-control-media-type__accept-message">Controls <code>Accept</code> header.</small></div></section><div class="model-example"><ul class="tab" role="tablist"><li class="tabitem active" role="presentation"><button aria-controls="JInYwDk=" aria-selected="true" class="tablinks" data-name="example" id="e2miBs4=" role="tab">Example Value</button></li><li class="tabitem" role="presentation"><button aria-controls="Onz6ApI=" aria-selected="false" class="tablinks" data-name="model" id="ZrtNBrI=" role="tab">Schema</button></li></ul><div aria-hidden="false" aria-labelledby="e2miBs4=" data-name="examplePanel" id="JInYwDk=" role="tabpanel" tabindex="0"><div><div class="highlight-code"><pre class="example microlight" style="display: block; overflow-x: auto; padding: 0.5em; background: rgb(51, 51, 51); color: white;"><code class="language-json" style="white-space: pre;"><span>{
</span><span>  </span><span class="hljs-attr">"id"</span><span>: </span><span style="color: rgb(211, 99, 99);">1</span><span>,
</span><span>  </span><span class="hljs-attr">"startDate"</span><span>: </span><span style="color: rgb(162, 252, 162);">"2024-01-01"</span><span>,
</span><span>  </span><span class="hljs-attr">"endDate"</span><span>: </span><span style="color: rgb(162, 252, 162);">"2024-01-31"</span><span>,
</span><span>  </span><span class="hljs-attr">"totalHours"</span><span>: </span><span style="color: rgb(211, 99, 99);">168</span><span>,
</span><span>  </span><span class="hljs-attr">"remoteWorkPercentage"</span><span>: </span><span style="color: rgb(211, 99, 99);">40</span><span>,
</span><span>  </span><span class="hljs-attr">"notes"</span><span>: </span><span style="color: rgb(162, 252, 162);">"January 2024 work period - focused on API development and client meetings"</span><span>,
</span><span>  </span><span class="hljs-attr">"isValidated"</span><span>: </span><span style="color: rgb(252, 194, 140);">true</span><span>,
</span><span>  </span><span class="hljs-attr">"validationStatus"</span><span>: </span><span style="color: rgb(162, 252, 162);">"Validated"</span><span>,
</span><span>  </span><span class="hljs-attr">"periodIdentifier"</span><span>: </span><span style="color: rgb(162, 252, 162);">"2024-01-01 to 2024-01-31"</span><span>,
</span><span>  </span><span class="hljs-attr">"durationInDays"</span><span>: </span><span style="color: rgb(211, 99, 99);">31</span><span>,
</span><span>  </span><span class="hljs-attr">"activities"</span><span>: [
</span>    {
<span>      </span><span class="hljs-attr">"id"</span><span>: </span><span style="color: rgb(211, 99, 99);">1</span><span>,
</span><span>      </span><span class="hljs-attr">"activityName"</span><span>: </span><span style="color: rgb(162, 252, 162);">"Backend Development"</span><span>,
</span><span>      </span><span class="hljs-attr">"hours"</span><span>: </span><span style="color: rgb(211, 99, 99);">120</span><span>,
</span><span>      </span><span class="hljs-attr">"description"</span><span>: </span><span style="color: rgb(162, 252, 162);">"REST API development and database optimization"</span><span>,
</span><span>      </span><span class="hljs-attr">"isBillable"</span><span>: </span><span style="color: rgb(252, 194, 140);">true</span><span>,
</span><span>      </span><span class="hljs-attr">"percentageOfTotal"</span><span>: </span><span style="color: rgb(211, 99, 99);">71.4</span><span>,
</span><span>      </span><span class="hljs-attr">"client"</span><span>: {
</span><span>        </span><span class="hljs-attr">"id"</span><span>: </span><span style="color: rgb(211, 99, 99);">1</span><span>,
</span><span>        </span><span class="hljs-attr">"name"</span><span>: </span><span style="color: rgb(162, 252, 162);">"TechCorp Solutions"</span><span>,
</span><span>        </span><span class="hljs-attr">"code"</span><span>: </span><span style="color: rgb(162, 252, 162);">"TECH"</span><span>
</span>      }
    }
  ],
<span>  </span><span class="hljs-attr">"consultant"</span><span>: {
</span><span>    </span><span class="hljs-attr">"id"</span><span>: </span><span style="color: rgb(211, 99, 99);">1</span><span>,
</span><span>    </span><span class="hljs-attr">"firstName"</span><span>: </span><span style="color: rgb(162, 252, 162);">"Marie"</span><span>,
</span><span>    </span><span class="hljs-attr">"lastName"</span><span>: </span><span style="color: rgb(162, 252, 162);">"Dubois"</span><span>
</span>  }
}</code></pre></div></div></div></div></td><td class="response-col_links"><i>No links</i></td></tr><tr class="response " data-code="400"><td class="response-col_status">400</td><td class="response-col_description"><div class="response-col_description__inner"><div class="renderedMarkdown"><p>Invalid input data</p></div></div><section class="response-controls"><div class="response-control-media-type"><small class="response-control-media-type__title">Media type</small><div class="content-type-wrapper "><select aria-label="Media Type" class="content-type"><option value="application/json">application/json</option></select></div></div></section><div class="model-example"><ul class="tab" role="tablist"><li class="tabitem active" role="presentation"><button aria-controls="d+/S4dE=" aria-selected="true" class="tablinks" data-name="example" id="jJVu5c8=" role="tab">Example Value</button></li><li class="tabitem" role="presentation"><button aria-controls="CanZkJ0=" aria-selected="false" class="tablinks" data-name="model" id="sRSniR4=" role="tab">Schema</button></li></ul><div aria-hidden="false" aria-labelledby="jJVu5c8=" data-name="examplePanel" id="d+/S4dE=" role="tabpanel" tabindex="0"><div><div class="highlight-code"><pre class="example microlight" style="display: block; overflow-x: auto; padding: 0.5em; background: rgb(51, 51, 51); color: white;"><code class="language-json" style="white-space: pre;"><span>{
</span><span>  </span><span class="hljs-attr">"error"</span><span>: </span><span style="color: rgb(162, 252, 162);">"Start date cannot be after end date"</span><span>,
</span><span>  </span><span class="hljs-attr">"success"</span><span>: </span><span style="color: rgb(252, 194, 140);">false</span><span>
</span>}</code></pre></div></div></div></div></td><td class="response-col_links"><i>No links</i></td></tr><tr class="response " data-code="403"><td class="response-col_status">403</td><td class="response-col_description"><div class="response-col_description__inner"><div class="renderedMarkdown"><p>Access denied</p></div></div><section class="response-controls"><div class="response-control-media-type"><small class="response-control-media-type__title">Media type</small><div class="content-type-wrapper "><select aria-label="Media Type" class="content-type"><option value="application/json">application/json</option></select></div></div></section><div class="model-example"><ul class="tab" role="tablist"><li class="tabitem active" role="presentation"><button aria-controls="uVAQYyM=" aria-selected="true" class="tablinks" data-name="example" id="8NwKBLs=" role="tab">Example Value</button></li><li class="tabitem" role="presentation"><button aria-controls="0CB4KrQ=" aria-selected="false" class="tablinks" data-name="model" id="mVYvqCE=" role="tab">Schema</button></li></ul><div aria-hidden="false" aria-labelledby="8NwKBLs=" data-name="examplePanel" id="uVAQYyM=" role="tabpanel" tabindex="0"><div><div class="highlight-code"><pre class="example microlight" style="display: block; overflow-x: auto; padding: 0.5em; background: rgb(51, 51, 51); color: white;"><code class="language-json" style="white-space: pre;"><span>{
</span><span>  </span><span class="hljs-attr">"error"</span><span>: </span><span style="color: rgb(162, 252, 162);">"You must be logged in as a consultant to create work time entries"</span><span>
</span>}</code></pre></div></div></div></div></td><td class="response-col_links"><i>No links</i></td></tr></tbody></table></div></div></div> </div></div></span><span><div class="opblock opblock-get" id="operations-Work_Time-get_app_work_time_show"><div class="opblock-summary opblock-summary-get"><button aria-expanded="false" class="opblock-summary-control"><span class="opblock-summary-method">GET</span><div class="opblock-summary-path-description-wrapper"><span class="opblock-summary-path" data-path="/api/v1/work-time/{id}"><a class="nostyle"><span>/api<wbr>/v1<wbr>/work-time<wbr>/{id}</span></a></span><div class="opblock-summary-description">Get a specific work time entry</div></div></button><div class="view-line-link copy-to-clipboard" title="Copy to clipboard"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 15 16" width="15" height="16" aria-hidden="true" focusable="false"><g transform="translate(2, -1)"><path fill="#ffffff" fill-rule="evenodd" d="M2 13h4v1H2v-1zm5-6H2v1h5V7zm2 3V8l-3 3 3 3v-2h5v-2H9zM4.5 9H2v1h2.5V9zM2 12h2.5v-1H2v1zm9 1h1v2c-.02.28-.11.52-.3.7-.19.18-.42.28-.7.3H1c-.55 0-1-.45-1-1V4c0-.55.45-1 1-1h3c0-1.11.89-2 2-2 1.11 0 2 .89 2 2h3c.55 0 1 .45 1 1v5h-1V6H1v9h10v-2zM2 5h8c0-.55-.45-1-1-1H8c-.55 0-1-.45-1-1s-.45-1-1-1-1 .45-1 1-.45 1-1 1H3c-.55 0-1 .45-1 1z"></path></g></svg></div><button class="authorization__btn" aria-label="authorization button unlocked"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" class="unlocked" width="20" height="20" aria-hidden="true" focusable="false"><path d="M15.8 8H14V5.6C14 2.703 12.665 1 10 1 7.334 1 6 2.703 6 5.6V6h2v-.801C8 3.754 8.797 3 10 3c1.203 0 2 .754 2 2.199V8H4c-.553 0-1 .646-1 1.199V17c0 .549.428 1.139.951 1.307l1.197.387C5.672 18.861 6.55 19 7.1 19h5.8c.549 0 1.428-.139 1.951-.307l1.196-.387c.524-.167.953-.757.953-1.306V9.199C17 8.646 16.352 8 15.8 8z"></path></svg></button><button aria-label="get ​/api​/v1​/work-time​/{id}" class="opblock-control-arrow" aria-expanded="false" tabindex="-1"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" class="arrow" width="20" height="20" aria-hidden="true" focusable="false"><path d="M17.418 6.109c.272-.268.709-.268.979 0s.271.701 0 .969l-7.908 7.83c-.27.268-.707.268-.979 0l-7.908-7.83c-.27-.268-.27-.701 0-.969.271-.268.709-.268.979 0L10 13.25l7.418-7.141z"></path></svg></button></div><noscript></noscript></div></span><span><div class="opblock opblock-put" id="operations-Work_Time-put_app_work_time_update"><div class="opblock-summary opblock-summary-put"><button aria-expanded="false" class="opblock-summary-control"><span class="opblock-summary-method">PUT</span><div class="opblock-summary-path-description-wrapper"><span class="opblock-summary-path" data-path="/api/v1/work-time/{id}"><a class="nostyle"><span>/api<wbr>/v1<wbr>/work-time<wbr>/{id}</span></a></span><div class="opblock-summary-description">Update an existing work time entry</div></div></button><div class="view-line-link copy-to-clipboard" title="Copy to clipboard"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 15 16" width="15" height="16" aria-hidden="true" focusable="false"><g transform="translate(2, -1)"><path fill="#ffffff" fill-rule="evenodd" d="M2 13h4v1H2v-1zm5-6H2v1h5V7zm2 3V8l-3 3 3 3v-2h5v-2H9zM4.5 9H2v1h2.5V9zM2 12h2.5v-1H2v1zm9 1h1v2c-.02.28-.11.52-.3.7-.19.18-.42.28-.7.3H1c-.55 0-1-.45-1-1V4c0-.55.45-1 1-1h3c0-1.11.89-2 2-2 1.11 0 2 .89 2 2h3c.55 0 1 .45 1 1v5h-1V6H1v9h10v-2zM2 5h8c0-.55-.45-1-1-1H8c-.55 0-1-.45-1-1s-.45-1-1-1-1 .45-1 1-.45 1-1 1H3c-.55 0-1 .45-1 1z"></path></g></svg></div><button class="authorization__btn" aria-label="authorization button unlocked"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" class="unlocked" width="20" height="20" aria-hidden="true" focusable="false"><path d="M15.8 8H14V5.6C14 2.703 12.665 1 10 1 7.334 1 6 2.703 6 5.6V6h2v-.801C8 3.754 8.797 3 10 3c1.203 0 2 .754 2 2.199V8H4c-.553 0-1 .646-1 1.199V17c0 .549.428 1.139.951 1.307l1.197.387C5.672 18.861 6.55 19 7.1 19h5.8c.549 0 1.428-.139 1.951-.307l1.196-.387c.524-.167.953-.757.953-1.306V9.199C17 8.646 16.352 8 15.8 8z"></path></svg></button><button aria-label="put ​/api​/v1​/work-time​/{id}" class="opblock-control-arrow" aria-expanded="false" tabindex="-1"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" class="arrow" width="20" height="20" aria-hidden="true" focusable="false"><path d="M17.418 6.109c.272-.268.709-.268.979 0s.271.701 0 .969l-7.908 7.83c-.27.268-.707.268-.979 0l-7.908-7.83c-.27-.268-.27-.701 0-.969.271-.268.709-.268.979 0L10 13.25l7.418-7.141z"></path></svg></button></div><noscript></noscript></div></span><span><div class="opblock opblock-delete" id="operations-Work_Time-delete_app_work_time_delete"><div class="opblock-summary opblock-summary-delete"><button aria-expanded="false" class="opblock-summary-control"><span class="opblock-summary-method">DELETE</span><div class="opblock-summary-path-description-wrapper"><span class="opblock-summary-path" data-path="/api/v1/work-time/{id}"><a class="nostyle"><span>/api<wbr>/v1<wbr>/work-time<wbr>/{id}</span></a></span><div class="opblock-summary-description">Delete a work time entry</div></div></button><div class="view-line-link copy-to-clipboard" title="Copy to clipboard"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 15 16" width="15" height="16" aria-hidden="true" focusable="false"><g transform="translate(2, -1)"><path fill="#ffffff" fill-rule="evenodd" d="M2 13h4v1H2v-1zm5-6H2v1h5V7zm2 3V8l-3 3 3 3v-2h5v-2H9zM4.5 9H2v1h2.5V9zM2 12h2.5v-1H2v1zm9 1h1v2c-.02.28-.11.52-.3.7-.19.18-.42.28-.7.3H1c-.55 0-1-.45-1-1V4c0-.55.45-1 1-1h3c0-1.11.89-2 2-2 1.11 0 2 .89 2 2h3c.55 0 1 .45 1 1v5h-1V6H1v9h10v-2zM2 5h8c0-.55-.45-1-1-1H8c-.55 0-1-.45-1-1s-.45-1-1-1-1 .45-1 1-.45 1-1 1H3c-.55 0-1 .45-1 1z"></path></g></svg></div><button class="authorization__btn" aria-label="authorization button unlocked"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" class="unlocked" width="20" height="20" aria-hidden="true" focusable="false"><path d="M15.8 8H14V5.6C14 2.703 12.665 1 10 1 7.334 1 6 2.703 6 5.6V6h2v-.801C8 3.754 8.797 3 10 3c1.203 0 2 .754 2 2.199V8H4c-.553 0-1 .646-1 1.199V17c0 .549.428 1.139.951 1.307l1.197.387C5.672 18.861 6.55 19 7.1 19h5.8c.549 0 1.428-.139 1.951-.307l1.196-.387c.524-.167.953-.757.953-1.306V9.199C17 8.646 16.352 8 15.8 8z"></path></svg></button><button aria-label="delete ​/api​/v1​/work-time​/{id}" class="opblock-control-arrow" aria-expanded="false" tabindex="-1"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" class="arrow" width="20" height="20" aria-hidden="true" focusable="false"><path d="M17.418 6.109c.272-.268.709-.268.979 0s.271.701 0 .969l-7.908 7.83c-.27.268-.707.268-.979 0l-7.908-7.83c-.27-.268-.27-.701 0-.969.271-.268.709-.268.979 0L10 13.25l7.418-7.141z"></path></svg></button></div><noscript></noscript></div></span><span><div class="opblock opblock-get" id="operations-Work_Time-get_app_work_time_by_consultant"><div class="opblock-summary opblock-summary-get"><button aria-expanded="false" class="opblock-summary-control"><span class="opblock-summary-method">GET</span><div class="opblock-summary-path-description-wrapper"><span class="opblock-summary-path" data-path="/api/v1/work-time/consultant/{consultantId}"><a class="nostyle"><span>/api<wbr>/v1<wbr>/work-time<wbr>/consultant<wbr>/{consultantId}</span></a></span><div class="opblock-summary-description">Get work time entries for a specific consultant</div></div></button><div class="view-line-link copy-to-clipboard" title="Copy to clipboard"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 15 16" width="15" height="16" aria-hidden="true" focusable="false"><g transform="translate(2, -1)"><path fill="#ffffff" fill-rule="evenodd" d="M2 13h4v1H2v-1zm5-6H2v1h5V7zm2 3V8l-3 3 3 3v-2h5v-2H9zM4.5 9H2v1h2.5V9zM2 12h2.5v-1H2v1zm9 1h1v2c-.02.28-.11.52-.3.7-.19.18-.42.28-.7.3H1c-.55 0-1-.45-1-1V4c0-.55.45-1 1-1h3c0-1.11.89-2 2-2 1.11 0 2 .89 2 2h3c.55 0 1 .45 1 1v5h-1V6H1v9h10v-2zM2 5h8c0-.55-.45-1-1-1H8c-.55 0-1-.45-1-1s-.45-1-1-1-1 .45-1 1-.45 1-1 1H3c-.55 0-1 .45-1 1z"></path></g></svg></div><button class="authorization__btn" aria-label="authorization button unlocked"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" class="unlocked" width="20" height="20" aria-hidden="true" focusable="false"><path d="M15.8 8H14V5.6C14 2.703 12.665 1 10 1 7.334 1 6 2.703 6 5.6V6h2v-.801C8 3.754 8.797 3 10 3c1.203 0 2 .754 2 2.199V8H4c-.553 0-1 .646-1 1.199V17c0 .549.428 1.139.951 1.307l1.197.387C5.672 18.861 6.55 19 7.1 19h5.8c.549 0 1.428-.139 1.951-.307l1.196-.387c.524-.167.953-.757.953-1.306V9.199C17 8.646 16.352 8 15.8 8z"></path></svg></button><button aria-label="get ​/api​/v1​/work-time​/consultant​/{consultantId}" class="opblock-control-arrow" aria-expanded="false" tabindex="-1"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" class="arrow" width="20" height="20" aria-hidden="true" focusable="false"><path d="M17.418 6.109c.272-.268.709-.268.979 0s.271.701 0 .969l-7.908 7.83c-.27.268-.707.268-.979 0l-7.908-7.83c-.27-.268-.27-.701 0-.969.271-.268.709-.268.979 0L10 13.25l7.418-7.141z"></path></svg></button></div><noscript></noscript></div></span><span><div class="opblock opblock-get" id="operations-Work_Time-get_app_work_time_statistics"><div class="opblock-summary opblock-summary-get"><button aria-expanded="false" class="opblock-summary-control"><span class="opblock-summary-method">GET</span><div class="opblock-summary-path-description-wrapper"><span class="opblock-summary-path" data-path="/api/v1/work-time/statistics/{consultantId}"><a class="nostyle"><span>/api<wbr>/v1<wbr>/work-time<wbr>/statistics<wbr>/{consultantId}</span></a></span><div class="opblock-summary-description">Get work time statistics for a consultant</div></div></button><div class="view-line-link copy-to-clipboard" title="Copy to clipboard"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 15 16" width="15" height="16" aria-hidden="true" focusable="false"><g transform="translate(2, -1)"><path fill="#ffffff" fill-rule="evenodd" d="M2 13h4v1H2v-1zm5-6H2v1h5V7zm2 3V8l-3 3 3 3v-2h5v-2H9zM4.5 9H2v1h2.5V9zM2 12h2.5v-1H2v1zm9 1h1v2c-.02.28-.11.52-.3.7-.19.18-.42.28-.7.3H1c-.55 0-1-.45-1-1V4c0-.55.45-1 1-1h3c0-1.11.89-2 2-2 1.11 0 2 .89 2 2h3c.55 0 1 .45 1 1v5h-1V6H1v9h10v-2zM2 5h8c0-.55-.45-1-1-1H8c-.55 0-1-.45-1-1s-.45-1-1-1-1 .45-1 1-.45 1-1 1H3c-.55 0-1 .45-1 1z"></path></g></svg></div><button class="authorization__btn" aria-label="authorization button unlocked"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" class="unlocked" width="20" height="20" aria-hidden="true" focusable="false"><path d="M15.8 8H14V5.6C14 2.703 12.665 1 10 1 7.334 1 6 2.703 6 5.6V6h2v-.801C8 3.754 8.797 3 10 3c1.203 0 2 .754 2 2.199V8H4c-.553 0-1 .646-1 1.199V17c0 .549.428 1.139.951 1.307l1.197.387C5.672 18.861 6.55 19 7.1 19h5.8c.549 0 1.428-.139 1.951-.307l1.196-.387c.524-.167.953-.757.953-1.306V9.199C17 8.646 16.352 8 15.8 8z"></path></svg></button><button aria-label="get ​/api​/v1​/work-time​/statistics​/{consultantId}" class="opblock-control-arrow" aria-expanded="false" tabindex="-1"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" class="arrow" width="20" height="20" aria-hidden="true" focusable="false"><path d="M17.418 6.109c.272-.268.709-.268.979 0s.271.701 0 .969l-7.908 7.83c-.27.268-.707.268-.979 0l-7.908-7.83c-.27-.268-.27-.701 0-.969.271-.268.709-.268.979 0L10 13.25l7.418-7.141z"></path></svg></button></div><noscript></noscript></div></span><span><div class="opblock opblock-post" id="operations-Work_Time-post_app_work_time_validate"><div class="opblock-summary opblock-summary-post"><button aria-expanded="false" class="opblock-summary-control"><span class="opblock-summary-method">POST</span><div class="opblock-summary-path-description-wrapper"><span class="opblock-summary-path" data-path="/api/v1/work-time/{id}/validate"><a class="nostyle"><span>/api<wbr>/v1<wbr>/work-time<wbr>/{id}<wbr>/validate</span></a></span><div class="opblock-summary-description">Validate a work time entry (admin only)</div></div></button><div class="view-line-link copy-to-clipboard" title="Copy to clipboard"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 15 16" width="15" height="16" aria-hidden="true" focusable="false"><g transform="translate(2, -1)"><path fill="#ffffff" fill-rule="evenodd" d="M2 13h4v1H2v-1zm5-6H2v1h5V7zm2 3V8l-3 3 3 3v-2h5v-2H9zM4.5 9H2v1h2.5V9zM2 12h2.5v-1H2v1zm9 1h1v2c-.02.28-.11.52-.3.7-.19.18-.42.28-.7.3H1c-.55 0-1-.45-1-1V4c0-.55.45-1 1-1h3c0-1.11.89-2 2-2 1.11 0 2 .89 2 2h3c.55 0 1 .45 1 1v5h-1V6H1v9h10v-2zM2 5h8c0-.55-.45-1-1-1H8c-.55 0-1-.45-1-1s-.45-1-1-1-1 .45-1 1-.45 1-1 1H3c-.55 0-1 .45-1 1z"></path></g></svg></div><button class="authorization__btn" aria-label="authorization button unlocked"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" class="unlocked" width="20" height="20" aria-hidden="true" focusable="false"><path d="M15.8 8H14V5.6C14 2.703 12.665 1 10 1 7.334 1 6 2.703 6 5.6V6h2v-.801C8 3.754 8.797 3 10 3c1.203 0 2 .754 2 2.199V8H4c-.553 0-1 .646-1 1.199V17c0 .549.428 1.139.951 1.307l1.197.387C5.672 18.861 6.55 19 7.1 19h5.8c.549 0 1.428-.139 1.951-.307l1.196-.387c.524-.167.953-.757.953-1.306V9.199C17 8.646 16.352 8 15.8 8z"></path></svg></button><button aria-label="post ​/api​/v1​/work-time​/{id}​/validate" class="opblock-control-arrow" aria-expanded="false" tabindex="-1"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" class="arrow" width="20" height="20" aria-hidden="true" focusable="false"><path d="M17.418 6.109c.272-.268.709-.268.979 0s.271.701 0 .969l-7.908 7.83c-.27.268-.707.268-.979 0l-7.908-7.83c-.27-.268-.27-.701 0-.969.271-.268.709-.268.979 0L10 13.25l7.418-7.141z"></path></svg></button></div><noscript></noscript></div></span></div> </div></div></span><span><div class="opblock-tag-section is-open"><h3 class="opblock-tag" id="operations-tag-Expenses" data-tag="Expenses" data-is-open="true"><a class="nostyle"><span>Expenses</span></a><small><div class="renderedMarkdown"><p>Expense reporting and management</p></div></small><button aria-expanded="true" class="expand-operation" title="Collapse operation"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" class="arrow" width="20" height="20" aria-hidden="true" focusable="false"><path d="M 17.418 14.908 C 17.69 15.176 18.127 15.176 18.397 14.908 C 18.667 14.64 18.668 14.207 18.397 13.939 L 10.489 6.109 C 10.219 5.841 9.782 5.841 9.51 6.109 L 1.602 13.939 C 1.332 14.207 1.332 14.64 1.602 14.908 C 1.873 15.176 2.311 15.176 2.581 14.908 L 10 7.767 L 17.418 14.908 Z"></path></svg></button></h3><div class="no-margin"> <div class="operation-tag-content"><span><div class="opblock opblock-get" id="operations-Expenses-get_app_expense_index"><div class="opblock-summary opblock-summary-get"><button aria-expanded="false" class="opblock-summary-control"><span class="opblock-summary-method">GET</span><div class="opblock-summary-path-description-wrapper"><span class="opblock-summary-path" data-path="/api/v1/expenses"><a class="nostyle"><span>/api<wbr>/v1<wbr>/expenses</span></a></span><div class="opblock-summary-description">Get all expenses (admin only)</div></div></button><div class="view-line-link copy-to-clipboard" title="Copy to clipboard"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 15 16" width="15" height="16" aria-hidden="true" focusable="false"><g transform="translate(2, -1)"><path fill="#ffffff" fill-rule="evenodd" d="M2 13h4v1H2v-1zm5-6H2v1h5V7zm2 3V8l-3 3 3 3v-2h5v-2H9zM4.5 9H2v1h2.5V9zM2 12h2.5v-1H2v1zm9 1h1v2c-.02.28-.11.52-.3.7-.19.18-.42.28-.7.3H1c-.55 0-1-.45-1-1V4c0-.55.45-1 1-1h3c0-1.11.89-2 2-2 1.11 0 2 .89 2 2h3c.55 0 1 .45 1 1v5h-1V6H1v9h10v-2zM2 5h8c0-.55-.45-1-1-1H8c-.55 0-1-.45-1-1s-.45-1-1-1-1 .45-1 1-.45 1-1 1H3c-.55 0-1 .45-1 1z"></path></g></svg></div><button class="authorization__btn" aria-label="authorization button unlocked"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" class="unlocked" width="20" height="20" aria-hidden="true" focusable="false"><path d="M15.8 8H14V5.6C14 2.703 12.665 1 10 1 7.334 1 6 2.703 6 5.6V6h2v-.801C8 3.754 8.797 3 10 3c1.203 0 2 .754 2 2.199V8H4c-.553 0-1 .646-1 1.199V17c0 .549.428 1.139.951 1.307l1.197.387C5.672 18.861 6.55 19 7.1 19h5.8c.549 0 1.428-.139 1.951-.307l1.196-.387c.524-.167.953-.757.953-1.306V9.199C17 8.646 16.352 8 15.8 8z"></path></svg></button><button aria-label="get ​/api​/v1​/expenses" class="opblock-control-arrow" aria-expanded="false" tabindex="-1"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" class="arrow" width="20" height="20" aria-hidden="true" focusable="false"><path d="M17.418 6.109c.272-.268.709-.268.979 0s.271.701 0 .969l-7.908 7.83c-.27.268-.707.268-.979 0l-7.908-7.83c-.27-.268-.27-.701 0-.969.271-.268.709-.268.979 0L10 13.25l7.418-7.141z"></path></svg></button></div><noscript></noscript></div></span><span><div class="opblock opblock-post" id="operations-Expenses-post_app_expense_create"><div class="opblock-summary opblock-summary-post"><button aria-expanded="false" class="opblock-summary-control"><span class="opblock-summary-method">POST</span><div class="opblock-summary-path-description-wrapper"><span class="opblock-summary-path" data-path="/api/v1/expenses"><a class="nostyle"><span>/api<wbr>/v1<wbr>/expenses</span></a></span><div class="opblock-summary-description">Create a new expense</div></div></button><div class="view-line-link copy-to-clipboard" title="Copy to clipboard"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 15 16" width="15" height="16" aria-hidden="true" focusable="false"><g transform="translate(2, -1)"><path fill="#ffffff" fill-rule="evenodd" d="M2 13h4v1H2v-1zm5-6H2v1h5V7zm2 3V8l-3 3 3 3v-2h5v-2H9zM4.5 9H2v1h2.5V9zM2 12h2.5v-1H2v1zm9 1h1v2c-.02.28-.11.52-.3.7-.19.18-.42.28-.7.3H1c-.55 0-1-.45-1-1V4c0-.55.45-1 1-1h3c0-1.11.89-2 2-2 1.11 0 2 .89 2 2h3c.55 0 1 .45 1 1v5h-1V6H1v9h10v-2zM2 5h8c0-.55-.45-1-1-1H8c-.55 0-1-.45-1-1s-.45-1-1-1-1 .45-1 1-.45 1-1 1H3c-.55 0-1 .45-1 1z"></path></g></svg></div><button class="authorization__btn" aria-label="authorization button unlocked"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" class="unlocked" width="20" height="20" aria-hidden="true" focusable="false"><path d="M15.8 8H14V5.6C14 2.703 12.665 1 10 1 7.334 1 6 2.703 6 5.6V6h2v-.801C8 3.754 8.797 3 10 3c1.203 0 2 .754 2 2.199V8H4c-.553 0-1 .646-1 1.199V17c0 .549.428 1.139.951 1.307l1.197.387C5.672 18.861 6.55 19 7.1 19h5.8c.549 0 1.428-.139 1.951-.307l1.196-.387c.524-.167.953-.757.953-1.306V9.199C17 8.646 16.352 8 15.8 8z"></path></svg></button><button aria-label="post ​/api​/v1​/expenses" class="opblock-control-arrow" aria-expanded="false" tabindex="-1"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" class="arrow" width="20" height="20" aria-hidden="true" focusable="false"><path d="M17.418 6.109c.272-.268.709-.268.979 0s.271.701 0 .969l-7.908 7.83c-.27.268-.707.268-.979 0l-7.908-7.83c-.27-.268-.27-.701 0-.969.271-.268.709-.268.979 0L10 13.25l7.418-7.141z"></path></svg></button></div><noscript></noscript></div></span><span><div class="opblock opblock-get" id="operations-Expenses-get_app_expense_non_validated"><div class="opblock-summary opblock-summary-get"><button aria-expanded="false" class="opblock-summary-control"><span class="opblock-summary-method">GET</span><div class="opblock-summary-path-description-wrapper"><span class="opblock-summary-path" data-path="/api/v1/expenses/non-validated"><a class="nostyle"><span>/api<wbr>/v1<wbr>/expenses<wbr>/non-validated</span></a></span><div class="opblock-summary-description">Get all non-validated expenses (admin only)</div></div></button><div class="view-line-link copy-to-clipboard" title="Copy to clipboard"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 15 16" width="15" height="16" aria-hidden="true" focusable="false"><g transform="translate(2, -1)"><path fill="#ffffff" fill-rule="evenodd" d="M2 13h4v1H2v-1zm5-6H2v1h5V7zm2 3V8l-3 3 3 3v-2h5v-2H9zM4.5 9H2v1h2.5V9zM2 12h2.5v-1H2v1zm9 1h1v2c-.02.28-.11.52-.3.7-.19.18-.42.28-.7.3H1c-.55 0-1-.45-1-1V4c0-.55.45-1 1-1h3c0-1.11.89-2 2-2 1.11 0 2 .89 2 2h3c.55 0 1 .45 1 1v5h-1V6H1v9h10v-2zM2 5h8c0-.55-.45-1-1-1H8c-.55 0-1-.45-1-1s-.45-1-1-1-1 .45-1 1-.45 1-1 1H3c-.55 0-1 .45-1 1z"></path></g></svg></div><button class="authorization__btn" aria-label="authorization button unlocked"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" class="unlocked" width="20" height="20" aria-hidden="true" focusable="false"><path d="M15.8 8H14V5.6C14 2.703 12.665 1 10 1 7.334 1 6 2.703 6 5.6V6h2v-.801C8 3.754 8.797 3 10 3c1.203 0 2 .754 2 2.199V8H4c-.553 0-1 .646-1 1.199V17c0 .549.428 1.139.951 1.307l1.197.387C5.672 18.861 6.55 19 7.1 19h5.8c.549 0 1.428-.139 1.951-.307l1.196-.387c.524-.167.953-.757.953-1.306V9.199C17 8.646 16.352 8 15.8 8z"></path></svg></button><button aria-label="get ​/api​/v1​/expenses​/non-validated" class="opblock-control-arrow" aria-expanded="false" tabindex="-1"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" class="arrow" width="20" height="20" aria-hidden="true" focusable="false"><path d="M17.418 6.109c.272-.268.709-.268.979 0s.271.701 0 .969l-7.908 7.83c-.27.268-.707.268-.979 0l-7.908-7.83c-.27-.268-.27-.701 0-.969.271-.268.709-.268.979 0L10 13.25l7.418-7.141z"></path></svg></button></div><noscript></noscript></div></span><span><div class="opblock opblock-get" id="operations-Expenses-get_app_expense_my"><div class="opblock-summary opblock-summary-get"><button aria-expanded="false" class="opblock-summary-control"><span class="opblock-summary-method">GET</span><div class="opblock-summary-path-description-wrapper"><span class="opblock-summary-path" data-path="/api/v1/expenses/my"><a class="nostyle"><span>/api<wbr>/v1<wbr>/expenses<wbr>/my</span></a></span><div class="opblock-summary-description">Get all expenses for the current user</div></div></button><div class="view-line-link copy-to-clipboard" title="Copy to clipboard"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 15 16" width="15" height="16" aria-hidden="true" focusable="false"><g transform="translate(2, -1)"><path fill="#ffffff" fill-rule="evenodd" d="M2 13h4v1H2v-1zm5-6H2v1h5V7zm2 3V8l-3 3 3 3v-2h5v-2H9zM4.5 9H2v1h2.5V9zM2 12h2.5v-1H2v1zm9 1h1v2c-.02.28-.11.52-.3.7-.19.18-.42.28-.7.3H1c-.55 0-1-.45-1-1V4c0-.55.45-1 1-1h3c0-1.11.89-2 2-2 1.11 0 2 .89 2 2h3c.55 0 1 .45 1 1v5h-1V6H1v9h10v-2zM2 5h8c0-.55-.45-1-1-1H8c-.55 0-1-.45-1-1s-.45-1-1-1-1 .45-1 1-.45 1-1 1H3c-.55 0-1 .45-1 1z"></path></g></svg></div><button class="authorization__btn" aria-label="authorization button unlocked"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" class="unlocked" width="20" height="20" aria-hidden="true" focusable="false"><path d="M15.8 8H14V5.6C14 2.703 12.665 1 10 1 7.334 1 6 2.703 6 5.6V6h2v-.801C8 3.754 8.797 3 10 3c1.203 0 2 .754 2 2.199V8H4c-.553 0-1 .646-1 1.199V17c0 .549.428 1.139.951 1.307l1.197.387C5.672 18.861 6.55 19 7.1 19h5.8c.549 0 1.428-.139 1.951-.307l1.196-.387c.524-.167.953-.757.953-1.306V9.199C17 8.646 16.352 8 15.8 8z"></path></svg></button><button aria-label="get ​/api​/v1​/expenses​/my" class="opblock-control-arrow" aria-expanded="false" tabindex="-1"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" class="arrow" width="20" height="20" aria-hidden="true" focusable="false"><path d="M17.418 6.109c.272-.268.709-.268.979 0s.271.701 0 .969l-7.908 7.83c-.27.268-.707.268-.979 0l-7.908-7.83c-.27-.268-.27-.701 0-.969.271-.268.709-.268.979 0L10 13.25l7.418-7.141z"></path></svg></button></div><noscript></noscript></div></span><span><div class="opblock opblock-get" id="operations-Expenses-get_app_expense_show"><div class="opblock-summary opblock-summary-get"><button aria-expanded="false" class="opblock-summary-control"><span class="opblock-summary-method">GET</span><div class="opblock-summary-path-description-wrapper"><span class="opblock-summary-path" data-path="/api/v1/expenses/{id}"><a class="nostyle"><span>/api<wbr>/v1<wbr>/expenses<wbr>/{id}</span></a></span><div class="opblock-summary-description">Get a specific expense by ID</div></div></button><div class="view-line-link copy-to-clipboard" title="Copy to clipboard"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 15 16" width="15" height="16" aria-hidden="true" focusable="false"><g transform="translate(2, -1)"><path fill="#ffffff" fill-rule="evenodd" d="M2 13h4v1H2v-1zm5-6H2v1h5V7zm2 3V8l-3 3 3 3v-2h5v-2H9zM4.5 9H2v1h2.5V9zM2 12h2.5v-1H2v1zm9 1h1v2c-.02.28-.11.52-.3.7-.19.18-.42.28-.7.3H1c-.55 0-1-.45-1-1V4c0-.55.45-1 1-1h3c0-1.11.89-2 2-2 1.11 0 2 .89 2 2h3c.55 0 1 .45 1 1v5h-1V6H1v9h10v-2zM2 5h8c0-.55-.45-1-1-1H8c-.55 0-1-.45-1-1s-.45-1-1-1-1 .45-1 1-.45 1-1 1H3c-.55 0-1 .45-1 1z"></path></g></svg></div><button class="authorization__btn" aria-label="authorization button unlocked"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" class="unlocked" width="20" height="20" aria-hidden="true" focusable="false"><path d="M15.8 8H14V5.6C14 2.703 12.665 1 10 1 7.334 1 6 2.703 6 5.6V6h2v-.801C8 3.754 8.797 3 10 3c1.203 0 2 .754 2 2.199V8H4c-.553 0-1 .646-1 1.199V17c0 .549.428 1.139.951 1.307l1.197.387C5.672 18.861 6.55 19 7.1 19h5.8c.549 0 1.428-.139 1.951-.307l1.196-.387c.524-.167.953-.757.953-1.306V9.199C17 8.646 16.352 8 15.8 8z"></path></svg></button><button aria-label="get ​/api​/v1​/expenses​/{id}" class="opblock-control-arrow" aria-expanded="false" tabindex="-1"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" class="arrow" width="20" height="20" aria-hidden="true" focusable="false"><path d="M17.418 6.109c.272-.268.709-.268.979 0s.271.701 0 .969l-7.908 7.83c-.27.268-.707.268-.979 0l-7.908-7.83c-.27-.268-.27-.701 0-.969.271-.268.709-.268.979 0L10 13.25l7.418-7.141z"></path></svg></button></div><noscript></noscript></div></span><span><div class="opblock opblock-put" id="operations-Expenses-put_app_expense_update"><div class="opblock-summary opblock-summary-put"><button aria-expanded="false" class="opblock-summary-control"><span class="opblock-summary-method">PUT</span><div class="opblock-summary-path-description-wrapper"><span class="opblock-summary-path" data-path="/api/v1/expenses/{id}"><a class="nostyle"><span>/api<wbr>/v1<wbr>/expenses<wbr>/{id}</span></a></span><div class="opblock-summary-description">Update an existing expense</div></div></button><div class="view-line-link copy-to-clipboard" title="Copy to clipboard"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 15 16" width="15" height="16" aria-hidden="true" focusable="false"><g transform="translate(2, -1)"><path fill="#ffffff" fill-rule="evenodd" d="M2 13h4v1H2v-1zm5-6H2v1h5V7zm2 3V8l-3 3 3 3v-2h5v-2H9zM4.5 9H2v1h2.5V9zM2 12h2.5v-1H2v1zm9 1h1v2c-.02.28-.11.52-.3.7-.19.18-.42.28-.7.3H1c-.55 0-1-.45-1-1V4c0-.55.45-1 1-1h3c0-1.11.89-2 2-2 1.11 0 2 .89 2 2h3c.55 0 1 .45 1 1v5h-1V6H1v9h10v-2zM2 5h8c0-.55-.45-1-1-1H8c-.55 0-1-.45-1-1s-.45-1-1-1-1 .45-1 1-.45 1-1 1H3c-.55 0-1 .45-1 1z"></path></g></svg></div><button class="authorization__btn" aria-label="authorization button unlocked"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" class="unlocked" width="20" height="20" aria-hidden="true" focusable="false"><path d="M15.8 8H14V5.6C14 2.703 12.665 1 10 1 7.334 1 6 2.703 6 5.6V6h2v-.801C8 3.754 8.797 3 10 3c1.203 0 2 .754 2 2.199V8H4c-.553 0-1 .646-1 1.199V17c0 .549.428 1.139.951 1.307l1.197.387C5.672 18.861 6.55 19 7.1 19h5.8c.549 0 1.428-.139 1.951-.307l1.196-.387c.524-.167.953-.757.953-1.306V9.199C17 8.646 16.352 8 15.8 8z"></path></svg></button><button aria-label="put ​/api​/v1​/expenses​/{id}" class="opblock-control-arrow" aria-expanded="false" tabindex="-1"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" class="arrow" width="20" height="20" aria-hidden="true" focusable="false"><path d="M17.418 6.109c.272-.268.709-.268.979 0s.271.701 0 .969l-7.908 7.83c-.27.268-.707.268-.979 0l-7.908-7.83c-.27-.268-.27-.701 0-.969.271-.268.709-.268.979 0L10 13.25l7.418-7.141z"></path></svg></button></div><noscript></noscript></div></span><span><div class="opblock opblock-delete" id="operations-Expenses-delete_app_expense_delete"><div class="opblock-summary opblock-summary-delete"><button aria-expanded="false" class="opblock-summary-control"><span class="opblock-summary-method">DELETE</span><div class="opblock-summary-path-description-wrapper"><span class="opblock-summary-path" data-path="/api/v1/expenses/{id}"><a class="nostyle"><span>/api<wbr>/v1<wbr>/expenses<wbr>/{id}</span></a></span><div class="opblock-summary-description">Delete an expense</div></div></button><div class="view-line-link copy-to-clipboard" title="Copy to clipboard"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 15 16" width="15" height="16" aria-hidden="true" focusable="false"><g transform="translate(2, -1)"><path fill="#ffffff" fill-rule="evenodd" d="M2 13h4v1H2v-1zm5-6H2v1h5V7zm2 3V8l-3 3 3 3v-2h5v-2H9zM4.5 9H2v1h2.5V9zM2 12h2.5v-1H2v1zm9 1h1v2c-.02.28-.11.52-.3.7-.19.18-.42.28-.7.3H1c-.55 0-1-.45-1-1V4c0-.55.45-1 1-1h3c0-1.11.89-2 2-2 1.11 0 2 .89 2 2h3c.55 0 1 .45 1 1v5h-1V6H1v9h10v-2zM2 5h8c0-.55-.45-1-1-1H8c-.55 0-1-.45-1-1s-.45-1-1-1-1 .45-1 1-.45 1-1 1H3c-.55 0-1 .45-1 1z"></path></g></svg></div><button class="authorization__btn" aria-label="authorization button unlocked"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" class="unlocked" width="20" height="20" aria-hidden="true" focusable="false"><path d="M15.8 8H14V5.6C14 2.703 12.665 1 10 1 7.334 1 6 2.703 6 5.6V6h2v-.801C8 3.754 8.797 3 10 3c1.203 0 2 .754 2 2.199V8H4c-.553 0-1 .646-1 1.199V17c0 .549.428 1.139.951 1.307l1.197.387C5.672 18.861 6.55 19 7.1 19h5.8c.549 0 1.428-.139 1.951-.307l1.196-.387c.524-.167.953-.757.953-1.306V9.199C17 8.646 16.352 8 15.8 8z"></path></svg></button><button aria-label="delete ​/api​/v1​/expenses​/{id}" class="opblock-control-arrow" aria-expanded="false" tabindex="-1"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" class="arrow" width="20" height="20" aria-hidden="true" focusable="false"><path d="M17.418 6.109c.272-.268.709-.268.979 0s.271.701 0 .969l-7.908 7.83c-.27.268-.707.268-.979 0l-7.908-7.83c-.27-.268-.27-.701 0-.969.271-.268.709-.268.979 0L10 13.25l7.418-7.141z"></path></svg></button></div><noscript></noscript></div></span><span><div class="opblock opblock-get" id="operations-Expenses-get_app_expense_by_consultant"><div class="opblock-summary opblock-summary-get"><button aria-expanded="false" class="opblock-summary-control"><span class="opblock-summary-method">GET</span><div class="opblock-summary-path-description-wrapper"><span class="opblock-summary-path" data-path="/api/v1/expenses/consultant/{consultantId}"><a class="nostyle"><span>/api<wbr>/v1<wbr>/expenses<wbr>/consultant<wbr>/{consultantId}</span></a></span><div class="opblock-summary-description">Get expenses for a specific consultant</div></div></button><div class="view-line-link copy-to-clipboard" title="Copy to clipboard"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 15 16" width="15" height="16" aria-hidden="true" focusable="false"><g transform="translate(2, -1)"><path fill="#ffffff" fill-rule="evenodd" d="M2 13h4v1H2v-1zm5-6H2v1h5V7zm2 3V8l-3 3 3 3v-2h5v-2H9zM4.5 9H2v1h2.5V9zM2 12h2.5v-1H2v1zm9 1h1v2c-.02.28-.11.52-.3.7-.19.18-.42.28-.7.3H1c-.55 0-1-.45-1-1V4c0-.55.45-1 1-1h3c0-1.11.89-2 2-2 1.11 0 2 .89 2 2h3c.55 0 1 .45 1 1v5h-1V6H1v9h10v-2zM2 5h8c0-.55-.45-1-1-1H8c-.55 0-1-.45-1-1s-.45-1-1-1-1 .45-1 1-.45 1-1 1H3c-.55 0-1 .45-1 1z"></path></g></svg></div><button class="authorization__btn" aria-label="authorization button unlocked"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" class="unlocked" width="20" height="20" aria-hidden="true" focusable="false"><path d="M15.8 8H14V5.6C14 2.703 12.665 1 10 1 7.334 1 6 2.703 6 5.6V6h2v-.801C8 3.754 8.797 3 10 3c1.203 0 2 .754 2 2.199V8H4c-.553 0-1 .646-1 1.199V17c0 .549.428 1.139.951 1.307l1.197.387C5.672 18.861 6.55 19 7.1 19h5.8c.549 0 1.428-.139 1.951-.307l1.196-.387c.524-.167.953-.757.953-1.306V9.199C17 8.646 16.352 8 15.8 8z"></path></svg></button><button aria-label="get ​/api​/v1​/expenses​/consultant​/{consultantId}" class="opblock-control-arrow" aria-expanded="false" tabindex="-1"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" class="arrow" width="20" height="20" aria-hidden="true" focusable="false"><path d="M17.418 6.109c.272-.268.709-.268.979 0s.271.701 0 .969l-7.908 7.83c-.27.268-.707.268-.979 0l-7.908-7.83c-.27-.268-.27-.701 0-.969.271-.268.709-.268.979 0L10 13.25l7.418-7.141z"></path></svg></button></div><noscript></noscript></div></span><span><div class="opblock opblock-put" id="operations-Expenses-put_app_expense_validate"><div class="opblock-summary opblock-summary-put"><button aria-expanded="false" class="opblock-summary-control"><span class="opblock-summary-method">PUT</span><div class="opblock-summary-path-description-wrapper"><span class="opblock-summary-path" data-path="/api/v1/expenses/{id}/validate"><a class="nostyle"><span>/api<wbr>/v1<wbr>/expenses<wbr>/{id}<wbr>/validate</span></a></span><div class="opblock-summary-description">Validate an expense (admin only)</div></div></button><div class="view-line-link copy-to-clipboard" title="Copy to clipboard"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 15 16" width="15" height="16" aria-hidden="true" focusable="false"><g transform="translate(2, -1)"><path fill="#ffffff" fill-rule="evenodd" d="M2 13h4v1H2v-1zm5-6H2v1h5V7zm2 3V8l-3 3 3 3v-2h5v-2H9zM4.5 9H2v1h2.5V9zM2 12h2.5v-1H2v1zm9 1h1v2c-.02.28-.11.52-.3.7-.19.18-.42.28-.7.3H1c-.55 0-1-.45-1-1V4c0-.55.45-1 1-1h3c0-1.11.89-2 2-2 1.11 0 2 .89 2 2h3c.55 0 1 .45 1 1v5h-1V6H1v9h10v-2zM2 5h8c0-.55-.45-1-1-1H8c-.55 0-1-.45-1-1s-.45-1-1-1-1 .45-1 1-.45 1-1 1H3c-.55 0-1 .45-1 1z"></path></g></svg></div><button class="authorization__btn" aria-label="authorization button unlocked"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" class="unlocked" width="20" height="20" aria-hidden="true" focusable="false"><path d="M15.8 8H14V5.6C14 2.703 12.665 1 10 1 7.334 1 6 2.703 6 5.6V6h2v-.801C8 3.754 8.797 3 10 3c1.203 0 2 .754 2 2.199V8H4c-.553 0-1 .646-1 1.199V17c0 .549.428 1.139.951 1.307l1.197.387C5.672 18.861 6.55 19 7.1 19h5.8c.549 0 1.428-.139 1.951-.307l1.196-.387c.524-.167.953-.757.953-1.306V9.199C17 8.646 16.352 8 15.8 8z"></path></svg></button><button aria-label="put ​/api​/v1​/expenses​/{id}​/validate" class="opblock-control-arrow" aria-expanded="false" tabindex="-1"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" class="arrow" width="20" height="20" aria-hidden="true" focusable="false"><path d="M17.418 6.109c.272-.268.709-.268.979 0s.271.701 0 .969l-7.908 7.83c-.27.268-.707.268-.979 0l-7.908-7.83c-.27-.268-.27-.701 0-.969.271-.268.709-.268.979 0L10 13.25l7.418-7.141z"></path></svg></button></div><noscript></noscript></div></span><span><div class="opblock opblock-get" id="operations-Expenses-get_app_expense_total"><div class="opblock-summary opblock-summary-get"><button aria-expanded="false" class="opblock-summary-control"><span class="opblock-summary-method">GET</span><div class="opblock-summary-path-description-wrapper"><span class="opblock-summary-path" data-path="/api/v1/expenses/total/{consultantId}"><a class="nostyle"><span>/api<wbr>/v1<wbr>/expenses<wbr>/total<wbr>/{consultantId}</span></a></span><div class="opblock-summary-description">Calculate total expenses for a consultant</div></div></button><div class="view-line-link copy-to-clipboard" title="Copy to clipboard"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 15 16" width="15" height="16" aria-hidden="true" focusable="false"><g transform="translate(2, -1)"><path fill="#ffffff" fill-rule="evenodd" d="M2 13h4v1H2v-1zm5-6H2v1h5V7zm2 3V8l-3 3 3 3v-2h5v-2H9zM4.5 9H2v1h2.5V9zM2 12h2.5v-1H2v1zm9 1h1v2c-.02.28-.11.52-.3.7-.19.18-.42.28-.7.3H1c-.55 0-1-.45-1-1V4c0-.55.45-1 1-1h3c0-1.11.89-2 2-2 1.11 0 2 .89 2 2h3c.55 0 1 .45 1 1v5h-1V6H1v9h10v-2zM2 5h8c0-.55-.45-1-1-1H8c-.55 0-1-.45-1-1s-.45-1-1-1-1 .45-1 1-.45 1-1 1H3c-.55 0-1 .45-1 1z"></path></g></svg></div><button class="authorization__btn" aria-label="authorization button unlocked"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" class="unlocked" width="20" height="20" aria-hidden="true" focusable="false"><path d="M15.8 8H14V5.6C14 2.703 12.665 1 10 1 7.334 1 6 2.703 6 5.6V6h2v-.801C8 3.754 8.797 3 10 3c1.203 0 2 .754 2 2.199V8H4c-.553 0-1 .646-1 1.199V17c0 .549.428 1.139.951 1.307l1.197.387C5.672 18.861 6.55 19 7.1 19h5.8c.549 0 1.428-.139 1.951-.307l1.196-.387c.524-.167.953-.757.953-1.306V9.199C17 8.646 16.352 8 15.8 8z"></path></svg></button><button aria-label="get ​/api​/v1​/expenses​/total​/{consultantId}" class="opblock-control-arrow" aria-expanded="false" tabindex="-1"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" class="arrow" width="20" height="20" aria-hidden="true" focusable="false"><path d="M17.418 6.109c.272-.268.709-.268.979 0s.271.701 0 .969l-7.908 7.83c-.27.268-.707.268-.979 0l-7.908-7.83c-.27-.268-.27-.701 0-.969.271-.268.709-.268.979 0L10 13.25l7.418-7.141z"></path></svg></button></div><noscript></noscript></div></span></div> </div></div></span><span><div class="opblock-tag-section is-open"><h3 class="opblock-tag" id="operations-tag-Leaves" data-tag="Leaves" data-is-open="true"><a class="nostyle"><span>Leaves</span></a><small><div class="renderedMarkdown"><p>Leave request management</p></div></small><button aria-expanded="true" class="expand-operation" title="Collapse operation"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" class="arrow" width="20" height="20" aria-hidden="true" focusable="false"><path d="M 17.418 14.908 C 17.69 15.176 18.127 15.176 18.397 14.908 C 18.667 14.64 18.668 14.207 18.397 13.939 L 10.489 6.109 C 10.219 5.841 9.782 5.841 9.51 6.109 L 1.602 13.939 C 1.332 14.207 1.332 14.64 1.602 14.908 C 1.873 15.176 2.311 15.176 2.581 14.908 L 10 7.767 L 17.418 14.908 Z"></path></svg></button></h3><div class="no-margin"> <div class="operation-tag-content"><span><div class="opblock opblock-get" id="operations-Leaves-get_app_leave_index"><div class="opblock-summary opblock-summary-get"><button aria-expanded="false" class="opblock-summary-control"><span class="opblock-summary-method">GET</span><div class="opblock-summary-path-description-wrapper"><span class="opblock-summary-path" data-path="/api/v1/leaves"><a class="nostyle"><span>/api<wbr>/v1<wbr>/leaves</span></a></span><div class="opblock-summary-description">Get all leave requests (admin only)</div></div></button><div class="view-line-link copy-to-clipboard" title="Copy to clipboard"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 15 16" width="15" height="16" aria-hidden="true" focusable="false"><g transform="translate(2, -1)"><path fill="#ffffff" fill-rule="evenodd" d="M2 13h4v1H2v-1zm5-6H2v1h5V7zm2 3V8l-3 3 3 3v-2h5v-2H9zM4.5 9H2v1h2.5V9zM2 12h2.5v-1H2v1zm9 1h1v2c-.02.28-.11.52-.3.7-.19.18-.42.28-.7.3H1c-.55 0-1-.45-1-1V4c0-.55.45-1 1-1h3c0-1.11.89-2 2-2 1.11 0 2 .89 2 2h3c.55 0 1 .45 1 1v5h-1V6H1v9h10v-2zM2 5h8c0-.55-.45-1-1-1H8c-.55 0-1-.45-1-1s-.45-1-1-1-1 .45-1 1-.45 1-1 1H3c-.55 0-1 .45-1 1z"></path></g></svg></div><button class="authorization__btn" aria-label="authorization button unlocked"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" class="unlocked" width="20" height="20" aria-hidden="true" focusable="false"><path d="M15.8 8H14V5.6C14 2.703 12.665 1 10 1 7.334 1 6 2.703 6 5.6V6h2v-.801C8 3.754 8.797 3 10 3c1.203 0 2 .754 2 2.199V8H4c-.553 0-1 .646-1 1.199V17c0 .549.428 1.139.951 1.307l1.197.387C5.672 18.861 6.55 19 7.1 19h5.8c.549 0 1.428-.139 1.951-.307l1.196-.387c.524-.167.953-.757.953-1.306V9.199C17 8.646 16.352 8 15.8 8z"></path></svg></button><button aria-label="get ​/api​/v1​/leaves" class="opblock-control-arrow" aria-expanded="false" tabindex="-1"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" class="arrow" width="20" height="20" aria-hidden="true" focusable="false"><path d="M17.418 6.109c.272-.268.709-.268.979 0s.271.701 0 .969l-7.908 7.83c-.27.268-.707.268-.979 0l-7.908-7.83c-.27-.268-.27-.701 0-.969.271-.268.709-.268.979 0L10 13.25l7.418-7.141z"></path></svg></button></div><noscript></noscript></div></span><span><div class="opblock opblock-post" id="operations-Leaves-post_app_leave_create"><div class="opblock-summary opblock-summary-post"><button aria-expanded="false" class="opblock-summary-control"><span class="opblock-summary-method">POST</span><div class="opblock-summary-path-description-wrapper"><span class="opblock-summary-path" data-path="/api/v1/leaves"><a class="nostyle"><span>/api<wbr>/v1<wbr>/leaves</span></a></span><div class="opblock-summary-description">Create a new leave request</div></div></button><div class="view-line-link copy-to-clipboard" title="Copy to clipboard"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 15 16" width="15" height="16" aria-hidden="true" focusable="false"><g transform="translate(2, -1)"><path fill="#ffffff" fill-rule="evenodd" d="M2 13h4v1H2v-1zm5-6H2v1h5V7zm2 3V8l-3 3 3 3v-2h5v-2H9zM4.5 9H2v1h2.5V9zM2 12h2.5v-1H2v1zm9 1h1v2c-.02.28-.11.52-.3.7-.19.18-.42.28-.7.3H1c-.55 0-1-.45-1-1V4c0-.55.45-1 1-1h3c0-1.11.89-2 2-2 1.11 0 2 .89 2 2h3c.55 0 1 .45 1 1v5h-1V6H1v9h10v-2zM2 5h8c0-.55-.45-1-1-1H8c-.55 0-1-.45-1-1s-.45-1-1-1-1 .45-1 1-.45 1-1 1H3c-.55 0-1 .45-1 1z"></path></g></svg></div><button class="authorization__btn" aria-label="authorization button unlocked"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" class="unlocked" width="20" height="20" aria-hidden="true" focusable="false"><path d="M15.8 8H14V5.6C14 2.703 12.665 1 10 1 7.334 1 6 2.703 6 5.6V6h2v-.801C8 3.754 8.797 3 10 3c1.203 0 2 .754 2 2.199V8H4c-.553 0-1 .646-1 1.199V17c0 .549.428 1.139.951 1.307l1.197.387C5.672 18.861 6.55 19 7.1 19h5.8c.549 0 1.428-.139 1.951-.307l1.196-.387c.524-.167.953-.757.953-1.306V9.199C17 8.646 16.352 8 15.8 8z"></path></svg></button><button aria-label="post ​/api​/v1​/leaves" class="opblock-control-arrow" aria-expanded="false" tabindex="-1"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" class="arrow" width="20" height="20" aria-hidden="true" focusable="false"><path d="M17.418 6.109c.272-.268.709-.268.979 0s.271.701 0 .969l-7.908 7.83c-.27.268-.707.268-.979 0l-7.908-7.83c-.27-.268-.27-.701 0-.969.271-.268.709-.268.979 0L10 13.25l7.418-7.141z"></path></svg></button></div><noscript></noscript></div></span><span><div class="opblock opblock-get" id="operations-Leaves-get_app_leave_pending"><div class="opblock-summary opblock-summary-get"><button aria-expanded="false" class="opblock-summary-control"><span class="opblock-summary-method">GET</span><div class="opblock-summary-path-description-wrapper"><span class="opblock-summary-path" data-path="/api/v1/leaves/pending"><a class="nostyle"><span>/api<wbr>/v1<wbr>/leaves<wbr>/pending</span></a></span><div class="opblock-summary-description">Get all pending leave requests (admin only)</div></div></button><div class="view-line-link copy-to-clipboard" title="Copy to clipboard"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 15 16" width="15" height="16" aria-hidden="true" focusable="false"><g transform="translate(2, -1)"><path fill="#ffffff" fill-rule="evenodd" d="M2 13h4v1H2v-1zm5-6H2v1h5V7zm2 3V8l-3 3 3 3v-2h5v-2H9zM4.5 9H2v1h2.5V9zM2 12h2.5v-1H2v1zm9 1h1v2c-.02.28-.11.52-.3.7-.19.18-.42.28-.7.3H1c-.55 0-1-.45-1-1V4c0-.55.45-1 1-1h3c0-1.11.89-2 2-2 1.11 0 2 .89 2 2h3c.55 0 1 .45 1 1v5h-1V6H1v9h10v-2zM2 5h8c0-.55-.45-1-1-1H8c-.55 0-1-.45-1-1s-.45-1-1-1-1 .45-1 1-.45 1-1 1H3c-.55 0-1 .45-1 1z"></path></g></svg></div><button class="authorization__btn" aria-label="authorization button unlocked"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" class="unlocked" width="20" height="20" aria-hidden="true" focusable="false"><path d="M15.8 8H14V5.6C14 2.703 12.665 1 10 1 7.334 1 6 2.703 6 5.6V6h2v-.801C8 3.754 8.797 3 10 3c1.203 0 2 .754 2 2.199V8H4c-.553 0-1 .646-1 1.199V17c0 .549.428 1.139.951 1.307l1.197.387C5.672 18.861 6.55 19 7.1 19h5.8c.549 0 1.428-.139 1.951-.307l1.196-.387c.524-.167.953-.757.953-1.306V9.199C17 8.646 16.352 8 15.8 8z"></path></svg></button><button aria-label="get ​/api​/v1​/leaves​/pending" class="opblock-control-arrow" aria-expanded="false" tabindex="-1"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" class="arrow" width="20" height="20" aria-hidden="true" focusable="false"><path d="M17.418 6.109c.272-.268.709-.268.979 0s.271.701 0 .969l-7.908 7.83c-.27.268-.707.268-.979 0l-7.908-7.83c-.27-.268-.27-.701 0-.969.271-.268.709-.268.979 0L10 13.25l7.418-7.141z"></path></svg></button></div><noscript></noscript></div></span><span><div class="opblock opblock-get" id="operations-Leaves-get_app_leave_show"><div class="opblock-summary opblock-summary-get"><button aria-expanded="false" class="opblock-summary-control"><span class="opblock-summary-method">GET</span><div class="opblock-summary-path-description-wrapper"><span class="opblock-summary-path" data-path="/api/v1/leaves/{id}"><a class="nostyle"><span>/api<wbr>/v1<wbr>/leaves<wbr>/{id}</span></a></span><div class="opblock-summary-description">Get a specific leave request by ID</div></div></button><div class="view-line-link copy-to-clipboard" title="Copy to clipboard"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 15 16" width="15" height="16" aria-hidden="true" focusable="false"><g transform="translate(2, -1)"><path fill="#ffffff" fill-rule="evenodd" d="M2 13h4v1H2v-1zm5-6H2v1h5V7zm2 3V8l-3 3 3 3v-2h5v-2H9zM4.5 9H2v1h2.5V9zM2 12h2.5v-1H2v1zm9 1h1v2c-.02.28-.11.52-.3.7-.19.18-.42.28-.7.3H1c-.55 0-1-.45-1-1V4c0-.55.45-1 1-1h3c0-1.11.89-2 2-2 1.11 0 2 .89 2 2h3c.55 0 1 .45 1 1v5h-1V6H1v9h10v-2zM2 5h8c0-.55-.45-1-1-1H8c-.55 0-1-.45-1-1s-.45-1-1-1-1 .45-1 1-.45 1-1 1H3c-.55 0-1 .45-1 1z"></path></g></svg></div><button class="authorization__btn" aria-label="authorization button unlocked"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" class="unlocked" width="20" height="20" aria-hidden="true" focusable="false"><path d="M15.8 8H14V5.6C14 2.703 12.665 1 10 1 7.334 1 6 2.703 6 5.6V6h2v-.801C8 3.754 8.797 3 10 3c1.203 0 2 .754 2 2.199V8H4c-.553 0-1 .646-1 1.199V17c0 .549.428 1.139.951 1.307l1.197.387C5.672 18.861 6.55 19 7.1 19h5.8c.549 0 1.428-.139 1.951-.307l1.196-.387c.524-.167.953-.757.953-1.306V9.199C17 8.646 16.352 8 15.8 8z"></path></svg></button><button aria-label="get ​/api​/v1​/leaves​/{id}" class="opblock-control-arrow" aria-expanded="false" tabindex="-1"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" class="arrow" width="20" height="20" aria-hidden="true" focusable="false"><path d="M17.418 6.109c.272-.268.709-.268.979 0s.271.701 0 .969l-7.908 7.83c-.27.268-.707.268-.979 0l-7.908-7.83c-.27-.268-.27-.701 0-.969.271-.268.709-.268.979 0L10 13.25l7.418-7.141z"></path></svg></button></div><noscript></noscript></div></span><span><div class="opblock opblock-put" id="operations-Leaves-put_app_leave_update"><div class="opblock-summary opblock-summary-put"><button aria-expanded="false" class="opblock-summary-control"><span class="opblock-summary-method">PUT</span><div class="opblock-summary-path-description-wrapper"><span class="opblock-summary-path" data-path="/api/v1/leaves/{id}"><a class="nostyle"><span>/api<wbr>/v1<wbr>/leaves<wbr>/{id}</span></a></span><div class="opblock-summary-description">Update an existing leave request</div></div></button><div class="view-line-link copy-to-clipboard" title="Copy to clipboard"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 15 16" width="15" height="16" aria-hidden="true" focusable="false"><g transform="translate(2, -1)"><path fill="#ffffff" fill-rule="evenodd" d="M2 13h4v1H2v-1zm5-6H2v1h5V7zm2 3V8l-3 3 3 3v-2h5v-2H9zM4.5 9H2v1h2.5V9zM2 12h2.5v-1H2v1zm9 1h1v2c-.02.28-.11.52-.3.7-.19.18-.42.28-.7.3H1c-.55 0-1-.45-1-1V4c0-.55.45-1 1-1h3c0-1.11.89-2 2-2 1.11 0 2 .89 2 2h3c.55 0 1 .45 1 1v5h-1V6H1v9h10v-2zM2 5h8c0-.55-.45-1-1-1H8c-.55 0-1-.45-1-1s-.45-1-1-1-1 .45-1 1-.45 1-1 1H3c-.55 0-1 .45-1 1z"></path></g></svg></div><button class="authorization__btn" aria-label="authorization button unlocked"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" class="unlocked" width="20" height="20" aria-hidden="true" focusable="false"><path d="M15.8 8H14V5.6C14 2.703 12.665 1 10 1 7.334 1 6 2.703 6 5.6V6h2v-.801C8 3.754 8.797 3 10 3c1.203 0 2 .754 2 2.199V8H4c-.553 0-1 .646-1 1.199V17c0 .549.428 1.139.951 1.307l1.197.387C5.672 18.861 6.55 19 7.1 19h5.8c.549 0 1.428-.139 1.951-.307l1.196-.387c.524-.167.953-.757.953-1.306V9.199C17 8.646 16.352 8 15.8 8z"></path></svg></button><button aria-label="put ​/api​/v1​/leaves​/{id}" class="opblock-control-arrow" aria-expanded="false" tabindex="-1"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" class="arrow" width="20" height="20" aria-hidden="true" focusable="false"><path d="M17.418 6.109c.272-.268.709-.268.979 0s.271.701 0 .969l-7.908 7.83c-.27.268-.707.268-.979 0l-7.908-7.83c-.27-.268-.27-.701 0-.969.271-.268.709-.268.979 0L10 13.25l7.418-7.141z"></path></svg></button></div><noscript></noscript></div></span><span><div class="opblock opblock-delete" id="operations-Leaves-delete_app_leave_delete"><div class="opblock-summary opblock-summary-delete"><button aria-expanded="false" class="opblock-summary-control"><span class="opblock-summary-method">DELETE</span><div class="opblock-summary-path-description-wrapper"><span class="opblock-summary-path" data-path="/api/v1/leaves/{id}"><a class="nostyle"><span>/api<wbr>/v1<wbr>/leaves<wbr>/{id}</span></a></span><div class="opblock-summary-description">Delete a leave request</div></div></button><div class="view-line-link copy-to-clipboard" title="Copy to clipboard"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 15 16" width="15" height="16" aria-hidden="true" focusable="false"><g transform="translate(2, -1)"><path fill="#ffffff" fill-rule="evenodd" d="M2 13h4v1H2v-1zm5-6H2v1h5V7zm2 3V8l-3 3 3 3v-2h5v-2H9zM4.5 9H2v1h2.5V9zM2 12h2.5v-1H2v1zm9 1h1v2c-.02.28-.11.52-.3.7-.19.18-.42.28-.7.3H1c-.55 0-1-.45-1-1V4c0-.55.45-1 1-1h3c0-1.11.89-2 2-2 1.11 0 2 .89 2 2h3c.55 0 1 .45 1 1v5h-1V6H1v9h10v-2zM2 5h8c0-.55-.45-1-1-1H8c-.55 0-1-.45-1-1s-.45-1-1-1-1 .45-1 1-.45 1-1 1H3c-.55 0-1 .45-1 1z"></path></g></svg></div><button class="authorization__btn" aria-label="authorization button unlocked"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" class="unlocked" width="20" height="20" aria-hidden="true" focusable="false"><path d="M15.8 8H14V5.6C14 2.703 12.665 1 10 1 7.334 1 6 2.703 6 5.6V6h2v-.801C8 3.754 8.797 3 10 3c1.203 0 2 .754 2 2.199V8H4c-.553 0-1 .646-1 1.199V17c0 .549.428 1.139.951 1.307l1.197.387C5.672 18.861 6.55 19 7.1 19h5.8c.549 0 1.428-.139 1.951-.307l1.196-.387c.524-.167.953-.757.953-1.306V9.199C17 8.646 16.352 8 15.8 8z"></path></svg></button><button aria-label="delete ​/api​/v1​/leaves​/{id}" class="opblock-control-arrow" aria-expanded="false" tabindex="-1"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" class="arrow" width="20" height="20" aria-hidden="true" focusable="false"><path d="M17.418 6.109c.272-.268.709-.268.979 0s.271.701 0 .969l-7.908 7.83c-.27.268-.707.268-.979 0l-7.908-7.83c-.27-.268-.27-.701 0-.969.271-.268.709-.268.979 0L10 13.25l7.418-7.141z"></path></svg></button></div><noscript></noscript></div></span><span><div class="opblock opblock-get" id="operations-Leaves-get_app_leave_by_consultant"><div class="opblock-summary opblock-summary-get"><button aria-expanded="false" class="opblock-summary-control"><span class="opblock-summary-method">GET</span><div class="opblock-summary-path-description-wrapper"><span class="opblock-summary-path" data-path="/api/v1/leaves/consultant/{consultantId}"><a class="nostyle"><span>/api<wbr>/v1<wbr>/leaves<wbr>/consultant<wbr>/{consultantId}</span></a></span><div class="opblock-summary-description">Get leave requests for a specific consultant</div></div></button><div class="view-line-link copy-to-clipboard" title="Copy to clipboard"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 15 16" width="15" height="16" aria-hidden="true" focusable="false"><g transform="translate(2, -1)"><path fill="#ffffff" fill-rule="evenodd" d="M2 13h4v1H2v-1zm5-6H2v1h5V7zm2 3V8l-3 3 3 3v-2h5v-2H9zM4.5 9H2v1h2.5V9zM2 12h2.5v-1H2v1zm9 1h1v2c-.02.28-.11.52-.3.7-.19.18-.42.28-.7.3H1c-.55 0-1-.45-1-1V4c0-.55.45-1 1-1h3c0-1.11.89-2 2-2 1.11 0 2 .89 2 2h3c.55 0 1 .45 1 1v5h-1V6H1v9h10v-2zM2 5h8c0-.55-.45-1-1-1H8c-.55 0-1-.45-1-1s-.45-1-1-1-1 .45-1 1-.45 1-1 1H3c-.55 0-1 .45-1 1z"></path></g></svg></div><button class="authorization__btn" aria-label="authorization button unlocked"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" class="unlocked" width="20" height="20" aria-hidden="true" focusable="false"><path d="M15.8 8H14V5.6C14 2.703 12.665 1 10 1 7.334 1 6 2.703 6 5.6V6h2v-.801C8 3.754 8.797 3 10 3c1.203 0 2 .754 2 2.199V8H4c-.553 0-1 .646-1 1.199V17c0 .549.428 1.139.951 1.307l1.197.387C5.672 18.861 6.55 19 7.1 19h5.8c.549 0 1.428-.139 1.951-.307l1.196-.387c.524-.167.953-.757.953-1.306V9.199C17 8.646 16.352 8 15.8 8z"></path></svg></button><button aria-label="get ​/api​/v1​/leaves​/consultant​/{consultantId}" class="opblock-control-arrow" aria-expanded="false" tabindex="-1"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" class="arrow" width="20" height="20" aria-hidden="true" focusable="false"><path d="M17.418 6.109c.272-.268.709-.268.979 0s.271.701 0 .969l-7.908 7.83c-.27.268-.707.268-.979 0l-7.908-7.83c-.27-.268-.27-.701 0-.969.271-.268.709-.268.979 0L10 13.25l7.418-7.141z"></path></svg></button></div><noscript></noscript></div></span><span><div class="opblock opblock-put" id="operations-Leaves-put_app_leave_approve"><div class="opblock-summary opblock-summary-put"><button aria-expanded="false" class="opblock-summary-control"><span class="opblock-summary-method">PUT</span><div class="opblock-summary-path-description-wrapper"><span class="opblock-summary-path" data-path="/api/v1/leaves/{id}/approve"><a class="nostyle"><span>/api<wbr>/v1<wbr>/leaves<wbr>/{id}<wbr>/approve</span></a></span><div class="opblock-summary-description">Approve a leave request (admin only)</div></div></button><div class="view-line-link copy-to-clipboard" title="Copy to clipboard"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 15 16" width="15" height="16" aria-hidden="true" focusable="false"><g transform="translate(2, -1)"><path fill="#ffffff" fill-rule="evenodd" d="M2 13h4v1H2v-1zm5-6H2v1h5V7zm2 3V8l-3 3 3 3v-2h5v-2H9zM4.5 9H2v1h2.5V9zM2 12h2.5v-1H2v1zm9 1h1v2c-.02.28-.11.52-.3.7-.19.18-.42.28-.7.3H1c-.55 0-1-.45-1-1V4c0-.55.45-1 1-1h3c0-1.11.89-2 2-2 1.11 0 2 .89 2 2h3c.55 0 1 .45 1 1v5h-1V6H1v9h10v-2zM2 5h8c0-.55-.45-1-1-1H8c-.55 0-1-.45-1-1s-.45-1-1-1-1 .45-1 1-.45 1-1 1H3c-.55 0-1 .45-1 1z"></path></g></svg></div><button class="authorization__btn" aria-label="authorization button unlocked"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" class="unlocked" width="20" height="20" aria-hidden="true" focusable="false"><path d="M15.8 8H14V5.6C14 2.703 12.665 1 10 1 7.334 1 6 2.703 6 5.6V6h2v-.801C8 3.754 8.797 3 10 3c1.203 0 2 .754 2 2.199V8H4c-.553 0-1 .646-1 1.199V17c0 .549.428 1.139.951 1.307l1.197.387C5.672 18.861 6.55 19 7.1 19h5.8c.549 0 1.428-.139 1.951-.307l1.196-.387c.524-.167.953-.757.953-1.306V9.199C17 8.646 16.352 8 15.8 8z"></path></svg></button><button aria-label="put ​/api​/v1​/leaves​/{id}​/approve" class="opblock-control-arrow" aria-expanded="false" tabindex="-1"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" class="arrow" width="20" height="20" aria-hidden="true" focusable="false"><path d="M17.418 6.109c.272-.268.709-.268.979 0s.271.701 0 .969l-7.908 7.83c-.27.268-.707.268-.979 0l-7.908-7.83c-.27-.268-.27-.701 0-.969.271-.268.709-.268.979 0L10 13.25l7.418-7.141z"></path></svg></button></div><noscript></noscript></div></span><span><div class="opblock opblock-put" id="operations-Leaves-put_app_leave_reject"><div class="opblock-summary opblock-summary-put"><button aria-expanded="false" class="opblock-summary-control"><span class="opblock-summary-method">PUT</span><div class="opblock-summary-path-description-wrapper"><span class="opblock-summary-path" data-path="/api/v1/leaves/{id}/reject"><a class="nostyle"><span>/api<wbr>/v1<wbr>/leaves<wbr>/{id}<wbr>/reject</span></a></span><div class="opblock-summary-description">Reject a leave request (admin only)</div></div></button><div class="view-line-link copy-to-clipboard" title="Copy to clipboard"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 15 16" width="15" height="16" aria-hidden="true" focusable="false"><g transform="translate(2, -1)"><path fill="#ffffff" fill-rule="evenodd" d="M2 13h4v1H2v-1zm5-6H2v1h5V7zm2 3V8l-3 3 3 3v-2h5v-2H9zM4.5 9H2v1h2.5V9zM2 12h2.5v-1H2v1zm9 1h1v2c-.02.28-.11.52-.3.7-.19.18-.42.28-.7.3H1c-.55 0-1-.45-1-1V4c0-.55.45-1 1-1h3c0-1.11.89-2 2-2 1.11 0 2 .89 2 2h3c.55 0 1 .45 1 1v5h-1V6H1v9h10v-2zM2 5h8c0-.55-.45-1-1-1H8c-.55 0-1-.45-1-1s-.45-1-1-1-1 .45-1 1-.45 1-1 1H3c-.55 0-1 .45-1 1z"></path></g></svg></div><button class="authorization__btn" aria-label="authorization button unlocked"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" class="unlocked" width="20" height="20" aria-hidden="true" focusable="false"><path d="M15.8 8H14V5.6C14 2.703 12.665 1 10 1 7.334 1 6 2.703 6 5.6V6h2v-.801C8 3.754 8.797 3 10 3c1.203 0 2 .754 2 2.199V8H4c-.553 0-1 .646-1 1.199V17c0 .549.428 1.139.951 1.307l1.197.387C5.672 18.861 6.55 19 7.1 19h5.8c.549 0 1.428-.139 1.951-.307l1.196-.387c.524-.167.953-.757.953-1.306V9.199C17 8.646 16.352 8 15.8 8z"></path></svg></button><button aria-label="put ​/api​/v1​/leaves​/{id}​/reject" class="opblock-control-arrow" aria-expanded="false" tabindex="-1"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" class="arrow" width="20" height="20" aria-hidden="true" focusable="false"><path d="M17.418 6.109c.272-.268.709-.268.979 0s.271.701 0 .969l-7.908 7.83c-.27.268-.707.268-.979 0l-7.908-7.83c-.27-.268-.27-.701 0-.969.271-.268.709-.268.979 0L10 13.25l7.418-7.141z"></path></svg></button></div><noscript></noscript></div></span><span><div class="opblock opblock-get" id="operations-Leaves-get_app_leave_balance"><div class="opblock-summary opblock-summary-get"><button aria-expanded="false" class="opblock-summary-control"><span class="opblock-summary-method">GET</span><div class="opblock-summary-path-description-wrapper"><span class="opblock-summary-path" data-path="/api/v1/leaves/balance/{consultantId}"><a class="nostyle"><span>/api<wbr>/v1<wbr>/leaves<wbr>/balance<wbr>/{consultantId}</span></a></span><div class="opblock-summary-description">Calculate the remaining leave balance for a consultant</div></div></button><div class="view-line-link copy-to-clipboard" title="Copy to clipboard"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 15 16" width="15" height="16" aria-hidden="true" focusable="false"><g transform="translate(2, -1)"><path fill="#ffffff" fill-rule="evenodd" d="M2 13h4v1H2v-1zm5-6H2v1h5V7zm2 3V8l-3 3 3 3v-2h5v-2H9zM4.5 9H2v1h2.5V9zM2 12h2.5v-1H2v1zm9 1h1v2c-.02.28-.11.52-.3.7-.19.18-.42.28-.7.3H1c-.55 0-1-.45-1-1V4c0-.55.45-1 1-1h3c0-1.11.89-2 2-2 1.11 0 2 .89 2 2h3c.55 0 1 .45 1 1v5h-1V6H1v9h10v-2zM2 5h8c0-.55-.45-1-1-1H8c-.55 0-1-.45-1-1s-.45-1-1-1-1 .45-1 1-.45 1-1 1H3c-.55 0-1 .45-1 1z"></path></g></svg></div><button class="authorization__btn" aria-label="authorization button unlocked"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" class="unlocked" width="20" height="20" aria-hidden="true" focusable="false"><path d="M15.8 8H14V5.6C14 2.703 12.665 1 10 1 7.334 1 6 2.703 6 5.6V6h2v-.801C8 3.754 8.797 3 10 3c1.203 0 2 .754 2 2.199V8H4c-.553 0-1 .646-1 1.199V17c0 .549.428 1.139.951 1.307l1.197.387C5.672 18.861 6.55 19 7.1 19h5.8c.549 0 1.428-.139 1.951-.307l1.196-.387c.524-.167.953-.757.953-1.306V9.199C17 8.646 16.352 8 15.8 8z"></path></svg></button><button aria-label="get ​/api​/v1​/leaves​/balance​/{consultantId}" class="opblock-control-arrow" aria-expanded="false" tabindex="-1"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" class="arrow" width="20" height="20" aria-hidden="true" focusable="false"><path d="M17.418 6.109c.272-.268.709-.268.979 0s.271.701 0 .969l-7.908 7.83c-.27.268-.707.268-.979 0l-7.908-7.83c-.27-.268-.27-.701 0-.969.271-.268.709-.268.979 0L10 13.25l7.418-7.141z"></path></svg></button></div><noscript></noscript></div></span></div> </div></div></span><span><div class="opblock-tag-section is-open"><h3 class="opblock-tag" id="operations-tag-Company" data-tag="Company" data-is-open="true"><a class="nostyle"><span>Company</span></a><small><div class="renderedMarkdown"><p>Company information management</p></div></small><button aria-expanded="true" class="expand-operation" title="Collapse operation"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" class="arrow" width="20" height="20" aria-hidden="true" focusable="false"><path d="M 17.418 14.908 C 17.69 15.176 18.127 15.176 18.397 14.908 C 18.667 14.64 18.668 14.207 18.397 13.939 L 10.489 6.109 C 10.219 5.841 9.782 5.841 9.51 6.109 L 1.602 13.939 C 1.332 14.207 1.332 14.64 1.602 14.908 C 1.873 15.176 2.311 15.176 2.581 14.908 L 10 7.767 L 17.418 14.908 Z"></path></svg></button></h3><div class="no-margin"> <div class="operation-tag-content"><span><div class="opblock opblock-get" id="operations-Company-get_app_company_show"><div class="opblock-summary opblock-summary-get"><button aria-expanded="false" class="opblock-summary-control"><span class="opblock-summary-method">GET</span><div class="opblock-summary-path-description-wrapper"><span class="opblock-summary-path" data-path="/api/v1/company"><a class="nostyle"><span>/api<wbr>/v1<wbr>/company</span></a></span><div class="opblock-summary-description">Get company information</div></div></button><div class="view-line-link copy-to-clipboard" title="Copy to clipboard"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 15 16" width="15" height="16" aria-hidden="true" focusable="false"><g transform="translate(2, -1)"><path fill="#ffffff" fill-rule="evenodd" d="M2 13h4v1H2v-1zm5-6H2v1h5V7zm2 3V8l-3 3 3 3v-2h5v-2H9zM4.5 9H2v1h2.5V9zM2 12h2.5v-1H2v1zm9 1h1v2c-.02.28-.11.52-.3.7-.19.18-.42.28-.7.3H1c-.55 0-1-.45-1-1V4c0-.55.45-1 1-1h3c0-1.11.89-2 2-2 1.11 0 2 .89 2 2h3c.55 0 1 .45 1 1v5h-1V6H1v9h10v-2zM2 5h8c0-.55-.45-1-1-1H8c-.55 0-1-.45-1-1s-.45-1-1-1-1 .45-1 1-.45 1-1 1H3c-.55 0-1 .45-1 1z"></path></g></svg></div><button class="authorization__btn" aria-label="authorization button unlocked"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" class="unlocked" width="20" height="20" aria-hidden="true" focusable="false"><path d="M15.8 8H14V5.6C14 2.703 12.665 1 10 1 7.334 1 6 2.703 6 5.6V6h2v-.801C8 3.754 8.797 3 10 3c1.203 0 2 .754 2 2.199V8H4c-.553 0-1 .646-1 1.199V17c0 .549.428 1.139.951 1.307l1.197.387C5.672 18.861 6.55 19 7.1 19h5.8c.549 0 1.428-.139 1.951-.307l1.196-.387c.524-.167.953-.757.953-1.306V9.199C17 8.646 16.352 8 15.8 8z"></path></svg></button><button aria-label="get ​/api​/v1​/company" class="opblock-control-arrow" aria-expanded="false" tabindex="-1"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" class="arrow" width="20" height="20" aria-hidden="true" focusable="false"><path d="M17.418 6.109c.272-.268.709-.268.979 0s.271.701 0 .969l-7.908 7.83c-.27.268-.707.268-.979 0l-7.908-7.83c-.27-.268-.27-.701 0-.969.271-.268.709-.268.979 0L10 13.25l7.418-7.141z"></path></svg></button></div><noscript></noscript></div></span><span><div class="opblock opblock-put" id="operations-Company-put_app_company_update"><div class="opblock-summary opblock-summary-put"><button aria-expanded="false" class="opblock-summary-control"><span class="opblock-summary-method">PUT</span><div class="opblock-summary-path-description-wrapper"><span class="opblock-summary-path" data-path="/api/v1/company"><a class="nostyle"><span>/api<wbr>/v1<wbr>/company</span></a></span><div class="opblock-summary-description">Update company information (admin only)</div></div></button><div class="view-line-link copy-to-clipboard" title="Copy to clipboard"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 15 16" width="15" height="16" aria-hidden="true" focusable="false"><g transform="translate(2, -1)"><path fill="#ffffff" fill-rule="evenodd" d="M2 13h4v1H2v-1zm5-6H2v1h5V7zm2 3V8l-3 3 3 3v-2h5v-2H9zM4.5 9H2v1h2.5V9zM2 12h2.5v-1H2v1zm9 1h1v2c-.02.28-.11.52-.3.7-.19.18-.42.28-.7.3H1c-.55 0-1-.45-1-1V4c0-.55.45-1 1-1h3c0-1.11.89-2 2-2 1.11 0 2 .89 2 2h3c.55 0 1 .45 1 1v5h-1V6H1v9h10v-2zM2 5h8c0-.55-.45-1-1-1H8c-.55 0-1-.45-1-1s-.45-1-1-1-1 .45-1 1-.45 1-1 1H3c-.55 0-1 .45-1 1z"></path></g></svg></div><button class="authorization__btn" aria-label="authorization button unlocked"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" class="unlocked" width="20" height="20" aria-hidden="true" focusable="false"><path d="M15.8 8H14V5.6C14 2.703 12.665 1 10 1 7.334 1 6 2.703 6 5.6V6h2v-.801C8 3.754 8.797 3 10 3c1.203 0 2 .754 2 2.199V8H4c-.553 0-1 .646-1 1.199V17c0 .549.428 1.139.951 1.307l1.197.387C5.672 18.861 6.55 19 7.1 19h5.8c.549 0 1.428-.139 1.951-.307l1.196-.387c.524-.167.953-.757.953-1.306V9.199C17 8.646 16.352 8 15.8 8z"></path></svg></button><button aria-label="put ​/api​/v1​/company" class="opblock-control-arrow" aria-expanded="false" tabindex="-1"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" class="arrow" width="20" height="20" aria-hidden="true" focusable="false"><path d="M17.418 6.109c.272-.268.709-.268.979 0s.271.701 0 .969l-7.908 7.83c-.27.268-.707.268-.979 0l-7.908-7.83c-.27-.268-.27-.701 0-.969.271-.268.709-.268.979 0L10 13.25l7.418-7.141z"></path></svg></button></div><noscript></noscript></div></span></div> </div></div></span><span><div class="opblock-tag-section is-open"><h3 class="opblock-tag" id="operations-tag-Dashboard" data-tag="Dashboard" data-is-open="true"><a class="nostyle"><span>Dashboard</span></a><small><div class="renderedMarkdown"><p>Analytics and reporting</p></div></small><button aria-expanded="true" class="expand-operation" title="Collapse operation"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" class="arrow" width="20" height="20" aria-hidden="true" focusable="false"><path d="M 17.418 14.908 C 17.69 15.176 18.127 15.176 18.397 14.908 C 18.667 14.64 18.668 14.207 18.397 13.939 L 10.489 6.109 C 10.219 5.841 9.782 5.841 9.51 6.109 L 1.602 13.939 C 1.332 14.207 1.332 14.64 1.602 14.908 C 1.873 15.176 2.311 15.176 2.581 14.908 L 10 7.767 L 17.418 14.908 Z"></path></svg></button></h3><div class="no-margin"> <div class="operation-tag-content"><span><div class="opblock opblock-get" id="operations-Dashboard-get_app_dashboard_consultant"><div class="opblock-summary opblock-summary-get"><button aria-expanded="false" class="opblock-summary-control"><span class="opblock-summary-method">GET</span><div class="opblock-summary-path-description-wrapper"><span class="opblock-summary-path" data-path="/api/v1/dashboard/consultant/{consultantId}"><a class="nostyle"><span>/api<wbr>/v1<wbr>/dashboard<wbr>/consultant<wbr>/{consultantId}</span></a></span><div class="opblock-summary-description">Get dashboard data for a specific consultant</div></div></button><div class="view-line-link copy-to-clipboard" title="Copy to clipboard"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 15 16" width="15" height="16" aria-hidden="true" focusable="false"><g transform="translate(2, -1)"><path fill="#ffffff" fill-rule="evenodd" d="M2 13h4v1H2v-1zm5-6H2v1h5V7zm2 3V8l-3 3 3 3v-2h5v-2H9zM4.5 9H2v1h2.5V9zM2 12h2.5v-1H2v1zm9 1h1v2c-.02.28-.11.52-.3.7-.19.18-.42.28-.7.3H1c-.55 0-1-.45-1-1V4c0-.55.45-1 1-1h3c0-1.11.89-2 2-2 1.11 0 2 .89 2 2h3c.55 0 1 .45 1 1v5h-1V6H1v9h10v-2zM2 5h8c0-.55-.45-1-1-1H8c-.55 0-1-.45-1-1s-.45-1-1-1-1 .45-1 1-.45 1-1 1H3c-.55 0-1 .45-1 1z"></path></g></svg></div><button class="authorization__btn" aria-label="authorization button unlocked"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" class="unlocked" width="20" height="20" aria-hidden="true" focusable="false"><path d="M15.8 8H14V5.6C14 2.703 12.665 1 10 1 7.334 1 6 2.703 6 5.6V6h2v-.801C8 3.754 8.797 3 10 3c1.203 0 2 .754 2 2.199V8H4c-.553 0-1 .646-1 1.199V17c0 .549.428 1.139.951 1.307l1.197.387C5.672 18.861 6.55 19 7.1 19h5.8c.549 0 1.428-.139 1.951-.307l1.196-.387c.524-.167.953-.757.953-1.306V9.199C17 8.646 16.352 8 15.8 8z"></path></svg></button><button aria-label="get ​/api​/v1​/dashboard​/consultant​/{consultantId}" class="opblock-control-arrow" aria-expanded="false" tabindex="-1"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" class="arrow" width="20" height="20" aria-hidden="true" focusable="false"><path d="M17.418 6.109c.272-.268.709-.268.979 0s.271.701 0 .969l-7.908 7.83c-.27.268-.707.268-.979 0l-7.908-7.83c-.27-.268-.27-.701 0-.969.271-.268.709-.268.979 0L10 13.25l7.418-7.141z"></path></svg></button></div><noscript></noscript></div></span><span><div class="opblock opblock-get" id="operations-Dashboard-get_app_dashboard_admin"><div class="opblock-summary opblock-summary-get"><button aria-expanded="false" class="opblock-summary-control"><span class="opblock-summary-method">GET</span><div class="opblock-summary-path-description-wrapper"><span class="opblock-summary-path" data-path="/api/v1/dashboard/admin"><a class="nostyle"><span>/api<wbr>/v1<wbr>/dashboard<wbr>/admin</span></a></span><div class="opblock-summary-description">Get admin dashboard data (admin only)</div></div></button><div class="view-line-link copy-to-clipboard" title="Copy to clipboard"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 15 16" width="15" height="16" aria-hidden="true" focusable="false"><g transform="translate(2, -1)"><path fill="#ffffff" fill-rule="evenodd" d="M2 13h4v1H2v-1zm5-6H2v1h5V7zm2 3V8l-3 3 3 3v-2h5v-2H9zM4.5 9H2v1h2.5V9zM2 12h2.5v-1H2v1zm9 1h1v2c-.02.28-.11.52-.3.7-.19.18-.42.28-.7.3H1c-.55 0-1-.45-1-1V4c0-.55.45-1 1-1h3c0-1.11.89-2 2-2 1.11 0 2 .89 2 2h3c.55 0 1 .45 1 1v5h-1V6H1v9h10v-2zM2 5h8c0-.55-.45-1-1-1H8c-.55 0-1-.45-1-1s-.45-1-1-1-1 .45-1 1-.45 1-1 1H3c-.55 0-1 .45-1 1z"></path></g></svg></div><button class="authorization__btn" aria-label="authorization button unlocked"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" class="unlocked" width="20" height="20" aria-hidden="true" focusable="false"><path d="M15.8 8H14V5.6C14 2.703 12.665 1 10 1 7.334 1 6 2.703 6 5.6V6h2v-.801C8 3.754 8.797 3 10 3c1.203 0 2 .754 2 2.199V8H4c-.553 0-1 .646-1 1.199V17c0 .549.428 1.139.951 1.307l1.197.387C5.672 18.861 6.55 19 7.1 19h5.8c.549 0 1.428-.139 1.951-.307l1.196-.387c.524-.167.953-.757.953-1.306V9.199C17 8.646 16.352 8 15.8 8z"></path></svg></button><button aria-label="get ​/api​/v1​/dashboard​/admin" class="opblock-control-arrow" aria-expanded="false" tabindex="-1"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" class="arrow" width="20" height="20" aria-hidden="true" focusable="false"><path d="M17.418 6.109c.272-.268.709-.268.979 0s.271.701 0 .969l-7.908 7.83c-.27.268-.707.268-.979 0l-7.908-7.83c-.27-.268-.27-.701 0-.969.271-.268.709-.268.979 0L10 13.25l7.418-7.141z"></path></svg></button></div><noscript></noscript></div></span></div> </div></div></span><span><div class="opblock-tag-section is-open"><h3 class="opblock-tag" id="operations-tag-Clients" data-tag="Clients" data-is-open="true"><a class="nostyle"><span>Clients</span></a><small><div class="renderedMarkdown"><p>Client management and information</p></div></small><button aria-expanded="true" class="expand-operation" title="Collapse operation"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" class="arrow" width="20" height="20" aria-hidden="true" focusable="false"><path d="M 17.418 14.908 C 17.69 15.176 18.127 15.176 18.397 14.908 C 18.667 14.64 18.668 14.207 18.397 13.939 L 10.489 6.109 C 10.219 5.841 9.782 5.841 9.51 6.109 L 1.602 13.939 C 1.332 14.207 1.332 14.64 1.602 14.908 C 1.873 15.176 2.311 15.176 2.581 14.908 L 10 7.767 L 17.418 14.908 Z"></path></svg></button></h3><div class="no-margin"> <div class="operation-tag-content"><span><div class="opblock opblock-get" id="operations-Clients-get_app_client_index"><div class="opblock-summary opblock-summary-get"><button aria-expanded="false" class="opblock-summary-control"><span class="opblock-summary-method">GET</span><div class="opblock-summary-path-description-wrapper"><span class="opblock-summary-path" data-path="/api/v1/clients"><a class="nostyle"><span>/api<wbr>/v1<wbr>/clients</span></a></span><div class="opblock-summary-description">Get all clients</div></div></button><div class="view-line-link copy-to-clipboard" title="Copy to clipboard"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 15 16" width="15" height="16" aria-hidden="true" focusable="false"><g transform="translate(2, -1)"><path fill="#ffffff" fill-rule="evenodd" d="M2 13h4v1H2v-1zm5-6H2v1h5V7zm2 3V8l-3 3 3 3v-2h5v-2H9zM4.5 9H2v1h2.5V9zM2 12h2.5v-1H2v1zm9 1h1v2c-.02.28-.11.52-.3.7-.19.18-.42.28-.7.3H1c-.55 0-1-.45-1-1V4c0-.55.45-1 1-1h3c0-1.11.89-2 2-2 1.11 0 2 .89 2 2h3c.55 0 1 .45 1 1v5h-1V6H1v9h10v-2zM2 5h8c0-.55-.45-1-1-1H8c-.55 0-1-.45-1-1s-.45-1-1-1-1 .45-1 1-.45 1-1 1H3c-.55 0-1 .45-1 1z"></path></g></svg></div><button class="authorization__btn" aria-label="authorization button unlocked"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" class="unlocked" width="20" height="20" aria-hidden="true" focusable="false"><path d="M15.8 8H14V5.6C14 2.703 12.665 1 10 1 7.334 1 6 2.703 6 5.6V6h2v-.801C8 3.754 8.797 3 10 3c1.203 0 2 .754 2 2.199V8H4c-.553 0-1 .646-1 1.199V17c0 .549.428 1.139.951 1.307l1.197.387C5.672 18.861 6.55 19 7.1 19h5.8c.549 0 1.428-.139 1.951-.307l1.196-.387c.524-.167.953-.757.953-1.306V9.199C17 8.646 16.352 8 15.8 8z"></path></svg></button><button aria-label="get ​/api​/v1​/clients" class="opblock-control-arrow" aria-expanded="false" tabindex="-1"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" class="arrow" width="20" height="20" aria-hidden="true" focusable="false"><path d="M17.418 6.109c.272-.268.709-.268.979 0s.271.701 0 .969l-7.908 7.83c-.27.268-.707.268-.979 0l-7.908-7.83c-.27-.268-.27-.701 0-.969.271-.268.709-.268.979 0L10 13.25l7.418-7.141z"></path></svg></button></div><noscript></noscript></div></span><span><div class="opblock opblock-post" id="operations-Clients-post_app_client_create"><div class="opblock-summary opblock-summary-post"><button aria-expanded="false" class="opblock-summary-control"><span class="opblock-summary-method">POST</span><div class="opblock-summary-path-description-wrapper"><span class="opblock-summary-path" data-path="/api/v1/clients"><a class="nostyle"><span>/api<wbr>/v1<wbr>/clients</span></a></span><div class="opblock-summary-description">Create a new client (admin only)</div></div></button><div class="view-line-link copy-to-clipboard" title="Copy to clipboard"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 15 16" width="15" height="16" aria-hidden="true" focusable="false"><g transform="translate(2, -1)"><path fill="#ffffff" fill-rule="evenodd" d="M2 13h4v1H2v-1zm5-6H2v1h5V7zm2 3V8l-3 3 3 3v-2h5v-2H9zM4.5 9H2v1h2.5V9zM2 12h2.5v-1H2v1zm9 1h1v2c-.02.28-.11.52-.3.7-.19.18-.42.28-.7.3H1c-.55 0-1-.45-1-1V4c0-.55.45-1 1-1h3c0-1.11.89-2 2-2 1.11 0 2 .89 2 2h3c.55 0 1 .45 1 1v5h-1V6H1v9h10v-2zM2 5h8c0-.55-.45-1-1-1H8c-.55 0-1-.45-1-1s-.45-1-1-1-1 .45-1 1-.45 1-1 1H3c-.55 0-1 .45-1 1z"></path></g></svg></div><button class="authorization__btn" aria-label="authorization button unlocked"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" class="unlocked" width="20" height="20" aria-hidden="true" focusable="false"><path d="M15.8 8H14V5.6C14 2.703 12.665 1 10 1 7.334 1 6 2.703 6 5.6V6h2v-.801C8 3.754 8.797 3 10 3c1.203 0 2 .754 2 2.199V8H4c-.553 0-1 .646-1 1.199V17c0 .549.428 1.139.951 1.307l1.197.387C5.672 18.861 6.55 19 7.1 19h5.8c.549 0 1.428-.139 1.951-.307l1.196-.387c.524-.167.953-.757.953-1.306V9.199C17 8.646 16.352 8 15.8 8z"></path></svg></button><button aria-label="post ​/api​/v1​/clients" class="opblock-control-arrow" aria-expanded="false" tabindex="-1"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" class="arrow" width="20" height="20" aria-hidden="true" focusable="false"><path d="M17.418 6.109c.272-.268.709-.268.979 0s.271.701 0 .969l-7.908 7.83c-.27.268-.707.268-.979 0l-7.908-7.83c-.27-.268-.27-.701 0-.969.271-.268.709-.268.979 0L10 13.25l7.418-7.141z"></path></svg></button></div><noscript></noscript></div></span><span><div class="opblock opblock-get" id="operations-Clients-get_app_client_show"><div class="opblock-summary opblock-summary-get"><button aria-expanded="false" class="opblock-summary-control"><span class="opblock-summary-method">GET</span><div class="opblock-summary-path-description-wrapper"><span class="opblock-summary-path" data-path="/api/v1/clients/{id}"><a class="nostyle"><span>/api<wbr>/v1<wbr>/clients<wbr>/{id}</span></a></span><div class="opblock-summary-description">Get a specific client</div></div></button><div class="view-line-link copy-to-clipboard" title="Copy to clipboard"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 15 16" width="15" height="16" aria-hidden="true" focusable="false"><g transform="translate(2, -1)"><path fill="#ffffff" fill-rule="evenodd" d="M2 13h4v1H2v-1zm5-6H2v1h5V7zm2 3V8l-3 3 3 3v-2h5v-2H9zM4.5 9H2v1h2.5V9zM2 12h2.5v-1H2v1zm9 1h1v2c-.02.28-.11.52-.3.7-.19.18-.42.28-.7.3H1c-.55 0-1-.45-1-1V4c0-.55.45-1 1-1h3c0-1.11.89-2 2-2 1.11 0 2 .89 2 2h3c.55 0 1 .45 1 1v5h-1V6H1v9h10v-2zM2 5h8c0-.55-.45-1-1-1H8c-.55 0-1-.45-1-1s-.45-1-1-1-1 .45-1 1-.45 1-1 1H3c-.55 0-1 .45-1 1z"></path></g></svg></div><button class="authorization__btn" aria-label="authorization button unlocked"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" class="unlocked" width="20" height="20" aria-hidden="true" focusable="false"><path d="M15.8 8H14V5.6C14 2.703 12.665 1 10 1 7.334 1 6 2.703 6 5.6V6h2v-.801C8 3.754 8.797 3 10 3c1.203 0 2 .754 2 2.199V8H4c-.553 0-1 .646-1 1.199V17c0 .549.428 1.139.951 1.307l1.197.387C5.672 18.861 6.55 19 7.1 19h5.8c.549 0 1.428-.139 1.951-.307l1.196-.387c.524-.167.953-.757.953-1.306V9.199C17 8.646 16.352 8 15.8 8z"></path></svg></button><button aria-label="get ​/api​/v1​/clients​/{id}" class="opblock-control-arrow" aria-expanded="false" tabindex="-1"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" class="arrow" width="20" height="20" aria-hidden="true" focusable="false"><path d="M17.418 6.109c.272-.268.709-.268.979 0s.271.701 0 .969l-7.908 7.83c-.27.268-.707.268-.979 0l-7.908-7.83c-.27-.268-.27-.701 0-.969.271-.268.709-.268.979 0L10 13.25l7.418-7.141z"></path></svg></button></div><noscript></noscript></div></span><span><div class="opblock opblock-put" id="operations-Clients-put_app_client_update"><div class="opblock-summary opblock-summary-put"><button aria-expanded="false" class="opblock-summary-control"><span class="opblock-summary-method">PUT</span><div class="opblock-summary-path-description-wrapper"><span class="opblock-summary-path" data-path="/api/v1/clients/{id}"><a class="nostyle"><span>/api<wbr>/v1<wbr>/clients<wbr>/{id}</span></a></span><div class="opblock-summary-description">Update a client (admin only)</div></div></button><div class="view-line-link copy-to-clipboard" title="Copy to clipboard"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 15 16" width="15" height="16" aria-hidden="true" focusable="false"><g transform="translate(2, -1)"><path fill="#ffffff" fill-rule="evenodd" d="M2 13h4v1H2v-1zm5-6H2v1h5V7zm2 3V8l-3 3 3 3v-2h5v-2H9zM4.5 9H2v1h2.5V9zM2 12h2.5v-1H2v1zm9 1h1v2c-.02.28-.11.52-.3.7-.19.18-.42.28-.7.3H1c-.55 0-1-.45-1-1V4c0-.55.45-1 1-1h3c0-1.11.89-2 2-2 1.11 0 2 .89 2 2h3c.55 0 1 .45 1 1v5h-1V6H1v9h10v-2zM2 5h8c0-.55-.45-1-1-1H8c-.55 0-1-.45-1-1s-.45-1-1-1-1 .45-1 1-.45 1-1 1H3c-.55 0-1 .45-1 1z"></path></g></svg></div><button class="authorization__btn" aria-label="authorization button unlocked"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" class="unlocked" width="20" height="20" aria-hidden="true" focusable="false"><path d="M15.8 8H14V5.6C14 2.703 12.665 1 10 1 7.334 1 6 2.703 6 5.6V6h2v-.801C8 3.754 8.797 3 10 3c1.203 0 2 .754 2 2.199V8H4c-.553 0-1 .646-1 1.199V17c0 .549.428 1.139.951 1.307l1.197.387C5.672 18.861 6.55 19 7.1 19h5.8c.549 0 1.428-.139 1.951-.307l1.196-.387c.524-.167.953-.757.953-1.306V9.199C17 8.646 16.352 8 15.8 8z"></path></svg></button><button aria-label="put ​/api​/v1​/clients​/{id}" class="opblock-control-arrow" aria-expanded="false" tabindex="-1"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" class="arrow" width="20" height="20" aria-hidden="true" focusable="false"><path d="M17.418 6.109c.272-.268.709-.268.979 0s.271.701 0 .969l-7.908 7.83c-.27.268-.707.268-.979 0l-7.908-7.83c-.27-.268-.27-.701 0-.969.271-.268.709-.268.979 0L10 13.25l7.418-7.141z"></path></svg></button></div><noscript></noscript></div></span><span><div class="opblock opblock-delete" id="operations-Clients-delete_app_client_delete"><div class="opblock-summary opblock-summary-delete"><button aria-expanded="false" class="opblock-summary-control"><span class="opblock-summary-method">DELETE</span><div class="opblock-summary-path-description-wrapper"><span class="opblock-summary-path" data-path="/api/v1/clients/{id}"><a class="nostyle"><span>/api<wbr>/v1<wbr>/clients<wbr>/{id}</span></a></span><div class="opblock-summary-description">Delete a client (admin only)</div></div></button><div class="view-line-link copy-to-clipboard" title="Copy to clipboard"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 15 16" width="15" height="16" aria-hidden="true" focusable="false"><g transform="translate(2, -1)"><path fill="#ffffff" fill-rule="evenodd" d="M2 13h4v1H2v-1zm5-6H2v1h5V7zm2 3V8l-3 3 3 3v-2h5v-2H9zM4.5 9H2v1h2.5V9zM2 12h2.5v-1H2v1zm9 1h1v2c-.02.28-.11.52-.3.7-.19.18-.42.28-.7.3H1c-.55 0-1-.45-1-1V4c0-.55.45-1 1-1h3c0-1.11.89-2 2-2 1.11 0 2 .89 2 2h3c.55 0 1 .45 1 1v5h-1V6H1v9h10v-2zM2 5h8c0-.55-.45-1-1-1H8c-.55 0-1-.45-1-1s-.45-1-1-1-1 .45-1 1-.45 1-1 1H3c-.55 0-1 .45-1 1z"></path></g></svg></div><button class="authorization__btn" aria-label="authorization button unlocked"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" class="unlocked" width="20" height="20" aria-hidden="true" focusable="false"><path d="M15.8 8H14V5.6C14 2.703 12.665 1 10 1 7.334 1 6 2.703 6 5.6V6h2v-.801C8 3.754 8.797 3 10 3c1.203 0 2 .754 2 2.199V8H4c-.553 0-1 .646-1 1.199V17c0 .549.428 1.139.951 1.307l1.197.387C5.672 18.861 6.55 19 7.1 19h5.8c.549 0 1.428-.139 1.951-.307l1.196-.387c.524-.167.953-.757.953-1.306V9.199C17 8.646 16.352 8 15.8 8z"></path></svg></button><button aria-label="delete ​/api​/v1​/clients​/{id}" class="opblock-control-arrow" aria-expanded="false" tabindex="-1"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" class="arrow" width="20" height="20" aria-hidden="true" focusable="false"><path d="M17.418 6.109c.272-.268.709-.268.979 0s.271.701 0 .969l-7.908 7.83c-.27.268-.707.268-.979 0l-7.908-7.83c-.27-.268-.27-.701 0-.969.271-.268.709-.268.979 0L10 13.25l7.418-7.141z"></path></svg></button></div><noscript></noscript></div></span></div> </div></div></span><span><div class="opblock-tag-section is-open"><h3 class="opblock-tag" id="operations-tag-Trips" data-tag="Trips" data-is-open="true"><a class="nostyle"><span>Trips</span></a><small><div class="renderedMarkdown"><p>Business trip planning and tracking</p></div></small><button aria-expanded="true" class="expand-operation" title="Collapse operation"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" class="arrow" width="20" height="20" aria-hidden="true" focusable="false"><path d="M 17.418 14.908 C 17.69 15.176 18.127 15.176 18.397 14.908 C 18.667 14.64 18.668 14.207 18.397 13.939 L 10.489 6.109 C 10.219 5.841 9.782 5.841 9.51 6.109 L 1.602 13.939 C 1.332 14.207 1.332 14.64 1.602 14.908 C 1.873 15.176 2.311 15.176 2.581 14.908 L 10 7.767 L 17.418 14.908 Z"></path></svg></button></h3><div class="no-margin"> <div class="operation-tag-content"><span><div class="opblock opblock-get" id="operations-Trips-get_app_trip_index"><div class="opblock-summary opblock-summary-get"><button aria-expanded="false" class="opblock-summary-control"><span class="opblock-summary-method">GET</span><div class="opblock-summary-path-description-wrapper"><span class="opblock-summary-path" data-path="/api/v1/trips"><a class="nostyle"><span>/api<wbr>/v1<wbr>/trips</span></a></span><div class="opblock-summary-description">Get all trips (admin) or consultant's trips</div></div></button><div class="view-line-link copy-to-clipboard" title="Copy to clipboard"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 15 16" width="15" height="16" aria-hidden="true" focusable="false"><g transform="translate(2, -1)"><path fill="#ffffff" fill-rule="evenodd" d="M2 13h4v1H2v-1zm5-6H2v1h5V7zm2 3V8l-3 3 3 3v-2h5v-2H9zM4.5 9H2v1h2.5V9zM2 12h2.5v-1H2v1zm9 1h1v2c-.02.28-.11.52-.3.7-.19.18-.42.28-.7.3H1c-.55 0-1-.45-1-1V4c0-.55.45-1 1-1h3c0-1.11.89-2 2-2 1.11 0 2 .89 2 2h3c.55 0 1 .45 1 1v5h-1V6H1v9h10v-2zM2 5h8c0-.55-.45-1-1-1H8c-.55 0-1-.45-1-1s-.45-1-1-1-1 .45-1 1-.45 1-1 1H3c-.55 0-1 .45-1 1z"></path></g></svg></div><button class="authorization__btn" aria-label="authorization button unlocked"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" class="unlocked" width="20" height="20" aria-hidden="true" focusable="false"><path d="M15.8 8H14V5.6C14 2.703 12.665 1 10 1 7.334 1 6 2.703 6 5.6V6h2v-.801C8 3.754 8.797 3 10 3c1.203 0 2 .754 2 2.199V8H4c-.553 0-1 .646-1 1.199V17c0 .549.428 1.139.951 1.307l1.197.387C5.672 18.861 6.55 19 7.1 19h5.8c.549 0 1.428-.139 1.951-.307l1.196-.387c.524-.167.953-.757.953-1.306V9.199C17 8.646 16.352 8 15.8 8z"></path></svg></button><button aria-label="get ​/api​/v1​/trips" class="opblock-control-arrow" aria-expanded="false" tabindex="-1"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" class="arrow" width="20" height="20" aria-hidden="true" focusable="false"><path d="M17.418 6.109c.272-.268.709-.268.979 0s.271.701 0 .969l-7.908 7.83c-.27.268-.707.268-.979 0l-7.908-7.83c-.27-.268-.27-.701 0-.969.271-.268.709-.268.979 0L10 13.25l7.418-7.141z"></path></svg></button></div><noscript></noscript></div></span><span><div class="opblock opblock-post" id="operations-Trips-post_app_trip_create"><div class="opblock-summary opblock-summary-post"><button aria-expanded="false" class="opblock-summary-control"><span class="opblock-summary-method">POST</span><div class="opblock-summary-path-description-wrapper"><span class="opblock-summary-path" data-path="/api/v1/trips"><a class="nostyle"><span>/api<wbr>/v1<wbr>/trips</span></a></span><div class="opblock-summary-description">Create a new trip</div></div></button><div class="view-line-link copy-to-clipboard" title="Copy to clipboard"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 15 16" width="15" height="16" aria-hidden="true" focusable="false"><g transform="translate(2, -1)"><path fill="#ffffff" fill-rule="evenodd" d="M2 13h4v1H2v-1zm5-6H2v1h5V7zm2 3V8l-3 3 3 3v-2h5v-2H9zM4.5 9H2v1h2.5V9zM2 12h2.5v-1H2v1zm9 1h1v2c-.02.28-.11.52-.3.7-.19.18-.42.28-.7.3H1c-.55 0-1-.45-1-1V4c0-.55.45-1 1-1h3c0-1.11.89-2 2-2 1.11 0 2 .89 2 2h3c.55 0 1 .45 1 1v5h-1V6H1v9h10v-2zM2 5h8c0-.55-.45-1-1-1H8c-.55 0-1-.45-1-1s-.45-1-1-1-1 .45-1 1-.45 1-1 1H3c-.55 0-1 .45-1 1z"></path></g></svg></div><button class="authorization__btn" aria-label="authorization button unlocked"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" class="unlocked" width="20" height="20" aria-hidden="true" focusable="false"><path d="M15.8 8H14V5.6C14 2.703 12.665 1 10 1 7.334 1 6 2.703 6 5.6V6h2v-.801C8 3.754 8.797 3 10 3c1.203 0 2 .754 2 2.199V8H4c-.553 0-1 .646-1 1.199V17c0 .549.428 1.139.951 1.307l1.197.387C5.672 18.861 6.55 19 7.1 19h5.8c.549 0 1.428-.139 1.951-.307l1.196-.387c.524-.167.953-.757.953-1.306V9.199C17 8.646 16.352 8 15.8 8z"></path></svg></button><button aria-label="post ​/api​/v1​/trips" class="opblock-control-arrow" aria-expanded="false" tabindex="-1"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" class="arrow" width="20" height="20" aria-hidden="true" focusable="false"><path d="M17.418 6.109c.272-.268.709-.268.979 0s.271.701 0 .969l-7.908 7.83c-.27.268-.707.268-.979 0l-7.908-7.83c-.27-.268-.27-.701 0-.969.271-.268.709-.268.979 0L10 13.25l7.418-7.141z"></path></svg></button></div><noscript></noscript></div></span><span><div class="opblock opblock-get" id="operations-Trips-get_app_trip_show"><div class="opblock-summary opblock-summary-get"><button aria-expanded="false" class="opblock-summary-control"><span class="opblock-summary-method">GET</span><div class="opblock-summary-path-description-wrapper"><span class="opblock-summary-path" data-path="/api/v1/trips/{id}"><a class="nostyle"><span>/api<wbr>/v1<wbr>/trips<wbr>/{id}</span></a></span><div class="opblock-summary-description">Get a specific trip</div></div></button><div class="view-line-link copy-to-clipboard" title="Copy to clipboard"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 15 16" width="15" height="16" aria-hidden="true" focusable="false"><g transform="translate(2, -1)"><path fill="#ffffff" fill-rule="evenodd" d="M2 13h4v1H2v-1zm5-6H2v1h5V7zm2 3V8l-3 3 3 3v-2h5v-2H9zM4.5 9H2v1h2.5V9zM2 12h2.5v-1H2v1zm9 1h1v2c-.02.28-.11.52-.3.7-.19.18-.42.28-.7.3H1c-.55 0-1-.45-1-1V4c0-.55.45-1 1-1h3c0-1.11.89-2 2-2 1.11 0 2 .89 2 2h3c.55 0 1 .45 1 1v5h-1V6H1v9h10v-2zM2 5h8c0-.55-.45-1-1-1H8c-.55 0-1-.45-1-1s-.45-1-1-1-1 .45-1 1-.45 1-1 1H3c-.55 0-1 .45-1 1z"></path></g></svg></div><button class="authorization__btn" aria-label="authorization button unlocked"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" class="unlocked" width="20" height="20" aria-hidden="true" focusable="false"><path d="M15.8 8H14V5.6C14 2.703 12.665 1 10 1 7.334 1 6 2.703 6 5.6V6h2v-.801C8 3.754 8.797 3 10 3c1.203 0 2 .754 2 2.199V8H4c-.553 0-1 .646-1 1.199V17c0 .549.428 1.139.951 1.307l1.197.387C5.672 18.861 6.55 19 7.1 19h5.8c.549 0 1.428-.139 1.951-.307l1.196-.387c.524-.167.953-.757.953-1.306V9.199C17 8.646 16.352 8 15.8 8z"></path></svg></button><button aria-label="get ​/api​/v1​/trips​/{id}" class="opblock-control-arrow" aria-expanded="false" tabindex="-1"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" class="arrow" width="20" height="20" aria-hidden="true" focusable="false"><path d="M17.418 6.109c.272-.268.709-.268.979 0s.271.701 0 .969l-7.908 7.83c-.27.268-.707.268-.979 0l-7.908-7.83c-.27-.268-.27-.701 0-.969.271-.268.709-.268.979 0L10 13.25l7.418-7.141z"></path></svg></button></div><noscript></noscript></div></span><span><div class="opblock opblock-put" id="operations-Trips-put_app_trip_update"><div class="opblock-summary opblock-summary-put"><button aria-expanded="false" class="opblock-summary-control"><span class="opblock-summary-method">PUT</span><div class="opblock-summary-path-description-wrapper"><span class="opblock-summary-path" data-path="/api/v1/trips/{id}"><a class="nostyle"><span>/api<wbr>/v1<wbr>/trips<wbr>/{id}</span></a></span><div class="opblock-summary-description">Update a trip</div></div></button><div class="view-line-link copy-to-clipboard" title="Copy to clipboard"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 15 16" width="15" height="16" aria-hidden="true" focusable="false"><g transform="translate(2, -1)"><path fill="#ffffff" fill-rule="evenodd" d="M2 13h4v1H2v-1zm5-6H2v1h5V7zm2 3V8l-3 3 3 3v-2h5v-2H9zM4.5 9H2v1h2.5V9zM2 12h2.5v-1H2v1zm9 1h1v2c-.02.28-.11.52-.3.7-.19.18-.42.28-.7.3H1c-.55 0-1-.45-1-1V4c0-.55.45-1 1-1h3c0-1.11.89-2 2-2 1.11 0 2 .89 2 2h3c.55 0 1 .45 1 1v5h-1V6H1v9h10v-2zM2 5h8c0-.55-.45-1-1-1H8c-.55 0-1-.45-1-1s-.45-1-1-1-1 .45-1 1-.45 1-1 1H3c-.55 0-1 .45-1 1z"></path></g></svg></div><button class="authorization__btn" aria-label="authorization button unlocked"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" class="unlocked" width="20" height="20" aria-hidden="true" focusable="false"><path d="M15.8 8H14V5.6C14 2.703 12.665 1 10 1 7.334 1 6 2.703 6 5.6V6h2v-.801C8 3.754 8.797 3 10 3c1.203 0 2 .754 2 2.199V8H4c-.553 0-1 .646-1 1.199V17c0 .549.428 1.139.951 1.307l1.197.387C5.672 18.861 6.55 19 7.1 19h5.8c.549 0 1.428-.139 1.951-.307l1.196-.387c.524-.167.953-.757.953-1.306V9.199C17 8.646 16.352 8 15.8 8z"></path></svg></button><button aria-label="put ​/api​/v1​/trips​/{id}" class="opblock-control-arrow" aria-expanded="false" tabindex="-1"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" class="arrow" width="20" height="20" aria-hidden="true" focusable="false"><path d="M17.418 6.109c.272-.268.709-.268.979 0s.271.701 0 .969l-7.908 7.83c-.27.268-.707.268-.979 0l-7.908-7.83c-.27-.268-.27-.701 0-.969.271-.268.709-.268.979 0L10 13.25l7.418-7.141z"></path></svg></button></div><noscript></noscript></div></span><span><div class="opblock opblock-delete" id="operations-Trips-delete_app_trip_delete"><div class="opblock-summary opblock-summary-delete"><button aria-expanded="false" class="opblock-summary-control"><span class="opblock-summary-method">DELETE</span><div class="opblock-summary-path-description-wrapper"><span class="opblock-summary-path" data-path="/api/v1/trips/{id}"><a class="nostyle"><span>/api<wbr>/v1<wbr>/trips<wbr>/{id}</span></a></span><div class="opblock-summary-description">Delete a trip</div></div></button><div class="view-line-link copy-to-clipboard" title="Copy to clipboard"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 15 16" width="15" height="16" aria-hidden="true" focusable="false"><g transform="translate(2, -1)"><path fill="#ffffff" fill-rule="evenodd" d="M2 13h4v1H2v-1zm5-6H2v1h5V7zm2 3V8l-3 3 3 3v-2h5v-2H9zM4.5 9H2v1h2.5V9zM2 12h2.5v-1H2v1zm9 1h1v2c-.02.28-.11.52-.3.7-.19.18-.42.28-.7.3H1c-.55 0-1-.45-1-1V4c0-.55.45-1 1-1h3c0-1.11.89-2 2-2 1.11 0 2 .89 2 2h3c.55 0 1 .45 1 1v5h-1V6H1v9h10v-2zM2 5h8c0-.55-.45-1-1-1H8c-.55 0-1-.45-1-1s-.45-1-1-1-1 .45-1 1-.45 1-1 1H3c-.55 0-1 .45-1 1z"></path></g></svg></div><button class="authorization__btn" aria-label="authorization button unlocked"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" class="unlocked" width="20" height="20" aria-hidden="true" focusable="false"><path d="M15.8 8H14V5.6C14 2.703 12.665 1 10 1 7.334 1 6 2.703 6 5.6V6h2v-.801C8 3.754 8.797 3 10 3c1.203 0 2 .754 2 2.199V8H4c-.553 0-1 .646-1 1.199V17c0 .549.428 1.139.951 1.307l1.197.387C5.672 18.861 6.55 19 7.1 19h5.8c.549 0 1.428-.139 1.951-.307l1.196-.387c.524-.167.953-.757.953-1.306V9.199C17 8.646 16.352 8 15.8 8z"></path></svg></button><button aria-label="delete ​/api​/v1​/trips​/{id}" class="opblock-control-arrow" aria-expanded="false" tabindex="-1"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" class="arrow" width="20" height="20" aria-hidden="true" focusable="false"><path d="M17.418 6.109c.272-.268.709-.268.979 0s.271.701 0 .969l-7.908 7.83c-.27.268-.707.268-.979 0l-7.908-7.83c-.27-.268-.27-.701 0-.969.271-.268.709-.268.979 0L10 13.25l7.418-7.141z"></path></svg></button></div><noscript></noscript></div></span><span><div class="opblock opblock-get" id="operations-Trips-get_app_trip_my"><div class="opblock-summary opblock-summary-get"><button aria-expanded="false" class="opblock-summary-control"><span class="opblock-summary-method">GET</span><div class="opblock-summary-path-description-wrapper"><span class="opblock-summary-path" data-path="/api/v1/trips/my"><a class="nostyle"><span>/api<wbr>/v1<wbr>/trips<wbr>/my</span></a></span><div class="opblock-summary-description">Get all trips for the current user</div></div></button><div class="view-line-link copy-to-clipboard" title="Copy to clipboard"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 15 16" width="15" height="16" aria-hidden="true" focusable="false"><g transform="translate(2, -1)"><path fill="#ffffff" fill-rule="evenodd" d="M2 13h4v1H2v-1zm5-6H2v1h5V7zm2 3V8l-3 3 3 3v-2h5v-2H9zM4.5 9H2v1h2.5V9zM2 12h2.5v-1H2v1zm9 1h1v2c-.02.28-.11.52-.3.7-.19.18-.42.28-.7.3H1c-.55 0-1-.45-1-1V4c0-.55.45-1 1-1h3c0-1.11.89-2 2-2 1.11 0 2 .89 2 2h3c.55 0 1 .45 1 1v5h-1V6H1v9h10v-2zM2 5h8c0-.55-.45-1-1-1H8c-.55 0-1-.45-1-1s-.45-1-1-1-1 .45-1 1-.45 1-1 1H3c-.55 0-1 .45-1 1z"></path></g></svg></div><button class="authorization__btn" aria-label="authorization button unlocked"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" class="unlocked" width="20" height="20" aria-hidden="true" focusable="false"><path d="M15.8 8H14V5.6C14 2.703 12.665 1 10 1 7.334 1 6 2.703 6 5.6V6h2v-.801C8 3.754 8.797 3 10 3c1.203 0 2 .754 2 2.199V8H4c-.553 0-1 .646-1 1.199V17c0 .549.428 1.139.951 1.307l1.197.387C5.672 18.861 6.55 19 7.1 19h5.8c.549 0 1.428-.139 1.951-.307l1.196-.387c.524-.167.953-.757.953-1.306V9.199C17 8.646 16.352 8 15.8 8z"></path></svg></button><button aria-label="get ​/api​/v1​/trips​/my" class="opblock-control-arrow" aria-expanded="false" tabindex="-1"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" class="arrow" width="20" height="20" aria-hidden="true" focusable="false"><path d="M17.418 6.109c.272-.268.709-.268.979 0s.271.701 0 .969l-7.908 7.83c-.27.268-.707.268-.979 0l-7.908-7.83c-.27-.268-.27-.701 0-.969.271-.268.709-.268.979 0L10 13.25l7.418-7.141z"></path></svg></button></div><noscript></noscript></div></span></div> </div></div></span><span><div class="opblock-tag-section is-open"><h3 class="opblock-tag" id="operations-tag-Expense_Categories" data-tag="Expense Categories" data-is-open="true"><a class="nostyle"><span>Expense Categories</span></a><small><div class="renderedMarkdown"><p>Expense category management and hierarchy</p></div></small><button aria-expanded="true" class="expand-operation" title="Collapse operation"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" class="arrow" width="20" height="20" aria-hidden="true" focusable="false"><path d="M 17.418 14.908 C 17.69 15.176 18.127 15.176 18.397 14.908 C 18.667 14.64 18.668 14.207 18.397 13.939 L 10.489 6.109 C 10.219 5.841 9.782 5.841 9.51 6.109 L 1.602 13.939 C 1.332 14.207 1.332 14.64 1.602 14.908 C 1.873 15.176 2.311 15.176 2.581 14.908 L 10 7.767 L 17.418 14.908 Z"></path></svg></button></h3><div class="no-margin"> <div class="operation-tag-content"><span><div class="opblock opblock-get" id="operations-Expense_Categories-get_app_expense_category_index"><div class="opblock-summary opblock-summary-get"><button aria-expanded="false" class="opblock-summary-control"><span class="opblock-summary-method">GET</span><div class="opblock-summary-path-description-wrapper"><span class="opblock-summary-path" data-path="/api/v1/expense-categories"><a class="nostyle"><span>/api<wbr>/v1<wbr>/expense-categories</span></a></span><div class="opblock-summary-description">Get all expense categories</div></div></button><div class="view-line-link copy-to-clipboard" title="Copy to clipboard"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 15 16" width="15" height="16" aria-hidden="true" focusable="false"><g transform="translate(2, -1)"><path fill="#ffffff" fill-rule="evenodd" d="M2 13h4v1H2v-1zm5-6H2v1h5V7zm2 3V8l-3 3 3 3v-2h5v-2H9zM4.5 9H2v1h2.5V9zM2 12h2.5v-1H2v1zm9 1h1v2c-.02.28-.11.52-.3.7-.19.18-.42.28-.7.3H1c-.55 0-1-.45-1-1V4c0-.55.45-1 1-1h3c0-1.11.89-2 2-2 1.11 0 2 .89 2 2h3c.55 0 1 .45 1 1v5h-1V6H1v9h10v-2zM2 5h8c0-.55-.45-1-1-1H8c-.55 0-1-.45-1-1s-.45-1-1-1-1 .45-1 1-.45 1-1 1H3c-.55 0-1 .45-1 1z"></path></g></svg></div><button class="authorization__btn" aria-label="authorization button unlocked"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" class="unlocked" width="20" height="20" aria-hidden="true" focusable="false"><path d="M15.8 8H14V5.6C14 2.703 12.665 1 10 1 7.334 1 6 2.703 6 5.6V6h2v-.801C8 3.754 8.797 3 10 3c1.203 0 2 .754 2 2.199V8H4c-.553 0-1 .646-1 1.199V17c0 .549.428 1.139.951 1.307l1.197.387C5.672 18.861 6.55 19 7.1 19h5.8c.549 0 1.428-.139 1.951-.307l1.196-.387c.524-.167.953-.757.953-1.306V9.199C17 8.646 16.352 8 15.8 8z"></path></svg></button><button aria-label="get ​/api​/v1​/expense-categories" class="opblock-control-arrow" aria-expanded="false" tabindex="-1"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" class="arrow" width="20" height="20" aria-hidden="true" focusable="false"><path d="M17.418 6.109c.272-.268.709-.268.979 0s.271.701 0 .969l-7.908 7.83c-.27.268-.707.268-.979 0l-7.908-7.83c-.27-.268-.27-.701 0-.969.271-.268.709-.268.979 0L10 13.25l7.418-7.141z"></path></svg></button></div><noscript></noscript></div></span><span><div class="opblock opblock-post" id="operations-Expense_Categories-post_app_expense_category_create"><div class="opblock-summary opblock-summary-post"><button aria-expanded="false" class="opblock-summary-control"><span class="opblock-summary-method">POST</span><div class="opblock-summary-path-description-wrapper"><span class="opblock-summary-path" data-path="/api/v1/expense-categories"><a class="nostyle"><span>/api<wbr>/v1<wbr>/expense-categories</span></a></span><div class="opblock-summary-description">Create a new expense category (admin only)</div></div></button><div class="view-line-link copy-to-clipboard" title="Copy to clipboard"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 15 16" width="15" height="16" aria-hidden="true" focusable="false"><g transform="translate(2, -1)"><path fill="#ffffff" fill-rule="evenodd" d="M2 13h4v1H2v-1zm5-6H2v1h5V7zm2 3V8l-3 3 3 3v-2h5v-2H9zM4.5 9H2v1h2.5V9zM2 12h2.5v-1H2v1zm9 1h1v2c-.02.28-.11.52-.3.7-.19.18-.42.28-.7.3H1c-.55 0-1-.45-1-1V4c0-.55.45-1 1-1h3c0-1.11.89-2 2-2 1.11 0 2 .89 2 2h3c.55 0 1 .45 1 1v5h-1V6H1v9h10v-2zM2 5h8c0-.55-.45-1-1-1H8c-.55 0-1-.45-1-1s-.45-1-1-1-1 .45-1 1-.45 1-1 1H3c-.55 0-1 .45-1 1z"></path></g></svg></div><button class="authorization__btn" aria-label="authorization button unlocked"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" class="unlocked" width="20" height="20" aria-hidden="true" focusable="false"><path d="M15.8 8H14V5.6C14 2.703 12.665 1 10 1 7.334 1 6 2.703 6 5.6V6h2v-.801C8 3.754 8.797 3 10 3c1.203 0 2 .754 2 2.199V8H4c-.553 0-1 .646-1 1.199V17c0 .549.428 1.139.951 1.307l1.197.387C5.672 18.861 6.55 19 7.1 19h5.8c.549 0 1.428-.139 1.951-.307l1.196-.387c.524-.167.953-.757.953-1.306V9.199C17 8.646 16.352 8 15.8 8z"></path></svg></button><button aria-label="post ​/api​/v1​/expense-categories" class="opblock-control-arrow" aria-expanded="false" tabindex="-1"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" class="arrow" width="20" height="20" aria-hidden="true" focusable="false"><path d="M17.418 6.109c.272-.268.709-.268.979 0s.271.701 0 .969l-7.908 7.83c-.27.268-.707.268-.979 0l-7.908-7.83c-.27-.268-.27-.701 0-.969.271-.268.709-.268.979 0L10 13.25l7.418-7.141z"></path></svg></button></div><noscript></noscript></div></span><span><div class="opblock opblock-get" id="operations-Expense_Categories-get_app_expense_category_show"><div class="opblock-summary opblock-summary-get"><button aria-expanded="false" class="opblock-summary-control"><span class="opblock-summary-method">GET</span><div class="opblock-summary-path-description-wrapper"><span class="opblock-summary-path" data-path="/api/v1/expense-categories/{id}"><a class="nostyle"><span>/api<wbr>/v1<wbr>/expense-categories<wbr>/{id}</span></a></span><div class="opblock-summary-description">Get a specific expense category</div></div></button><div class="view-line-link copy-to-clipboard" title="Copy to clipboard"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 15 16" width="15" height="16" aria-hidden="true" focusable="false"><g transform="translate(2, -1)"><path fill="#ffffff" fill-rule="evenodd" d="M2 13h4v1H2v-1zm5-6H2v1h5V7zm2 3V8l-3 3 3 3v-2h5v-2H9zM4.5 9H2v1h2.5V9zM2 12h2.5v-1H2v1zm9 1h1v2c-.02.28-.11.52-.3.7-.19.18-.42.28-.7.3H1c-.55 0-1-.45-1-1V4c0-.55.45-1 1-1h3c0-1.11.89-2 2-2 1.11 0 2 .89 2 2h3c.55 0 1 .45 1 1v5h-1V6H1v9h10v-2zM2 5h8c0-.55-.45-1-1-1H8c-.55 0-1-.45-1-1s-.45-1-1-1-1 .45-1 1-.45 1-1 1H3c-.55 0-1 .45-1 1z"></path></g></svg></div><button class="authorization__btn" aria-label="authorization button unlocked"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" class="unlocked" width="20" height="20" aria-hidden="true" focusable="false"><path d="M15.8 8H14V5.6C14 2.703 12.665 1 10 1 7.334 1 6 2.703 6 5.6V6h2v-.801C8 3.754 8.797 3 10 3c1.203 0 2 .754 2 2.199V8H4c-.553 0-1 .646-1 1.199V17c0 .549.428 1.139.951 1.307l1.197.387C5.672 18.861 6.55 19 7.1 19h5.8c.549 0 1.428-.139 1.951-.307l1.196-.387c.524-.167.953-.757.953-1.306V9.199C17 8.646 16.352 8 15.8 8z"></path></svg></button><button aria-label="get ​/api​/v1​/expense-categories​/{id}" class="opblock-control-arrow" aria-expanded="false" tabindex="-1"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" class="arrow" width="20" height="20" aria-hidden="true" focusable="false"><path d="M17.418 6.109c.272-.268.709-.268.979 0s.271.701 0 .969l-7.908 7.83c-.27.268-.707.268-.979 0l-7.908-7.83c-.27-.268-.27-.701 0-.969.271-.268.709-.268.979 0L10 13.25l7.418-7.141z"></path></svg></button></div><noscript></noscript></div></span><span><div class="opblock opblock-put" id="operations-Expense_Categories-put_app_expense_category_update"><div class="opblock-summary opblock-summary-put"><button aria-expanded="false" class="opblock-summary-control"><span class="opblock-summary-method">PUT</span><div class="opblock-summary-path-description-wrapper"><span class="opblock-summary-path" data-path="/api/v1/expense-categories/{id}"><a class="nostyle"><span>/api<wbr>/v1<wbr>/expense-categories<wbr>/{id}</span></a></span><div class="opblock-summary-description">Update an expense category (admin only)</div></div></button><div class="view-line-link copy-to-clipboard" title="Copy to clipboard"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 15 16" width="15" height="16" aria-hidden="true" focusable="false"><g transform="translate(2, -1)"><path fill="#ffffff" fill-rule="evenodd" d="M2 13h4v1H2v-1zm5-6H2v1h5V7zm2 3V8l-3 3 3 3v-2h5v-2H9zM4.5 9H2v1h2.5V9zM2 12h2.5v-1H2v1zm9 1h1v2c-.02.28-.11.52-.3.7-.19.18-.42.28-.7.3H1c-.55 0-1-.45-1-1V4c0-.55.45-1 1-1h3c0-1.11.89-2 2-2 1.11 0 2 .89 2 2h3c.55 0 1 .45 1 1v5h-1V6H1v9h10v-2zM2 5h8c0-.55-.45-1-1-1H8c-.55 0-1-.45-1-1s-.45-1-1-1-1 .45-1 1-.45 1-1 1H3c-.55 0-1 .45-1 1z"></path></g></svg></div><button class="authorization__btn" aria-label="authorization button unlocked"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" class="unlocked" width="20" height="20" aria-hidden="true" focusable="false"><path d="M15.8 8H14V5.6C14 2.703 12.665 1 10 1 7.334 1 6 2.703 6 5.6V6h2v-.801C8 3.754 8.797 3 10 3c1.203 0 2 .754 2 2.199V8H4c-.553 0-1 .646-1 1.199V17c0 .549.428 1.139.951 1.307l1.197.387C5.672 18.861 6.55 19 7.1 19h5.8c.549 0 1.428-.139 1.951-.307l1.196-.387c.524-.167.953-.757.953-1.306V9.199C17 8.646 16.352 8 15.8 8z"></path></svg></button><button aria-label="put ​/api​/v1​/expense-categories​/{id}" class="opblock-control-arrow" aria-expanded="false" tabindex="-1"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" class="arrow" width="20" height="20" aria-hidden="true" focusable="false"><path d="M17.418 6.109c.272-.268.709-.268.979 0s.271.701 0 .969l-7.908 7.83c-.27.268-.707.268-.979 0l-7.908-7.83c-.27-.268-.27-.701 0-.969.271-.268.709-.268.979 0L10 13.25l7.418-7.141z"></path></svg></button></div><noscript></noscript></div></span><span><div class="opblock opblock-delete" id="operations-Expense_Categories-delete_app_expense_category_delete"><div class="opblock-summary opblock-summary-delete"><button aria-expanded="false" class="opblock-summary-control"><span class="opblock-summary-method">DELETE</span><div class="opblock-summary-path-description-wrapper"><span class="opblock-summary-path" data-path="/api/v1/expense-categories/{id}"><a class="nostyle"><span>/api<wbr>/v1<wbr>/expense-categories<wbr>/{id}</span></a></span><div class="opblock-summary-description">Delete an expense category (admin only)</div></div></button><div class="view-line-link copy-to-clipboard" title="Copy to clipboard"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 15 16" width="15" height="16" aria-hidden="true" focusable="false"><g transform="translate(2, -1)"><path fill="#ffffff" fill-rule="evenodd" d="M2 13h4v1H2v-1zm5-6H2v1h5V7zm2 3V8l-3 3 3 3v-2h5v-2H9zM4.5 9H2v1h2.5V9zM2 12h2.5v-1H2v1zm9 1h1v2c-.02.28-.11.52-.3.7-.19.18-.42.28-.7.3H1c-.55 0-1-.45-1-1V4c0-.55.45-1 1-1h3c0-1.11.89-2 2-2 1.11 0 2 .89 2 2h3c.55 0 1 .45 1 1v5h-1V6H1v9h10v-2zM2 5h8c0-.55-.45-1-1-1H8c-.55 0-1-.45-1-1s-.45-1-1-1-1 .45-1 1-.45 1-1 1H3c-.55 0-1 .45-1 1z"></path></g></svg></div><button class="authorization__btn" aria-label="authorization button unlocked"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" class="unlocked" width="20" height="20" aria-hidden="true" focusable="false"><path d="M15.8 8H14V5.6C14 2.703 12.665 1 10 1 7.334 1 6 2.703 6 5.6V6h2v-.801C8 3.754 8.797 3 10 3c1.203 0 2 .754 2 2.199V8H4c-.553 0-1 .646-1 1.199V17c0 .549.428 1.139.951 1.307l1.197.387C5.672 18.861 6.55 19 7.1 19h5.8c.549 0 1.428-.139 1.951-.307l1.196-.387c.524-.167.953-.757.953-1.306V9.199C17 8.646 16.352 8 15.8 8z"></path></svg></button><button aria-label="delete ​/api​/v1​/expense-categories​/{id}" class="opblock-control-arrow" aria-expanded="false" tabindex="-1"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" class="arrow" width="20" height="20" aria-hidden="true" focusable="false"><path d="M17.418 6.109c.272-.268.709-.268.979 0s.271.701 0 .969l-7.908 7.83c-.27.268-.707.268-.979 0l-7.908-7.83c-.27-.268-.27-.701 0-.969.271-.268.709-.268.979 0L10 13.25l7.418-7.141z"></path></svg></button></div><noscript></noscript></div></span><span><div class="opblock opblock-get" id="operations-Expense_Categories-get_app_expense_category_subcategories"><div class="opblock-summary opblock-summary-get"><button aria-expanded="false" class="opblock-summary-control"><span class="opblock-summary-method">GET</span><div class="opblock-summary-path-description-wrapper"><span class="opblock-summary-path" data-path="/api/v1/expense-categories/{id}/subcategories"><a class="nostyle"><span>/api<wbr>/v1<wbr>/expense-categories<wbr>/{id}<wbr>/subcategories</span></a></span><div class="opblock-summary-description">Get subcategories of a parent category</div></div></button><div class="view-line-link copy-to-clipboard" title="Copy to clipboard"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 15 16" width="15" height="16" aria-hidden="true" focusable="false"><g transform="translate(2, -1)"><path fill="#ffffff" fill-rule="evenodd" d="M2 13h4v1H2v-1zm5-6H2v1h5V7zm2 3V8l-3 3 3 3v-2h5v-2H9zM4.5 9H2v1h2.5V9zM2 12h2.5v-1H2v1zm9 1h1v2c-.02.28-.11.52-.3.7-.19.18-.42.28-.7.3H1c-.55 0-1-.45-1-1V4c0-.55.45-1 1-1h3c0-1.11.89-2 2-2 1.11 0 2 .89 2 2h3c.55 0 1 .45 1 1v5h-1V6H1v9h10v-2zM2 5h8c0-.55-.45-1-1-1H8c-.55 0-1-.45-1-1s-.45-1-1-1-1 .45-1 1-.45 1-1 1H3c-.55 0-1 .45-1 1z"></path></g></svg></div><button class="authorization__btn" aria-label="authorization button unlocked"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" class="unlocked" width="20" height="20" aria-hidden="true" focusable="false"><path d="M15.8 8H14V5.6C14 2.703 12.665 1 10 1 7.334 1 6 2.703 6 5.6V6h2v-.801C8 3.754 8.797 3 10 3c1.203 0 2 .754 2 2.199V8H4c-.553 0-1 .646-1 1.199V17c0 .549.428 1.139.951 1.307l1.197.387C5.672 18.861 6.55 19 7.1 19h5.8c.549 0 1.428-.139 1.951-.307l1.196-.387c.524-.167.953-.757.953-1.306V9.199C17 8.646 16.352 8 15.8 8z"></path></svg></button><button aria-label="get ​/api​/v1​/expense-categories​/{id}​/subcategories" class="opblock-control-arrow" aria-expanded="false" tabindex="-1"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" class="arrow" width="20" height="20" aria-hidden="true" focusable="false"><path d="M17.418 6.109c.272-.268.709-.268.979 0s.271.701 0 .969l-7.908 7.83c-.27.268-.707.268-.979 0l-7.908-7.83c-.27-.268-.27-.701 0-.969.271-.268.709-.268.979 0L10 13.25l7.418-7.141z"></path></svg></button></div><noscript></noscript></div></span></div> </div></div></span></div></section></div><div class="wrapper"><section class="block col-12 block-desktop col-12-desktop"><section class="models is-open"><h4><button aria-expanded="true" class="models-control"><span>Schemas</span><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" width="20" height="20" aria-hidden="true" focusable="false"><path d="M 17.418 14.908 C 17.69 15.176 18.127 15.176 18.397 14.908 C 18.667 14.64 18.668 14.207 18.397 13.939 L 10.489 6.109 C 10.219 5.841 9.782 5.841 9.51 6.109 L 1.602 13.939 C 1.332 14.207 1.332 14.64 1.602 14.908 C 1.873 15.176 2.311 15.176 2.581 14.908 L 10 7.767 L 17.418 14.908 Z"></path></svg></button></h4><div class="no-margin"> <div id="model-Consultant" class="model-container" data-name="Consultant"><span class="models-jump-to-path"></span><span class="model-box"><button aria-expanded="false" class="model-box-control"><span class="pointer"><span class="model-box"><span class="model model-title">Consultant</span></span></span><span class="model-toggle collapsed"></span><span> </span></button></span></div><div id="model-RegistrationUserInfo" class="model-container" data-name="RegistrationUserInfo"><span class="models-jump-to-path"></span><span class="model-box"><button aria-expanded="false" class="model-box-control"><span class="pointer"><span class="model-box"><span class="model model-title">RegistrationUserInfo</span></span></span><span class="model-toggle collapsed"></span><span> </span></button></span></div><div id="model-RegistrationResponse" class="model-container" data-name="RegistrationResponse"><span class="models-jump-to-path"></span><span class="model-box"><button aria-expanded="false" class="model-box-control"><span class="pointer"><span class="model-box"><span class="model model-title">RegistrationResponse</span></span></span><span class="model-toggle collapsed"></span><span> </span></button></span></div><div id="model-RegistrationRequest" class="model-container" data-name="RegistrationRequest"><span class="models-jump-to-path"></span><span class="model-box"><button aria-expanded="false" class="model-box-control"><span class="pointer"><span class="model-box"><span class="model model-title">RegistrationRequest</span></span></span><span class="model-toggle collapsed"></span><span> </span></button></span></div><div id="model-UserInfo" class="model-container" data-name="UserInfo"><span class="models-jump-to-path"></span><span class="model-box"><button aria-expanded="false" class="model-box-control"><span class="pointer"><span class="model-box"><span class="model model-title">UserInfo</span></span></span><span class="model-toggle collapsed"></span><span> </span></button></span></div><div id="model-LoginResponse" class="model-container" data-name="LoginResponse"><span class="models-jump-to-path"></span><span class="model-box"><button aria-expanded="false" class="model-box-control"><span class="pointer"><span class="model-box"><span class="model model-title">LoginResponse</span></span></span><span class="model-toggle collapsed"></span><span> </span></button></span></div><div id="model-LoginRequest" class="model-container" data-name="LoginRequest"><span class="models-jump-to-path"></span><span class="model-box"><button aria-expanded="false" class="model-box-control"><span class="pointer"><span class="model-box"><span class="model model-title">LoginRequest</span></span></span><span class="model-toggle collapsed"></span><span> </span></button></span></div> </div></section></section></div></div></div><div class="wrapper"><section class=""></section></div></section></div>
    
    <div class="custom-swagger-footer">
        <div style="max-width: 1200px; margin: 0 auto; padding: 0 20px;">
            <p>© 2025 FSLI Group. All rights reserved.</p>
            <div style="margin-top: 8px;">
                <a href="https://staging.api.erp.fsli-group.com/">Home</a>
                <a href="https://staging.api.erp.fsli-group.com/api/v1/doc#">Terms of Service</a>
                <a href="https://staging.api.erp.fsli-group.com/api/v1/doc#">Privacy Policy</a>
            </div>
        </div>
    </div>

                <script src="./Consultant Management System API_files/swagger-ui-bundle.js"></script>
        <script src="./Consultant Management System API_files/swagger-ui-standalone-preset.js"></script>
    
    <script>
        function authorizeModal() {
            // Find and click the authorize button
            const authorizeBtn = document.querySelector('.swagger-ui .auth-wrapper .authorize');
            if (authorizeBtn) {
                authorizeBtn.click();
            }
        }

        // Add custom initialization after Swagger UI loads
        window.addEventListener('load', function() {
            // Add some delay to ensure Swagger UI is fully loaded
            setTimeout(function() {
                // Expand all operations by default
                const expandButtons = document.querySelectorAll('.opblock-tag');
                expandButtons.forEach(function(button) {
                    if (button.getAttribute('aria-expanded') === 'false') {
                        button.click();
                    }
                });

                // Hide the redundant info section under the header
                const infoSection = document.querySelector('.swagger-ui .info');
                if (infoSection) {
                    infoSection.style.display = 'none';
                }

                // Fine-tune the body padding based on actual header height
                const header = document.querySelector('.custom-swagger-header');

                if (header) {
                    // Get the header height
                    const headerHeight = header.offsetHeight;

                    // Adjust body padding to match exact header height
                    document.body.style.paddingTop = headerHeight + 'px';
                }
            }, 1000);
        });
    </script>

    <script src="./Consultant Management System API_files/init-swagger-ui.js"></script>

            <script type="text/javascript">
            window.onload = () => {
                loadSwaggerUI([]);
            };
        </script>
    

<div class="glasp-extension-toaster" style="display: block; width: 320px; margin: unset; padding: unset; border: unset; border-radius: unset; outline: unset; background-color: unset; box-shadow: unset; position: fixed; top: 40px; right: 24px; z-index: 9999;"><template shadowrootmode="open"><div class="glasp-extension" style="font-family: Inter;"></div></template></div><div class="glasp-extension" style="display: block; width: unset; margin: unset; padding: unset; border: unset; border-radius: unset; outline: unset; background-color: unset; box-shadow: unset; position: fixed; bottom: 16px; right: 16px; z-index: 9999;"><template shadowrootmode="open"><div class="glasp-extension" style="font-family: Inter;"></div></template></div></body></html>