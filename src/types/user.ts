// User types based on API structure and JWT token

export interface Consultant {
  id: number
  firstName: string
  lastName: string
  email: string
  phoneNumber?: string
  address?: string
  city?: string
  postalCode?: string
  country?: string
  dateOfBirth?: string
  nationality?: string
  emergencyContactName?: string
  emergencyContactPhone?: string
  bankAccountNumber?: string
  socialSecurityNumber?: string
  taxNumber?: string
  contractStartDate?: string
  contractEndDate?: string
  hourlyRate?: number
  isActive: boolean
  createdAt: string
  updatedAt: string
}

export interface JwtPayload {
  sub: string // user ID
  email: string
  roles: string[] // e.g., ['ROLE_ADMIN', 'ROLE_USER']
  iat: number
  exp: number
}

export interface User {
  id: number
  consultant: Consultant
  roles: string[]
  email: string
  isAdmin: boolean
  isConsultant: boolean
  fullName: string
  initials: string
  avatar?: string
}

export interface AuthState {
  user: User | null
  token: string | null
  isAuthenticated: boolean
  isLoading: boolean
  error: string | null
}

export interface UserPermissions {
  canViewUsers: boolean
  canManageUsers: boolean
  canViewAllTimeEntries: boolean
  canValidateTimeEntries: boolean
  canViewAllExpenses: boolean
  canValidateExpenses: boolean
  canViewAllLeaves: boolean
  canApproveLeaves: boolean
  canManageClients: boolean
  canViewAdminDashboard: boolean
  canManageCompany: boolean
  canViewTrips: boolean
  canManageTrips: boolean
}

// Role constants
export const ROLES = {
  ADMIN: 'ROLE_ADMIN',
  USER: 'ROLE_USER',
  CONSULTANT: 'ROLE_CONSULTANT',
} as const

export type Role = typeof ROLES[keyof typeof ROLES]

// Helper functions
export const hasRole = (user: User | null, role: Role): boolean => {
  return user?.roles.includes(role) ?? false
}

export const isAdmin = (user: User | null): boolean => {
  return hasRole(user, ROLES.ADMIN)
}

export const isConsultant = (user: User | null): boolean => {
  return hasRole(user, ROLES.USER) || hasRole(user, ROLES.CONSULTANT)
}

export const getUserPermissions = (user: User | null): UserPermissions => {
  const admin = isAdmin(user)
  
  return {
    canViewUsers: admin,
    canManageUsers: admin,
    canViewAllTimeEntries: admin,
    canValidateTimeEntries: admin,
    canViewAllExpenses: admin,
    canValidateExpenses: admin,
    canViewAllLeaves: admin,
    canApproveLeaves: admin,
    canManageClients: admin,
    canViewAdminDashboard: admin,
    canManageCompany: admin,
    canViewTrips: admin || isConsultant(user),
    canManageTrips: admin,
  }
}

export const getInitials = (firstName: string, lastName: string): string => {
  return `${firstName.charAt(0)}${lastName.charAt(0)}`.toUpperCase()
}

export const getFullName = (firstName: string, lastName: string): string => {
  return `${firstName} ${lastName}`
}
