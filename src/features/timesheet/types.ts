// API-compliant interfaces matching the backend structure

export interface WorkTimePeriod {
  id: number
  startDate: string
  endDate: string
  totalHours: number
  remoteWorkPercentage: number
  notes?: string
  isValidated: boolean
  validationStatus: string
  periodIdentifier: string
  durationInDays: number
  activities: Activity[]
  consultant: Consultant
}

export interface Activity {
  id: number
  activityName: string
  hours: number
  description?: string
  isBillable: boolean
  percentageOfTotal: number
  client?: Client
}

export interface Client {
  id: number
  name: string
  code: string
}

export interface Consultant {
  id: number
  firstName: string
  lastName: string
  email?: string
}

export interface ConsultantInfo {
  id: string
  name: string
  email: string
}

// Form data interfaces
export interface WorkTimePeriodFormData {
  startDate: Date
  endDate: Date
  remoteWorkPercentage: number
  notes?: string
  activities: ActivityFormData[]
}

export interface ActivityFormData {
  activityName: string
  hours: string
  description?: string
  isBillable: boolean
  clientId?: number
}

// API request/response interfaces
export interface CreateWorkTimePeriodRequest {
  consultantId: number
  startDate: string
  endDate: string
  totalHours: number
  remoteWorkPercentage: number
  notes?: string
  activities: CreateActivityRequest[]
}

export interface CreateActivityRequest {
  activityName: string
  hours: number
  description?: string
  isBillable: boolean
  clientId?: number
}

export interface UpdateWorkTimePeriodRequest extends Partial<CreateWorkTimePeriodRequest> {
  id: number
}

export interface WorkTimeStatistics {
  totalHours: number
  remoteHours: number
  onsiteHours: number
  totalPeriods: number
  remotePercentage: number
  onsitePercentage: number
  averageHoursPerPeriod: number
}

export interface ApiResponse<T> {
  data: T
  message?: string
  success: boolean
}

export interface ApiError {
  error: string
  success: boolean
  message?: string
}

// Legacy interfaces for backward compatibility during migration
export interface TimeEntry {
  id: string
  date: string
  hours: number
  location: 'office' | 'home'
  client: string
  project?: string
  description?: string
  consultantId: string
  createdAt?: string
  updatedAt?: string
}

export interface TimeEntryFormData {
  dateRange?: {
    from: Date
    to?: Date
  }
  hours: string
  location: 'office' | 'home'
  client: string
  project?: string
  description?: string
}
