import { Button } from '@/components/ui/button'
import { IconPlus, IconFileImport, IconFileExport } from '@tabler/icons-react'
import { useTimesheet } from '../context/timesheet-context'

export function WorkTimePeriodPrimaryButtons() {
  const { setOpen } = useTimesheet()

  return (
    <div className='flex flex-wrap items-center gap-2'>
      <Button onClick={() => setOpen('create-period')}>
        <IconPlus className='mr-2 h-4 w-4' />
        Add Work Period
      </Button>

      <Button variant='outline' onClick={() => setOpen('import')}>
        <IconFileImport className='mr-2 h-4 w-4' />
        Import
      </Button>

      <Button variant='outline'>
        <IconFileExport className='mr-2 h-4 w-4' />
        Export
      </Button>
    </div>
  )
}
