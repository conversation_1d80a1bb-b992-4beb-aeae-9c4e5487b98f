import { ColumnDef } from '@tanstack/react-table'
import { Badge } from '@/components/ui/badge'
import { But<PERSON> } from '@/components/ui/button'
import { Checkbox } from '@/components/ui/checkbox'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { IconDots, IconEdit, IconTrash, IconEye, IconCheck, IconClock } from '@tabler/icons-react'
import { WorkTimePeriod } from '../data/schema'
import { useTimesheet } from '../context/timesheet-context'

export const workTimePeriodColumns: ColumnDef<WorkTimePeriod>[] = [
  {
    id: 'select',
    header: ({ table }) => (
      <Checkbox
        checked={
          table.getIsAllPageRowsSelected() ||
          (table.getIsSomePageRowsSelected() && 'indeterminate')
        }
        onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
        aria-label='Select all'
        className='translate-y-[2px]'
      />
    ),
    cell: ({ row }) => (
      <Checkbox
        checked={row.getIsSelected()}
        onCheckedChange={(value) => row.toggleSelected(!!value)}
        aria-label='Select row'
        className='translate-y-[2px]'
      />
    ),
    enableSorting: false,
    enableHiding: false,
  },
  {
    accessorKey: 'periodIdentifier',
    header: 'Period',
    cell: ({ row }) => {
      const period = row.original
      const startDate = new Date(period.startDate)
      const endDate = new Date(period.endDate)
      
      const formatDate = (date: Date) => date.toLocaleDateString('en-US', {
        month: 'short',
        day: 'numeric',
        year: 'numeric',
      })
      
      return (
        <div className='font-medium'>
          {startDate.getTime() === endDate.getTime() 
            ? formatDate(startDate)
            : `${formatDate(startDate)} - ${formatDate(endDate)}`
          }
        </div>
      )
    },
  },
  {
    accessorKey: 'totalHours',
    header: 'Total Hours',
    cell: ({ row }) => {
      const hours = row.getValue('totalHours') as number
      return <div className='font-medium'>{hours}h</div>
    },
  },
  {
    accessorKey: 'remoteWorkPercentage',
    header: 'Remote Work',
    cell: ({ row }) => {
      const percentage = row.getValue('remoteWorkPercentage') as number
      const isRemote = percentage > 50
      return (
        <div className="flex items-center gap-2">
          <Badge variant={isRemote ? 'secondary' : 'default'}>
            {isRemote ? '🏠' : '🏢'} {percentage}%
          </Badge>
        </div>
      )
    },
  },
  {
    accessorKey: 'activities',
    header: 'Activities',
    cell: ({ row }) => {
      const activities = row.getValue('activities') as WorkTimePeriod['activities']
      const activityCount = activities.length
      const mainActivity = activities[0]?.activityName || 'No activities'
      
      return (
        <div className='max-w-[200px]'>
          <div className='font-medium truncate'>{mainActivity}</div>
          {activityCount > 1 && (
            <div className='text-xs text-muted-foreground'>
              +{activityCount - 1} more
            </div>
          )}
        </div>
      )
    },
  },
  {
    accessorKey: 'isValidated',
    header: 'Status',
    cell: ({ row }) => {
      const isValidated = row.getValue('isValidated') as boolean
      const validationStatus = row.original.validationStatus
      
      return (
        <Badge variant={isValidated ? 'default' : 'secondary'}>
          {isValidated ? (
            <>
              <IconCheck className='mr-1 h-3 w-3' />
              Validated
            </>
          ) : (
            <>
              <IconClock className='mr-1 h-3 w-3' />
              {validationStatus || 'Pending'}
            </>
          )}
        </Badge>
      )
    },
  },
  {
    accessorKey: 'consultant',
    header: 'Consultant',
    cell: ({ row }) => {
      const consultant = row.original.consultant
      return (
        <div className='max-w-[150px] truncate font-medium'>
          {consultant.firstName} {consultant.lastName}
        </div>
      )
    },
  },
  {
    id: 'actions',
    enableHiding: false,
    cell: ({ row }) => {
      const period = row.original
      // eslint-disable-next-line react-hooks/rules-of-hooks
      const { setOpen, setCurrentPeriod } = useTimesheet()

      return (
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant='ghost' className='h-8 w-8 p-0'>
              <span className='sr-only'>Open menu</span>
              <IconDots className='h-4 w-4' />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align='end'>
            <DropdownMenuLabel>Actions</DropdownMenuLabel>
            <DropdownMenuSeparator />
            <DropdownMenuItem
              onClick={() => {
                setCurrentPeriod(period)
                setOpen('view-period')
              }}
            >
              <IconEye className='mr-2 h-4 w-4' />
              View Details
            </DropdownMenuItem>
            <DropdownMenuItem
              onClick={() => {
                setCurrentPeriod(period)
                setOpen('update-period')
              }}
            >
              <IconEdit className='mr-2 h-4 w-4' />
              Edit
            </DropdownMenuItem>
            {!period.isValidated && (
              <DropdownMenuItem
                onClick={() => {
                  setCurrentPeriod(period)
                  setOpen('validate-period')
                }}
              >
                <IconCheck className='mr-2 h-4 w-4' />
                Validate
              </DropdownMenuItem>
            )}
            <DropdownMenuItem
              onClick={() => {
                setCurrentPeriod(period)
                setOpen('delete-period')
              }}
              className='text-destructive'
            >
              <IconTrash className='mr-2 h-4 w-4' />
              Delete
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      )
    },
  },
]
