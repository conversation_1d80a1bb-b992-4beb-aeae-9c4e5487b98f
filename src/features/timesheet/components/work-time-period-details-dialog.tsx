import {
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Separator } from '@/components/ui/separator'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { IconCalendar, IconClock, IconUser, IconBuilding, IconCheck, IconEdit } from '@tabler/icons-react'
import { WorkTimePeriod } from '../data/schema'
import { useTimesheet } from '../context/timesheet-context'

interface Props {
  open: boolean
  onOpenChange: (open: boolean) => void
  period: WorkTimePeriod
}

export function WorkTimePeriodDetailsDialog({ open, onOpenChange, period }: Props) {
  const { setOpen, setCurrentPeriod } = useTimesheet()

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    })
  }

  const handleEdit = () => {
    setCurrentPeriod(period)
    setOpen('update-period')
    onOpenChange(false)
  }

  const totalBillableHours = period.activities
    .filter(activity => activity.isBillable)
    .reduce((sum, activity) => sum + activity.hours, 0)

  const totalNonBillableHours = period.activities
    .filter(activity => !activity.isBillable)
    .reduce((sum, activity) => sum + activity.hours, 0)

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <div className="flex items-center justify-between">
            <div>
              <DialogTitle className="text-xl">Work Time Period Details</DialogTitle>
              <DialogDescription>
                {formatDate(period.startDate)}
                {period.startDate !== period.endDate && ` - ${formatDate(period.endDate)}`}
              </DialogDescription>
            </div>
            <div className="flex items-center gap-2">
              <Badge variant={period.isValidated ? 'default' : 'secondary'}>
                {period.isValidated ? (
                  <>
                    <IconCheck className='mr-1 h-3 w-3' />
                    Validated
                  </>
                ) : (
                  <>
                    <IconClock className='mr-1 h-3 w-3' />
                    {period.validationStatus || 'Pending'}
                  </>
                )}
              </Badge>
              {!period.isValidated && (
                <Button size="sm" onClick={handleEdit}>
                  <IconEdit className="h-4 w-4 mr-2" />
                  Edit
                </Button>
              )}
            </div>
          </div>
        </DialogHeader>

        <div className="space-y-6">
          {/* Period Overview */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Hours</CardTitle>
                <IconClock className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{period.totalHours}h</div>
                <p className="text-xs text-muted-foreground">
                  {period.durationInDays} day{period.durationInDays !== 1 ? 's' : ''}
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Remote Work</CardTitle>
                <IconUser className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{period.remoteWorkPercentage}%</div>
                <p className="text-xs text-muted-foreground">
                  {((period.totalHours * period.remoteWorkPercentage) / 100).toFixed(1)}h remote
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Activities</CardTitle>
                <IconCalendar className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{period.activities.length}</div>
                <p className="text-xs text-muted-foreground">
                  {totalBillableHours}h billable, {totalNonBillableHours}h non-billable
                </p>
              </CardContent>
            </Card>
          </div>

          {/* Consultant Info */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg flex items-center gap-2">
                <IconUser className="h-5 w-5" />
                Consultant
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex items-center gap-4">
                <div>
                  <p className="font-medium">
                    {period.consultant.firstName} {period.consultant.lastName}
                  </p>
                  {period.consultant.email && (
                    <p className="text-sm text-muted-foreground">{period.consultant.email}</p>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Notes */}
          {period.notes && (
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Notes</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-sm leading-relaxed">{period.notes}</p>
              </CardContent>
            </Card>
          )}

          <Separator />

          {/* Activities */}
          <div>
            <h3 className="text-lg font-semibold mb-4">Activities</h3>
            <div className="space-y-4">
              {period.activities.map((activity, index) => (
                <Card key={activity.id || index}>
                  <CardContent className="pt-6">
                    <div className="flex items-start justify-between gap-4">
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center gap-2 mb-2">
                          <h4 className="font-medium truncate">{activity.activityName}</h4>
                          <Badge variant={activity.isBillable ? 'default' : 'secondary'}>
                            {activity.isBillable ? 'Billable' : 'Non-billable'}
                          </Badge>
                        </div>
                        
                        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3 text-sm">
                          <div className="flex items-center space-x-2">
                            <IconClock className="h-4 w-4 text-muted-foreground flex-shrink-0" />
                            <div>
                              <span className="font-medium">Hours:</span>
                              <span className="ml-1">{activity.hours}h</span>
                            </div>
                          </div>
                          
                          <div className="flex items-center space-x-2">
                            <IconCalendar className="h-4 w-4 text-muted-foreground flex-shrink-0" />
                            <div>
                              <span className="font-medium">Percentage:</span>
                              <span className="ml-1">{activity.percentageOfTotal.toFixed(1)}%</span>
                            </div>
                          </div>

                          {activity.client && (
                            <div className="flex items-center space-x-2">
                              <IconBuilding className="h-4 w-4 text-muted-foreground flex-shrink-0" />
                              <div>
                                <span className="font-medium">Client:</span>
                                <span className="ml-1">{activity.client.name} ({activity.client.code})</span>
                              </div>
                            </div>
                          )}
                        </div>

                        {activity.description && (
                          <div className="mt-3 pt-3 border-t border-muted">
                            <p className="text-sm text-muted-foreground leading-relaxed">
                              {activity.description}
                            </p>
                          </div>
                        )}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}
