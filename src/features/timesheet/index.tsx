import { Header } from '@/components/layout/header'
import { Main } from '@/components/layout/main'
import { ProfileDropdown } from '@/components/profile-dropdown'
import { Search } from '@/components/search'
import { ThemeSwitch } from '@/components/theme-switch'
import { LanguageSwitcher } from '@/components/language-switcher'
import { useTranslations, useErrorTranslations } from '@/context/i18n-context'
import { workTimePeriodColumns } from './components/work-time-period-columns'
import { DataTable } from './components/data-table'
import { WorkTimePeriodDialogs } from './components/work-time-period-dialogs'
import { WorkTimePeriodPrimaryButtons } from './components/work-time-period-primary-buttons'
import TimesheetProvider from './context/timesheet-context'
import { useConsultantInfo, useConsultantWorkTimePeriods } from './hooks/use-timesheet'
import { Skeleton } from '@/components/ui/skeleton'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { IconAlertCircle } from '@tabler/icons-react'

function TimesheetContent() {
  const { t } = useTranslations('timesheet')
  const { t: errorT } = useErrorTranslations()
  const { data: consultantInfo, isLoading: isLoadingConsultant, error: consultantError } = useConsultantInfo()
  const {
    data: workTimePeriods,
    isLoading: isLoadingPeriods,
    error: periodsError
  } = useConsultantWorkTimePeriods(consultantInfo ? parseInt(consultantInfo.id) : 0)

  const isLoading = isLoadingConsultant || isLoadingPeriods
  const error = consultantError || periodsError

  if (consultantError) {
    return (
      <Main>
        <Alert variant="destructive">
          <IconAlertCircle className="h-4 w-4" />
          <AlertDescription>
            {errorT('authentication.login_required')}
          </AlertDescription>
        </Alert>
      </Main>
    )
  }

  return (
    <Main>
      <div className='mb-2 flex flex-wrap items-center justify-between gap-x-4 space-y-2'>
        <div>
          <h2 className='text-2xl font-bold tracking-tight'>{t('title')}</h2>
          <p className='text-muted-foreground'>
            {t('subtitle')}
          </p>
        </div>
        <WorkTimePeriodPrimaryButtons />
      </div>

      <div className='-mx-4 flex-1 overflow-auto px-4 py-1 lg:flex-row lg:space-x-12 lg:space-y-0'>
        {isLoading ? (
          <div className="space-y-4">
            <Skeleton className="h-10 w-full" />
            <Skeleton className="h-10 w-full" />
            <Skeleton className="h-10 w-full" />
            <Skeleton className="h-10 w-full" />
          </div>
        ) : error ? (
          <Alert variant="destructive">
            <IconAlertCircle className="h-4 w-4" />
            <AlertDescription>
              {periodsError ? t('messages.loading_entries') : errorT('general.something_went_wrong')}
            </AlertDescription>
          </Alert>
        ) : (
          <DataTable data={workTimePeriods || []} columns={workTimePeriodColumns} />
        )}
      </div>
    </Main>
  )
}

export default function Timesheet() {
  return (
    <TimesheetProvider>
      <Header fixed>
        <Search />
        <div className='ml-auto flex items-center space-x-4'>
          <LanguageSwitcher />
          <ThemeSwitch />
          <ProfileDropdown />
        </div>
      </Header>

      <TimesheetContent />
      <WorkTimePeriodDialogs />
    </TimesheetProvider>
  )
}
