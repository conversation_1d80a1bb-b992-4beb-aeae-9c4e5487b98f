import { z } from 'zod'

// API-compliant schemas matching the backend structure

export const clientSchema = z.object({
  id: z.number(),
  name: z.string(),
  code: z.string(),
})

export const consultantSchema = z.object({
  id: z.number(),
  firstName: z.string(),
  lastName: z.string(),
  email: z.string().optional(),
})

// Legacy project schema for backward compatibility
export const projectSchema = z.object({
  id: z.string(),
  name: z.string(),
  clientId: z.string(),
  code: z.string(),
})

export const activitySchema = z.object({
  id: z.number(),
  activityName: z.string(),
  hours: z.number().positive(),
  description: z.string().optional(),
  isBillable: z.boolean(),
  percentageOfTotal: z.number().min(0).max(100),
  client: clientSchema.optional(),
})

export const workTimePeriodSchema = z.object({
  id: z.number(),
  startDate: z.string(),
  endDate: z.string(),
  totalHours: z.number().positive(),
  remoteWorkPercentage: z.number().min(0).max(100),
  notes: z.string().optional(),
  isValidated: z.boolean(),
  validationStatus: z.string(),
  periodIdentifier: z.string(),
  durationInDays: z.number().positive(),
  activities: z.array(activitySchema),
  consultant: consultantSchema,
})

// Form schemas for creating/editing work time periods

export const activityFormSchema = z.object({
  activityName: z.string().min(1, 'Activity name is required'),
  hours: z.string()
    .min(1, 'Hours is required')
    .refine((val) => {
      const num = parseFloat(val)
      return !isNaN(num) && num > 0
    }, 'Hours must be a positive number'),
  description: z.string().optional(),
  isBillable: z.boolean(),
  clientId: z.number().optional(),
  location: z.enum(['remote', 'onsite'], {
    required_error: 'Please select work location',
  }),
})

export const workTimePeriodFormSchema = z.object({
  startDate: z.date({ required_error: 'Please select a start date' }),
  endDate: z.date({ required_error: 'Please select an end date' }),
  notes: z.string().optional(),
  activities: z.array(activityFormSchema).min(1, 'At least one activity is required'),
}).refine((data) => {
  return data.endDate >= data.startDate
}, {
  message: 'End date must be after or equal to start date',
  path: ['endDate'],
})

// API request schemas

export const createActivityRequestSchema = z.object({
  activityName: z.string().min(1),
  hours: z.number().positive(),
  description: z.string().optional(),
  isBillable: z.boolean(),
  clientId: z.number().optional(),
  location: z.enum(['remote', 'onsite']),
})

export const createWorkTimePeriodRequestSchema = z.object({
  consultantId: z.number(),
  startDate: z.string(),
  endDate: z.string(),
  totalHours: z.number().positive(),
  remoteWorkPercentage: z.number().min(0).max(100),
  notes: z.string().optional(),
  activities: z.array(createActivityRequestSchema).min(1),
})

export const updateWorkTimePeriodRequestSchema = createWorkTimePeriodRequestSchema.partial().extend({
  id: z.number(),
})

export const workTimeStatisticsSchema = z.object({
  totalHours: z.number(),
  remoteHours: z.number(),
  onsiteHours: z.number(),
  totalPeriods: z.number(),
  remotePercentage: z.number(),
  onsitePercentage: z.number(),
  averageHoursPerPeriod: z.number(),
})

// Legacy schemas for backward compatibility
export const timeEntrySchema = z.object({
  id: z.string(),
  date: z.string(),
  hours: z.number().positive(),
  location: z.enum(['office', 'home']),
  client: z.string().min(1),
  project: z.string().optional(),
  description: z.string().optional(),
  consultantId: z.string(),
  createdAt: z.string().optional(),
  updatedAt: z.string().optional(),
})

export const timeEntryFormSchema = z.object({
  dateRange: z.object({
    from: z.date({ required_error: 'Please select a date' }),
    to: z.date().optional(),
  }).refine((data) => {
    if (data.to && data.from) {
      return data.to >= data.from
    }
    return true
  }, 'End date must be after start date'),
  hours: z.string()
    .min(1, 'Hours is required')
    .refine((val) => {
      const num = parseFloat(val)
      return !isNaN(num) && num > 0
    }, 'Hours must be a positive number'),
  location: z.enum(['office', 'home'], {
    required_error: 'Please select a work location',
  }),
  client: z.string().min(1, 'Please select a client'),
  project: z.string().optional(),
  description: z.string().optional(),
})

// Type exports
export type WorkTimePeriod = z.infer<typeof workTimePeriodSchema>
export type Activity = z.infer<typeof activitySchema>
export type Client = z.infer<typeof clientSchema>
export type Consultant = z.infer<typeof consultantSchema>
export type WorkTimePeriodForm = z.infer<typeof workTimePeriodFormSchema>
export type ActivityForm = z.infer<typeof activityFormSchema>
export type CreateWorkTimePeriodRequest = z.infer<typeof createWorkTimePeriodRequestSchema>
export type CreateActivityRequest = z.infer<typeof createActivityRequestSchema>
export type UpdateWorkTimePeriodRequest = z.infer<typeof updateWorkTimePeriodRequestSchema>
export type WorkTimeStatistics = z.infer<typeof workTimeStatisticsSchema>

// Legacy type exports
export type TimeEntry = z.infer<typeof timeEntrySchema>
export type TimeEntryForm = z.infer<typeof timeEntryFormSchema>
export type Project = z.infer<typeof projectSchema>
