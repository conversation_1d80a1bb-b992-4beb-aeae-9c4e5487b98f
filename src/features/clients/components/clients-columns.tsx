import { ColumnDef } from '@tanstack/react-table'
import { cn } from '@/lib/utils'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Checkbox } from '@/components/ui/checkbox'
import LongText from '@/components/long-text'
import { Client } from '../data/schema'
import { DataTableColumnHeader } from './data-table-column-header'
import { DataTableRowActions } from './data-table-row-actions'
import { useToggleClientStatus } from '../hooks/use-clients'
import { IconLoader2 } from '@tabler/icons-react'

// Status toggle cell component
function StatusToggleCell({ client }: { client: Client }) {
  const toggleStatus = useToggleClientStatus()

  const handleToggle = () => {
    toggleStatus.mutate({ id: client.id, isActive: !client.isActive })
  }

  return (
    <div className='flex items-center space-x-2'>
      <Badge
        variant={client.isActive ? 'default' : 'secondary'}
        className={cn(
          'capitalize',
          client.isActive
            ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300'
            : 'bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-300'
        )}
      >
        {client.isActive ? 'Active' : 'Inactive'}
      </Badge>
      <Button
        variant="ghost"
        size="sm"
        onClick={handleToggle}
        disabled={toggleStatus.isPending}
        className="h-6 px-2 text-xs"
      >
        {toggleStatus.isPending ? (
          <IconLoader2 className="h-3 w-3 animate-spin" />
        ) : (
          client.isActive ? 'Deactivate' : 'Activate'
        )}
      </Button>
    </div>
  )
}

export const columns: ColumnDef<Client>[] = [
  {
    id: 'select',
    header: ({ table }) => (
      <Checkbox
        checked={
          table.getIsAllPageRowsSelected() ||
          (table.getIsSomePageRowsSelected() && 'indeterminate')
        }
        onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
        aria-label='Select all'
        className='translate-y-[2px]'
      />
    ),
    meta: {
      className: cn(
        'sticky md:table-cell left-0 z-10 rounded-tl',
        'bg-background transition-colors duration-200 group-hover/row:bg-muted group-data-[state=selected]/row:bg-muted'
      ),
    },
    cell: ({ row }) => (
      <Checkbox
        checked={row.getIsSelected()}
        onCheckedChange={(value) => row.toggleSelected(!!value)}
        aria-label='Select row'
        className='translate-y-[2px]'
      />
    ),
    enableSorting: false,
    enableHiding: false,
  },
  {
    accessorKey: 'name',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title='Client Name' />
    ),
    cell: ({ row }) => (
      <LongText className='max-w-48 font-medium'>{row.getValue('name')}</LongText>
    ),
    meta: {
      className: cn(
        'drop-shadow-[0_1px_2px_rgb(0_0_0_/_0.1)] dark:drop-shadow-[0_1px_2px_rgb(255_255_255_/_0.1)] lg:drop-shadow-none',
        'bg-background transition-colors duration-200 group-hover/row:bg-muted group-data-[state=selected]/row:bg-muted',
        'sticky left-6 md:table-cell'
      ),
    },
    enableHiding: false,
  },
  {
    accessorKey: 'code',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title='Code' />
    ),
    cell: ({ row }) => (
      <Badge variant='secondary' className='font-mono text-xs'>
        {row.getValue('code')}
      </Badge>
    ),
    meta: { className: 'w-24' },
  },
  {
    accessorKey: 'description',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title='Description' />
    ),
    cell: ({ row }) => {
      const description = row.getValue('description') as string
      return description ? (
        <LongText className='max-w-64 text-muted-foreground'>
          {description}
        </LongText>
      ) : (
        <span className='text-muted-foreground italic'>No description</span>
      )
    },
    enableSorting: false,
  },
  {
    accessorKey: 'contactEmail',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title='Contact Email' />
    ),
    cell: ({ row }) => {
      const email = row.getValue('contactEmail') as string
      return email ? (
        <div className='w-fit text-nowrap'>{email}</div>
      ) : (
        <span className='text-muted-foreground italic'>No email</span>
      )
    },
    enableSorting: false,
  },
  {
    accessorKey: 'contactPhone',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title='Phone' />
    ),
    cell: ({ row }) => {
      const phone = row.getValue('contactPhone') as string
      return phone ? (
        <div className='w-fit text-nowrap'>{phone}</div>
      ) : (
        <span className='text-muted-foreground italic'>No phone</span>
      )
    },
    enableSorting: false,
  },
  {
    accessorKey: 'isActive',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title='Status' />
    ),
    cell: ({ row }) => <StatusToggleCell client={row.original} />,
    filterFn: (row, id, value) => {
      const isActive = row.getValue(id) as boolean
      return value.includes(isActive ? 'active' : 'inactive')
    },
    enableHiding: false,
    enableSorting: false,
    meta: { className: 'w-48' },
  },
  {
    id: 'actions',
    cell: DataTableRowActions,
  },
]
