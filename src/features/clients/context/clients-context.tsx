import React, { createContext, useContext, useState } from 'react'
import { Client } from '../data/schema'

type DialogType = 'create' | 'edit' | 'delete' | 'view' | null

interface ClientsContextType {
  open: DialogType
  setOpen: (open: DialogType) => void
  currentRow: Client | null
  setCurrentRow: (client: Client | null) => void
}

const ClientsContext = createContext<ClientsContextType | undefined>(undefined)

interface ClientsProviderProps {
  children: React.ReactNode
}

export default function ClientsProvider({ children }: ClientsProviderProps) {
  const [open, setOpen] = useState<DialogType>(null)
  const [currentRow, setCurrentRow] = useState<Client | null>(null)

  const value: ClientsContextType = {
    open,
    setOpen,
    currentRow,
    setCurrentRow,
  }

  return (
    <ClientsContext.Provider value={value}>
      {children}
    </ClientsContext.Provider>
  )
}

export function useClients() {
  const context = useContext(ClientsContext)
  if (context === undefined) {
    throw new Error('useClients must be used within a ClientsProvider')
  }
  return context
}
