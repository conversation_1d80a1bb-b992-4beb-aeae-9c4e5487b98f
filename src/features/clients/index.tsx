import { Header } from '@/components/layout/header'
import { Main } from '@/components/layout/main'
import { ProfileDropdown } from '@/components/profile-dropdown'
import { Search } from '@/components/search'
import { ThemeSwitch } from '@/components/theme-switch'
import { Skeleton } from '@/components/ui/skeleton'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { IconAlertCircle } from '@tabler/icons-react'
import { columns } from './components/clients-columns'
import { ClientsDialogs } from './components/clients-dialogs'
import { ClientsPrimaryButtons } from './components/clients-primary-buttons'
import { ClientsTable } from './components/clients-table'
import ClientsProvider from './context/clients-context'
import { useClients, usePrefetchClient } from './hooks/use-clients'

export default function Clients() {
  const { data: clients, isLoading, error, isFetching } = useClients()
  const prefetchClient = usePrefetchClient()

  return (
    <ClientsProvider>
      <Header fixed>
        <Search />
        <div className='ml-auto flex items-center space-x-4'>
          <ThemeSwitch />
          <ProfileDropdown />
        </div>
      </Header>

      <Main>
        <div className='mb-2 flex flex-wrap items-center justify-between space-y-2'>
          <div>
            <div className="flex items-center gap-2">
              <h2 className='text-2xl font-bold tracking-tight'>Client Management</h2>
              {isFetching && !isLoading && (
                <div className="h-4 w-4 animate-spin rounded-full border-2 border-primary border-t-transparent" />
              )}
            </div>
            <p className='text-muted-foreground'>
              Manage your clients and their information here.
            </p>
          </div>
          <ClientsPrimaryButtons />
        </div>

        <div className='-mx-4 flex-1 overflow-auto px-4 py-1 lg:flex-row lg:space-x-12 lg:space-y-0'>
          {isLoading ? (
            <div className='space-y-4'>
              <Skeleton className='h-10 w-full' />
              <Skeleton className='h-64 w-full' />
            </div>
          ) : error ? (
            <Alert variant='destructive'>
              <IconAlertCircle className='h-4 w-4' />
              <AlertDescription>
                Failed to load clients. Please try again later.
              </AlertDescription>
            </Alert>
          ) : (
            <ClientsTable
              data={clients || []}
              columns={columns}
              onRowHover={(client) => prefetchClient(client.id)}
            />
          )}
        </div>
      </Main>

      <ClientsDialogs />
    </ClientsProvider>
  )
}
