import { IconAlertCircle } from '@tabler/icons-react'
import { Header } from '@/components/layout/header'
import { Main } from '@/components/layout/main'
import { ProfileDropdown } from '@/components/profile-dropdown'
import { Search } from '@/components/search'
import { ThemeSwitch } from '@/components/theme-switch'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Skeleton } from '@/components/ui/skeleton'
import { columns } from './components/consultants-columns'
import { ConsultantsDialogs } from './components/consultants-dialogs'
import { ConsultantsPrimaryButtons } from './components/consultants-primary-buttons'
import { ConsultantsTable } from './components/consultants-table'
import ConsultantsProvider from './context/consultants-context'
import { useConsultants, usePrefetchConsultant } from './hooks/use-consultants'

export default function Consultants() {
  const { data: consultants, isLoading, error } = useConsultants()
  const prefetchConsultant = usePrefetchConsultant()

  return (
    <ConsultantsProvider>
      <Header fixed>
        <Search />
        <div className='ml-auto flex items-center space-x-4'>
          <ThemeSwitch />
          <ProfileDropdown />
        </div>
      </Header>

      <Main>
        <div className='mb-2 flex flex-wrap items-center justify-between space-y-2'>
          <div>
            <h2 className='text-2xl font-bold tracking-tight'>Consultant Management</h2>
            <p className='text-muted-foreground'>
              Manage consultants and their profiles here.
            </p>
          </div>
          <ConsultantsPrimaryButtons />
        </div>
        <div className='-mx-4 flex-1 overflow-auto px-4 py-1 lg:flex-row lg:space-x-12 lg:space-y-0'>
          {isLoading ? (
            <div className='space-y-4'>
              <Skeleton className='h-10 w-full' />
              <Skeleton className='h-64 w-full' />
            </div>
          ) : error ? (
            <Alert variant='destructive'>
              <IconAlertCircle className='h-4 w-4' />
              <AlertDescription>
                Failed to load consultants. Please try again later.
              </AlertDescription>
            </Alert>
          ) : (
            <ConsultantsTable
              data={consultants || []}
              columns={columns}
              onRowHover={(consultant) => prefetchConsultant(consultant.id)}
            />
          )}
        </div>
      </Main>

      <ConsultantsDialogs />
    </ConsultantsProvider>
  )
}
