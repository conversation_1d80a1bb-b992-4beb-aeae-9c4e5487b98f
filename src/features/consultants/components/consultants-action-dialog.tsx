'use client'

import { useEffect } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { Button } from '@/components/ui/button'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import { Switch } from '@/components/ui/switch'
import { PasswordInput } from '@/components/password-input'
import { Consultant, ConsultantForm, consultantFormSchema } from '../data/schema'
import { useCreateConsultant, useUpdateConsultant } from '../hooks/use-consultants'

interface Props {
  open: boolean
  onOpenChange: (open: boolean) => void
  currentRow?: Consultant
}

export function ConsultantsActionDialog({ currentRow, open, onOpenChange }: Props) {
  const isEdit = !!currentRow
  const createConsultant = useCreateConsultant()
  const updateConsultant = useUpdateConsultant()

  const form = useForm<ConsultantForm>({
    resolver: zodResolver(consultantFormSchema),
    defaultValues: {
      firstName: '',
      lastName: '',
      email: '',
      phone: '',
      password: '',
      confirmPassword: '',
      isAdmin: false,
    },
  })

  // Reset form when dialog opens/closes or currentRow changes
  useEffect(() => {
    if (open) {
      if (isEdit && currentRow) {
        form.reset({
          firstName: currentRow.firstName || '',
          lastName: currentRow.lastName || '',
          email: currentRow.email || '',
          phone: currentRow.phoneNumber || '',
          password: '',
          confirmPassword: '',
          isAdmin: false, // This would need to come from user roles, not consultant data
        })
      } else {
        form.reset({
          firstName: '',
          lastName: '',
          email: '',
          phone: '',
          password: '',
          confirmPassword: '',
          isAdmin: false,
        })
      }
    }
  }, [open, isEdit, currentRow, form])

  const onSubmit = async (values: ConsultantForm) => {
    try {
      if (isEdit && currentRow) {
        // For edit, we don't send password fields
        const { password, confirmPassword, ...updateData } = values
        await updateConsultant.mutateAsync({
          id: currentRow.id,
          data: { ...updateData, id: currentRow.id },
        })
      } else {
        // For create, we send all fields except confirmPassword
        const { confirmPassword, ...createData } = values
        // Only include phone if it's not empty
        const finalData = {
          ...createData,
          phone: createData.phone?.trim() || undefined,
        }
        await createConsultant.mutateAsync(finalData)
      }
      onOpenChange(false)
    } catch (error) {
      // Error handling is done in the mutation hooks
      console.error('Failed to save consultant:', error)
    }
  }

  const isLoading = createConsultant.isPending || updateConsultant.isPending

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className='sm:max-w-2xl max-h-[90vh] overflow-hidden flex flex-col'>
        <DialogHeader className='text-left'>
          <DialogTitle>{isEdit ? 'Edit Consultant' : 'Add New Consultant'}</DialogTitle>
          <DialogDescription>
            {isEdit ? 'Update the consultant information here. ' : 'Create a new consultant profile here. '}
            Click save when you&apos;re done.
          </DialogDescription>
        </DialogHeader>
        <div className='flex-1 overflow-y-auto py-1 pr-4 -mr-4'>
          <Form {...form}>
            <form
              id='consultant-form'
              onSubmit={form.handleSubmit(onSubmit)}
              className='space-y-4 p-0.5'
            >
              <div className='grid grid-cols-2 gap-4'>
                <FormField
                  control={form.control}
                  name='firstName'
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>First Name *</FormLabel>
                      <FormControl>
                        <Input placeholder='John' {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name='lastName'
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Last Name *</FormLabel>
                      <FormControl>
                        <Input placeholder='Doe' {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <FormField
                control={form.control}
                name='email'
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Email *</FormLabel>
                    <FormControl>
                      <Input placeholder='<EMAIL>' type='email' {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name='phone'
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Phone Number</FormLabel>
                    <FormControl>
                      <Input placeholder='+33123456789' {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {!isEdit && (
                <>
                  <FormField
                    control={form.control}
                    name='password'
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Password *</FormLabel>
                        <FormControl>
                          <PasswordInput placeholder='Enter password' {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name='confirmPassword'
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Confirm Password *</FormLabel>
                        <FormControl>
                          <PasswordInput placeholder='Confirm password' {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </>
              )}

              <FormField
                control={form.control}
                name='isAdmin'
                render={({ field }) => (
                  <FormItem className='flex flex-row items-center justify-between rounded-lg border p-4'>
                    <div className='space-y-0.5'>
                      <FormLabel className='text-base'>Admin Access</FormLabel>
                      <div className='text-sm text-muted-foreground'>
                        Grant administrative privileges to this consultant
                      </div>
                    </div>
                    <FormControl>
                      <Switch
                        checked={field.value}
                        onCheckedChange={field.onChange}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />
            </form>
          </Form>
        </div>
        <DialogFooter>
          <Button
            type='button'
            variant='outline'
            onClick={() => onOpenChange(false)}
            disabled={isLoading}
          >
            Cancel
          </Button>
          <Button
            type='submit'
            form='consultant-form'
            disabled={isLoading}
          >
            {isLoading ? 'Saving...' : isEdit ? 'Update Consultant' : 'Create Consultant'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
