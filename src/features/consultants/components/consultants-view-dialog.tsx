'use client'

import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Separator } from '@/components/ui/separator'
import { Consultant, getConsultantFullName } from '../data/schema'
import { formatDate } from '@/services/consultants'

interface Props {
  open: boolean
  onOpenChange: (open: boolean) => void
  currentRow: Consultant
}

export function ConsultantsViewDialog({ currentRow, open, onOpenChange }: Props) {
  const fullName = getConsultantFullName(currentRow)

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className='sm:max-w-2xl max-h-[90vh] overflow-hidden flex flex-col'>
        <DialogHeader className='text-left'>
          <DialogTitle>Consultant Details</DialogTitle>
          <DialogDescription>
            View detailed information about {fullName}.
          </DialogDescription>
        </DialogHeader>

        <div className='flex-1 overflow-y-auto py-1 pr-4 -mr-4'>
          <div className='space-y-6'>
            {/* Basic Information */}
            <div>
              <h3 className='text-lg font-semibold mb-3'>Consultant Information</h3>
              <div className='grid grid-cols-2 gap-4'>
                <div>
                  <label className='text-sm font-medium text-muted-foreground'>Full Name</label>
                  <p className='text-sm font-medium'>{fullName}</p>
                </div>
                <div>
                  <label className='text-sm font-medium text-muted-foreground'>Email</label>
                  <p className='text-sm'>{currentRow.email}</p>
                </div>
                <div>
                  <label className='text-sm font-medium text-muted-foreground'>Phone</label>
                  <p className='text-sm'>{currentRow.phoneNumber || (currentRow as any).phone || 'Not provided'}</p>
                </div>
                <div>
                  <label className='text-sm font-medium text-muted-foreground'>Role</label>
                  <div className='mt-1'>
                    <Badge variant={currentRow.isAdmin ? 'default' : 'secondary'}>
                      {currentRow.isAdmin ? 'Admin' : 'Consultant'}
                    </Badge>
                  </div>
                </div>
              </div>
            </div>

            <Separator />

            {/* System Information */}
            <div>
              <h3 className='text-lg font-semibold mb-3'>System Information</h3>
              <div className='grid grid-cols-2 gap-4'>
                <div>
                  <label className='text-sm font-medium text-muted-foreground'>Account ID</label>
                  <p className='text-sm font-mono'>#{currentRow.id}</p>
                </div>
                <div>
                  <label className='text-sm font-medium text-muted-foreground'>Created At</label>
                  <p className='text-sm'>{currentRow.createdAt ? formatDate(currentRow.createdAt) : 'Not available'}</p>
                </div>
                <div>
                  <label className='text-sm font-medium text-muted-foreground'>Last Updated</label>
                  <p className='text-sm'>{currentRow.updatedAt ? formatDate(currentRow.updatedAt) : 'Not available'}</p>
                </div>
              </div>
            </div>
          </div>
        </div>

        <DialogFooter>
          <Button
            type='button'
            variant='outline'
            onClick={() => onOpenChange(false)}
          >
            Close
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
