'use client'

import { useState } from 'react'
import { IconAlertTriangle } from '@tabler/icons-react'
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { ConfirmDialog } from '@/components/confirm-dialog'
import { Consultant, getConsultantFullName } from '../data/schema'
import { useDeleteConsultant } from '../hooks/use-consultants'

interface Props {
  open: boolean
  onOpenChange: (open: boolean) => void
  currentRow: Consultant
}

export function ConsultantsDeleteDialog({ currentRow, open, onOpenChange }: Props) {
  const [confirmText, setConfirmText] = useState('')
  const deleteConsultant = useDeleteConsultant()

  const fullName = getConsultantFullName(currentRow)
  const expectedText = 'DELETE'
  const isConfirmValid = confirmText === expectedText

  const handleDelete = async () => {
    if (!isConfirmValid) return

    try {
      await deleteConsultant.mutateAsync(currentRow.id)
      onOpenChange(false)
      setConfirmText('')
    } catch (error) {
      // Error handling is done in the mutation hook
      console.error('Failed to delete consultant:', error)
    }
  }

  const handleOpenChange = (open: boolean) => {
    if (!open) {
      setConfirmText('')
    }
    onOpenChange(open)
  }

  return (
    <ConfirmDialog
      open={open}
      onOpenChange={handleOpenChange}
      title='Delete Consultant'
      desc={`Are you sure you want to delete ${fullName}? This action cannot be undone.`}
      confirmText='Delete Consultant'
      handleConfirm={handleDelete}
      destructive
      isLoading={deleteConsultant.isPending}
      disabled={!isConfirmValid}
    >
      <Alert variant='destructive' className='mb-4'>
        <IconAlertTriangle className='h-4 w-4' />
        <AlertTitle>Warning</AlertTitle>
        <AlertDescription>
          This will permanently delete the consultant profile and all associated data.
          This action cannot be undone.
        </AlertDescription>
      </Alert>

      <div className='space-y-4'>
        <div>
          <Label htmlFor='confirm-text'>
            Type <span className='font-mono font-bold'>{expectedText}</span> to confirm deletion:
          </Label>
          <Input
            id='confirm-text'
            value={confirmText}
            onChange={(e) => setConfirmText(e.target.value)}
            placeholder={expectedText}
            className='mt-2'
          />
        </div>

        <div className='rounded-lg border p-4 bg-muted/50'>
          <h4 className='font-medium mb-2'>Consultant to be deleted:</h4>
          <div className='space-y-1 text-sm'>
            <p><span className='font-medium'>Name:</span> {fullName}</p>
            <p><span className='font-medium'>Email:</span> {currentRow.email}</p>
            <p><span className='font-medium'>ID:</span> {currentRow.id}</p>
          </div>
        </div>
      </div>
    </ConfirmDialog>
  )
}
