import { ConsultantsActionDialog } from './consultants-action-dialog'
import { ConsultantsDeleteDialog } from './consultants-delete-dialog'
import { ConsultantsViewDialog } from './consultants-view-dialog'
import { useConsultants } from '../context/consultants-context'

export function ConsultantsDialogs() {
  const { open, setOpen, currentRow, setCurrentRow } = useConsultants()
  return (
    <>
      <ConsultantsActionDialog
        key='consultant-add'
        open={open === 'add'}
        onOpenChange={() => setOpen('add')}
      />

      {currentRow && (
        <>
          <ConsultantsActionDialog
            key={`consultant-edit-${currentRow.id}`}
            open={open === 'edit'}
            onOpenChange={() => {
              setOpen('edit')
              setTimeout(() => {
                setCurrentRow(null)
              }, 500)
            }}
            currentRow={currentRow}
          />

          <ConsultantsViewDialog
            key={`consultant-view-${currentRow.id}`}
            open={open === 'view'}
            onOpenChange={() => {
              setOpen('view')
              setTimeout(() => {
                setCurrentRow(null)
              }, 500)
            }}
            currentRow={currentRow}
          />

          <ConsultantsDeleteDialog
            key={`consultant-delete-${currentRow.id}`}
            open={open === 'delete'}
            onOpenChange={() => {
              setOpen('delete')
              setTimeout(() => {
                setCurrentRow(null)
              }, 500)
            }}
            currentRow={currentRow}
          />
        </>
      )}
    </>
  )
}
