import { IconUserPlus } from '@tabler/icons-react'
import { Button } from '@/components/ui/button'
import { useConsultants } from '../context/consultants-context'
import { useUser } from '@/context/user-context'

export function ConsultantsPrimaryButtons() {
  const { setOpen } = useConsultants()
  const { permissions } = useUser()

  if (!permissions.canManageUsers) {
    return null
  }

  return (
    <div className='flex gap-2'>
      <Button className='space-x-1' onClick={() => setOpen('add')}>
        <span>Add Consultant</span> <IconUserPlus size={18} />
      </Button>
    </div>
  )
}
