import React, { useState } from 'react'
import useDialogState from '@/hooks/use-dialog-state'
import { Consultant } from '../data/schema'

type ConsultantsDialogType = 'add' | 'edit' | 'delete' | 'view'

interface ConsultantsContextType {
  open: ConsultantsDialogType | null
  setOpen: (str: ConsultantsDialogType | null) => void
  currentRow: Consultant | null
  setCurrentRow: React.Dispatch<React.SetStateAction<Consultant | null>>
}

const ConsultantsContext = React.createContext<ConsultantsContextType | null>(null)

interface Props {
  children: React.ReactNode
}

export default function ConsultantsProvider({ children }: Props) {
  const [open, setOpen] = useDialogState<ConsultantsDialogType>(null)
  const [currentRow, setCurrentRow] = useState<Consultant | null>(null)

  return (
    <ConsultantsContext value={{ open, setOpen, currentRow, setCurrentRow }}>
      {children}
    </ConsultantsContext>
  )
}

// eslint-disable-next-line react-refresh/only-export-components
export const useConsultants = () => {
  const consultantsContext = React.useContext(ConsultantsContext)

  if (!consultantsContext) {
    throw new Error('useConsultants has to be used within <ConsultantsContext>')
  }

  return consultantsContext
}
