import { createContext, useContext, useState, ReactNode } from 'react'
import { LeaveRequest } from '../data/schema'

type DialogType = 'create' | 'update' | 'delete' | null

interface NonWorkingDaysContextType {
  open: DialogType
  setOpen: (open: DialogType) => void
  currentItem: LeaveRequest | null
  setCurrentItem: (item: LeaveRequest | null) => void
}

const NonWorkingDaysContext = createContext<NonWorkingDaysContextType | undefined>(undefined)

interface NonWorkingDaysProviderProps {
  children: ReactNode
}

export default function NonWorkingDaysProvider({ children }: NonWorkingDaysProviderProps) {
  const [open, setOpen] = useState<DialogType>(null)
  const [currentItem, setCurrentItem] = useState<NonWorkingDay | null>(null)

  const handleSetOpen = (newOpen: DialogType) => {
    setOpen(newOpen)
    if (!newOpen) {
      // Clear current item when closing dialogs
      setTimeout(() => {
        setCurrentItem(null)
      }, 300) // Small delay to allow dialog close animation
    }
  }

  return (
    <NonWorkingDaysContext.Provider
      value={{
        open,
        setOpen: handleSetOpen,
        currentItem,
        setCurrentItem,
      }}
    >
      {children}
    </NonWorkingDaysContext.Provider>
  )
}

export function useNonWorkingDays() {
  const context = useContext(NonWorkingDaysContext)
  if (context === undefined) {
    throw new Error('useNonWorkingDays must be used within a NonWorkingDaysProvider')
  }
  return context
}
