import { z } from 'zod'

// API-compliant schema matching the leave request structure
export const leaveRequestSchema = z.object({
  id: z.number(),
  consultantId: z.number(),
  startDate: z.string(),
  endDate: z.string(),
  numberOfDays: z.number(),
  leaveType: z.enum(['annual', 'sick', 'maternity', 'paternity', 'unpaid', 'other']),
  reason: z.string().optional(),
  status: z.enum(['pending', 'approved', 'rejected']),
  approvedBy: z.number().optional(),
  approvedAt: z.string().optional(),
  rejectedBy: z.number().optional(),
  rejectedAt: z.string().optional(),
  rejectionReason: z.string().optional(),
  createdAt: z.string(),
  updatedAt: z.string(),
})

export const leaveRequestFormSchema = z.object({
  dateRange: z.object({
    from: z.date().optional(),
    to: z.date().optional(),
  }).refine((data) => {
    return data.from && data.to
  }, {
    message: 'Please select both start and end dates',
    path: ['from'],
  }).refine((data) => {
    if (data.from && data.to) {
      return data.to >= data.from
    }
    return true
  }, {
    message: 'End date must be after or equal to start date',
    path: ['to'],
  }),
  leaveType: z.enum(['annual', 'sick', 'maternity', 'paternity', 'unpaid', 'other'], {
    required_error: 'Please select a leave type',
  }),
  reason: z.string().optional(),
  comment: z.string().optional(),
}).refine((data) => {
  if (data.leaveType === 'other') {
    return data.reason && data.reason.trim().length > 0
  }
  return true
}, {
  message: 'Reason is required when "Other" is selected',
  path: ['reason'],
})

// Leave balance schema
export const leaveBalanceSchema = z.object({
  consultantId: z.number(),
  totalAnnualLeave: z.number(),
  usedAnnualLeave: z.number(),
  remainingAnnualLeave: z.number(),
  totalSickLeave: z.number(),
  usedSickLeave: z.number(),
  remainingSickLeave: z.number(),
})

// Type exports
export type LeaveRequest = z.infer<typeof leaveRequestSchema>
export type LeaveRequestForm = z.infer<typeof leaveRequestFormSchema>
export type LeaveBalance = z.infer<typeof leaveBalanceSchema>

// API request/response types
export interface CreateLeaveRequestRequest {
  consultantId: number
  startDate: string
  endDate: string
  numberOfDays: number
  leaveType: 'annual' | 'sick' | 'maternity' | 'paternity' | 'unpaid' | 'other'
  reason?: string
}

export interface UpdateLeaveRequestRequest extends Partial<CreateLeaveRequestRequest> {
  id: number
}

export interface ApiResponse<T> {
  data: T
  message?: string
  success: boolean
}

export interface ApiError {
  error: string
  success: boolean
  message?: string
}

// Backward compatibility - keeping old types for existing components
export type NonWorkingDay = LeaveRequest
export type NonWorkingDayForm = LeaveRequestForm

export const leaveTypeOptions = [
  { value: 'annual', label: '🏖️ Annual Leave' },
  { value: 'sick', label: '🤒 Sick Leave' },
  { value: 'maternity', label: '👶 Maternity Leave' },
  { value: 'paternity', label: '👨‍👶 Paternity Leave' },
  { value: 'unpaid', label: '📅 Unpaid Leave' },
  { value: 'other', label: '📝 Other' },
] as const

// Backward compatibility
export const reasonOptions = leaveTypeOptions

export const statusOptions = [
  { value: 'pending', label: '⏳ Pending' },
  { value: 'approved', label: '✅ Approved' },
  { value: 'rejected', label: '❌ Rejected' },
] as const

// Utility function to calculate number of days between two dates (inclusive)
export const calculateNumberOfDays = (startDate: Date, endDate: Date): number => {
  const diffTime = Math.abs(endDate.getTime() - startDate.getTime())
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24)) + 1
  return diffDays
}
