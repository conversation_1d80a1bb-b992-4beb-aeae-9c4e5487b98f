import { ConfirmDialog } from '@/components/confirm-dialog'
import { useNonWorkingDays } from '../context/non-working-days-context'
import { NonWorkingDayMutateDrawer } from './non-working-day-mutate-drawer'
import { useDeleteLeaveRequest } from '../hooks/use-leaves'

export function NonWorkingDaysDialogs() {
  const {
    open,
    setOpen,
    currentItem,
    setCurrentItem,
  } = useNonWorkingDays()

  const deleteLeaveRequest = useDeleteLeaveRequest()

  const handleDelete = async () => {
    if (!currentItem) return

    try {
      await deleteLeaveRequest.mutateAsync(currentItem.id)
      setCurrentItem(null)
      setOpen(null)
    } catch (error) {
      // Error handling is done in the hook
      console.error('Error deleting leave request:', error)
    }
  }

  return (
    <>
      {/* Create Leave Request */}
      <NonWorkingDayMutateDrawer
        key='leave-request-create'
        open={open === 'create'}
        onOpenChange={(isOpen) => {
          if (!isOpen) {
            setOpen(null)
          }
        }}
      />

      {/* Update Leave Request */}
      {currentItem && (
        <NonWorkingDayMutateDrawer
          key={`leave-request-update-${currentItem.id}`}
          open={open === 'update'}
          onOpenChange={(isOpen) => {
            if (!isOpen) {
              setOpen(null)
              setTimeout(() => {
                setCurrentItem(null)
              }, 300)
            }
          }}
          currentItem={currentItem}
        />
      )}

      {/* Delete Leave Request Confirmation */}
      {currentItem && (
        <ConfirmDialog
          open={open === 'delete'}
          onOpenChange={(isOpen) => {
            if (!isOpen) {
              setOpen(null)
            }
          }}
          handleConfirm={handleDelete}
          title='Delete Leave Request'
          desc={`Are you sure you want to delete this leave request? This action cannot be undone.`}
          isLoading={deleteLeaveRequest.isPending}
        />
      )}
    </>
  )
}
