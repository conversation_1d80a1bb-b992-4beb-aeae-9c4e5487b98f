import { Header } from '@/components/layout/header'
import { Main } from '@/components/layout/main'
import { ProfileDropdown } from '@/components/profile-dropdown'
import { Search } from '@/components/search'
import { ThemeSwitch } from '@/components/theme-switch'
import { LoadingSpinner } from '@/components/ui/loading-spinner'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { nonWorkingDaysColumns } from './components/non-working-days-columns'
import { NonWorkingDaysDataTable } from './components/non-working-days-data-table'
import { NonWorkingDaysDialogs } from './components/non-working-days-dialogs'
import { NonWorkingDaysPrimaryButtons } from './components/non-working-days-primary-buttons'
import NonWorkingDaysProvider from './context/non-working-days-context'
import { useMyLeaveRequests } from './hooks/use-leaves'

export default function NonWorkingDays() {
  const { data: leaveRequests, isLoading, error } = useMyLeaveRequests()

  return (
    <NonWorkingDaysProvider>
      <Header fixed>
        <Search />
        <div className='ml-auto flex items-center space-x-4'>
          <ThemeSwitch />
          <ProfileDropdown />
        </div>
      </Header>

      <Main>
        <div className='mb-2 flex flex-wrap items-center justify-between gap-x-4 space-y-2'>
          <div>
            <h2 className='text-2xl font-bold tracking-tight'>Leave Requests</h2>
            <p className='text-muted-foreground'>
              Manage your leave requests including annual leave, sick leave, and other absences.
            </p>
          </div>
          <NonWorkingDaysPrimaryButtons />
        </div>

        <div className='-mx-4 flex-1 overflow-auto px-4 py-1 lg:flex-row lg:space-x-12 lg:space-y-0'>
          {isLoading ? (
            <div className='flex items-center justify-center py-8'>
              <LoadingSpinner className='h-8 w-8' />
              <span className='ml-2'>Loading leave requests...</span>
            </div>
          ) : error ? (
            <Alert variant='destructive'>
              <AlertDescription>
                Failed to load leave requests. Please try again later.
              </AlertDescription>
            </Alert>
          ) : (
            <NonWorkingDaysDataTable
              data={leaveRequests || []}
              columns={nonWorkingDaysColumns}
            />
          )}
        </div>
      </Main>

      <NonWorkingDaysDialogs />
    </NonWorkingDaysProvider>
  )
}
