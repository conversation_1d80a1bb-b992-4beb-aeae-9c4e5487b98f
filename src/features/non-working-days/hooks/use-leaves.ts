import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import { toast } from '@/hooks/use-toast'
import {
  getAllLeaveRequests,
  createLeaveRequest,
  getPendingLeaveRequests,
  getLeaveRequestById,
  updateLeaveRequest,
  deleteLeaveRequest,
  getConsultantLeaveRequests,
  approveLeaveRequest,
  rejectLeaveRequest,
  getLeaveBalance,
  getMyLeaveRequests,
  getMyLeaveBalance,
} from '@/services/leaves'
import type {
  LeaveRequest,
  CreateLeaveRequestRequest,
  UpdateLeaveRequestRequest,
} from '../data/schema'

// Query keys
export const leaveKeys = {
  all: ['leaves'] as const,
  lists: () => [...leaveKeys.all, 'list'] as const,
  list: (filters: Record<string, any>) => [...leaveKeys.lists(), { filters }] as const,
  details: () => [...leaveKeys.all, 'detail'] as const,
  detail: (id: number) => [...leaveKeys.details(), id] as const,
  pending: () => [...leaveKeys.all, 'pending'] as const,
  consultant: (consultantId: number) => [...leave<PERSON>eys.all, 'consultant', consultantId] as const,
  balance: (consultantId: number) => [...leaveKeys.all, 'balance', consultantId] as const,
  my: () => [...leaveKeys.all, 'my'] as const,
  myBalance: () => [...leaveKeys.all, 'myBalance'] as const,
}

/**
 * Hook to get all leave requests (admin only)
 */
export const useLeaveRequests = () => {
  return useQuery({
    queryKey: leaveKeys.lists(),
    queryFn: getAllLeaveRequests,
    staleTime: 5 * 60 * 1000, // 5 minutes
  })
}

/**
 * Hook to get a specific leave request by ID
 */
export const useLeaveRequest = (id: number) => {
  return useQuery({
    queryKey: leaveKeys.detail(id),
    queryFn: () => getLeaveRequestById(id),
    enabled: !!id,
    staleTime: 5 * 60 * 1000, // 5 minutes
  })
}

/**
 * Hook to get pending leave requests (admin only)
 */
export const usePendingLeaveRequests = () => {
  return useQuery({
    queryKey: leaveKeys.pending(),
    queryFn: getPendingLeaveRequests,
    staleTime: 2 * 60 * 1000, // 2 minutes
  })
}

/**
 * Hook to get leave requests for a specific consultant
 */
export const useConsultantLeaveRequests = (consultantId: number) => {
  return useQuery({
    queryKey: leaveKeys.consultant(consultantId),
    queryFn: () => getConsultantLeaveRequests(consultantId),
    enabled: !!consultantId,
    staleTime: 5 * 60 * 1000, // 5 minutes
  })
}

/**
 * Hook to get leave balance for a consultant
 */
export const useLeaveBalance = (consultantId: number) => {
  return useQuery({
    queryKey: leaveKeys.balance(consultantId),
    queryFn: () => getLeaveBalance(consultantId),
    enabled: !!consultantId,
    staleTime: 10 * 60 * 1000, // 10 minutes
  })
}

/**
 * Hook to get current user's leave requests
 */
export const useMyLeaveRequests = () => {
  return useQuery({
    queryKey: leaveKeys.my(),
    queryFn: getMyLeaveRequests,
    staleTime: 5 * 60 * 1000, // 5 minutes
  })
}

/**
 * Hook to get current user's leave balance
 */
export const useMyLeaveBalance = () => {
  return useQuery({
    queryKey: leaveKeys.myBalance(),
    queryFn: getMyLeaveBalance,
    staleTime: 10 * 60 * 1000, // 10 minutes
  })
}

/**
 * Hook to create a new leave request
 */
export const useCreateLeaveRequest = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: createLeaveRequest,
    onSuccess: (newLeaveRequest) => {
      // Invalidate and refetch relevant queries
      queryClient.invalidateQueries({ queryKey: leaveKeys.lists() })
      queryClient.invalidateQueries({ queryKey: leaveKeys.my() })
      queryClient.invalidateQueries({ queryKey: leaveKeys.consultant(newLeaveRequest.consultantId) })
      queryClient.invalidateQueries({ queryKey: leaveKeys.pending() })

      toast({
        title: 'Leave request created',
        description: 'Your leave request has been submitted successfully.',
      })
    },
    onError: (error: any) => {
      const errorMessage = error.response?.data?.message || error.message || 'Failed to create leave request'
      toast({
        variant: 'destructive',
        title: 'Failed to create leave request',
        description: errorMessage,
      })
    },
  })
}

/**
 * Hook to update a leave request
 */
export const useUpdateLeaveRequest = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: ({ id, ...data }: UpdateLeaveRequestRequest) => updateLeaveRequest(id, data),
    onSuccess: (updatedLeaveRequest) => {
      // Invalidate and refetch relevant queries
      queryClient.invalidateQueries({ queryKey: leaveKeys.lists() })
      queryClient.invalidateQueries({ queryKey: leaveKeys.my() })
      queryClient.invalidateQueries({ queryKey: leaveKeys.detail(updatedLeaveRequest.id) })
      queryClient.invalidateQueries({ queryKey: leaveKeys.consultant(updatedLeaveRequest.consultantId) })
      queryClient.invalidateQueries({ queryKey: leaveKeys.pending() })

      toast({
        title: 'Leave request updated',
        description: 'Your leave request has been updated successfully.',
      })
    },
    onError: (error: any) => {
      const errorMessage = error.response?.data?.message || error.message || 'Failed to update leave request'
      toast({
        variant: 'destructive',
        title: 'Failed to update leave request',
        description: errorMessage,
      })
    },
  })
}

/**
 * Hook to delete a leave request
 */
export const useDeleteLeaveRequest = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: deleteLeaveRequest,
    onSuccess: () => {
      // Invalidate and refetch relevant queries
      queryClient.invalidateQueries({ queryKey: leaveKeys.lists() })
      queryClient.invalidateQueries({ queryKey: leaveKeys.my() })
      queryClient.invalidateQueries({ queryKey: leaveKeys.pending() })

      toast({
        title: 'Leave request deleted',
        description: 'The leave request has been deleted successfully.',
      })
    },
    onError: (error: any) => {
      const errorMessage = error.response?.data?.message || error.message || 'Failed to delete leave request'
      toast({
        variant: 'destructive',
        title: 'Failed to delete leave request',
        description: errorMessage,
      })
    },
  })
}

/**
 * Hook to approve a leave request (admin only)
 */
export const useApproveLeaveRequest = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: approveLeaveRequest,
    onSuccess: (approvedLeaveRequest) => {
      // Invalidate and refetch relevant queries
      queryClient.invalidateQueries({ queryKey: leaveKeys.lists() })
      queryClient.invalidateQueries({ queryKey: leaveKeys.detail(approvedLeaveRequest.id) })
      queryClient.invalidateQueries({ queryKey: leaveKeys.consultant(approvedLeaveRequest.consultantId) })
      queryClient.invalidateQueries({ queryKey: leaveKeys.pending() })
      queryClient.invalidateQueries({ queryKey: leaveKeys.balance(approvedLeaveRequest.consultantId) })

      toast({
        title: 'Leave request approved',
        description: 'The leave request has been approved successfully.',
      })
    },
    onError: (error: any) => {
      const errorMessage = error.response?.data?.message || error.message || 'Failed to approve leave request'
      toast({
        variant: 'destructive',
        title: 'Failed to approve leave request',
        description: errorMessage,
      })
    },
  })
}

/**
 * Hook to reject a leave request (admin only)
 */
export const useRejectLeaveRequest = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: ({ id, rejectionReason }: { id: number; rejectionReason?: string }) =>
      rejectLeaveRequest(id, rejectionReason),
    onSuccess: (rejectedLeaveRequest) => {
      // Invalidate and refetch relevant queries
      queryClient.invalidateQueries({ queryKey: leaveKeys.lists() })
      queryClient.invalidateQueries({ queryKey: leaveKeys.detail(rejectedLeaveRequest.id) })
      queryClient.invalidateQueries({ queryKey: leaveKeys.consultant(rejectedLeaveRequest.consultantId) })
      queryClient.invalidateQueries({ queryKey: leaveKeys.pending() })

      toast({
        title: 'Leave request rejected',
        description: 'The leave request has been rejected.',
      })
    },
    onError: (error: any) => {
      const errorMessage = error.response?.data?.message || error.message || 'Failed to reject leave request'
      toast({
        variant: 'destructive',
        title: 'Failed to reject leave request',
        description: errorMessage,
      })
    },
  })
}

/**
 * Hook to get leave requests with real-time updates
 */
export const useLeaveRequestsWithRefresh = (refetchInterval?: number) => {
  return useQuery({
    queryKey: leaveKeys.lists(),
    queryFn: getAllLeaveRequests,
    staleTime: 2 * 60 * 1000, // 2 minutes
    refetchInterval: refetchInterval || false,
    refetchOnWindowFocus: true,
    refetchOnReconnect: true,
  })
}

/**
 * Hook to prefetch a leave request's details
 */
export const usePrefetchLeaveRequest = () => {
  const queryClient = useQueryClient()

  return (id: number) => {
    queryClient.prefetchQuery({
      queryKey: leaveKeys.detail(id),
      queryFn: () => getLeaveRequestById(id),
      staleTime: 5 * 60 * 1000, // 5 minutes
    })
  }
}
