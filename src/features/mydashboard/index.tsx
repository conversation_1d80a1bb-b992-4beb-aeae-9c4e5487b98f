import { Header } from '@/components/layout/header'
import { Main } from '@/components/layout/main'
import { ProfileDropdown } from '@/components/profile-dropdown'
import { Search } from '@/components/search'
import { ThemeSwitch } from '@/components/theme-switch'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { WorkTimeChart } from './components/work-time-chart'
import { LeavesOverview } from './components/leaves-overview'
import { ExpensesOverview } from './components/expenses-overview'
import { WorkTimeStats } from './components/work-time-stats'

export default function MyDashboard() {
  return (
    <>
      {/* ===== Top Heading ===== */}
      <Header>
        <div className='ml-auto flex items-center space-x-4'>
          <Search />
          <ThemeSwitch />
          <ProfileDropdown />
        </div>
      </Header>

      {/* ===== Main ===== */}
      <Main>
        <div className='mb-6 flex items-center justify-between space-y-2'>
          <div>
            <h1 className='text-2xl font-bold tracking-tight'>My Dashboard</h1>
            <p className='text-muted-foreground'>
              Your personal overview of work time, leaves, and expenses.
            </p>
          </div>
        </div>

        <div className='space-y-6'>
          {/* Work Time Section */}
          <div className='grid gap-6 md:grid-cols-2 lg:grid-cols-3'>
            <Card className='md:col-span-1'>
              <CardHeader>
                <CardTitle>Work Time Distribution</CardTitle>
                <CardDescription>
                  Remote vs onsite work breakdown
                </CardDescription>
              </CardHeader>
              <CardContent>
                <WorkTimeChart />
              </CardContent>
            </Card>
            
            <Card className='md:col-span-1 lg:col-span-2'>
              <CardHeader>
                <CardTitle>Work Time Statistics</CardTitle>
                <CardDescription>
                  Your work time summary for this month
                </CardDescription>
              </CardHeader>
              <CardContent>
                <WorkTimeStats />
              </CardContent>
            </Card>
          </div>

          {/* Leaves and Expenses Section */}
          <div className='grid gap-6 md:grid-cols-2'>
            <Card>
              <CardHeader>
                <CardTitle>Leaves Overview</CardTitle>
                <CardDescription>
                  Your recent and upcoming non-working days
                </CardDescription>
              </CardHeader>
              <CardContent>
                <LeavesOverview />
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Expenses Overview</CardTitle>
                <CardDescription>
                  Your recent expense submissions and status
                </CardDescription>
              </CardHeader>
              <CardContent>
                <ExpensesOverview />
              </CardContent>
            </Card>
          </div>
        </div>
      </Main>
    </>
  )
}
