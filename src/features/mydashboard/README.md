# My Dashboard Feature

A personal dashboard that provides users with a comprehensive overview of their work data, including time tracking, leaves, and expenses.

## Features

### 🍩 Work Time Distribution Chart
- **Donut chart** showing remote vs onsite work breakdown
- **Percentage tooltips** (as per user preferences) instead of absolute hours
- **Visual indicators** with color-coded legend
- **Summary statistics** showing total hours and percentages

### 📊 Work Time Statistics
- **Total hours** worked this month
- **Working days** logged
- **Average hours per day**
- **Active clients** count
- **Location breakdown** (remote vs office)
- **Recent activity** list with latest time entries

### 🏖️ Leaves Overview
- **Summary stats**: Total days taken this year and upcoming leaves count
- **Upcoming leaves** in the next 3 months with visual alerts
- **Recent leaves** from the last 3 months
- **Leave type indicators** with emoji badges
- **Duration calculation** for multi-day leaves

### 💰 Expenses Overview
- **Total amount** across all expense items
- **Status breakdown** with visual indicators (Draft, Submitted, Approved, Rejected)
- **Recent expenses** with client and amount information
- **Expense count** per item
- **Date ranges** for expense periods

## Technical Implementation

### Components Structure
```
src/features/mydashboard/
├── index.tsx                    # Main dashboard component
├── components/
│   ├── work-time-chart.tsx     # Donut chart using recharts
│   ├── work-time-stats.tsx     # Work time statistics cards
│   ├── leaves-overview.tsx     # Leaves summary and list
│   └── expenses-overview.tsx   # Expenses summary and list
└── README.md                   # This file
```

### Data Sources
- **Time Tracking**: Uses `mockTimeEntries` from timesheet feature
- **Leaves**: Uses `mockNonWorkingDays` from non-working-days feature  
- **Expenses**: Uses `mockExpenseItems` from expenses feature

### Chart Implementation
- Built with **recharts** library (already installed)
- Custom tooltip showing **percentages** instead of absolute values
- Responsive design with proper color theming
- Follows user preference for percentage display in tooltips

### UI/UX Design
- **Consistent layout** following established patterns
- **Header with search, theme switch, and profile dropdown**
- **Card-based layout** for different sections
- **Responsive grid** that adapts to different screen sizes
- **Modern shadcn UI components** throughout

## Navigation

The dashboard is accessible via:
- **URL**: `/mydashboard`
- **Sidebar**: "My Dashboard" with pie chart icon in the General section
- **Icon**: PieChart from lucide-react

## Future Enhancements

- Real API integration instead of mock data
- Date range filtering for statistics
- Export functionality for reports
- Interactive chart drilling down to detailed views
- Customizable dashboard widgets
- Performance metrics and trends over time
