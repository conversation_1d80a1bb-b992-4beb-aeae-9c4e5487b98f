import { <PERSON><PERSON><PERSON>, Pie, Cell, ResponsiveContainer, Tooltip } from 'recharts'
import { mockTimeEntries } from '@/features/timesheet/data/mock-data'

interface WorkTimeData {
  name: string
  value: number
  hours: number
  color: string
  totalHours: number
}

const COLORS = {
  remote: 'hsl(var(--chart-1))',
  office: 'hsl(var(--chart-2))',
}

// Custom tooltip component that shows percentages
const CustomTooltip = ({ active, payload }: any) => {
  if (active && payload && payload.length) {
    const data = payload[0].payload
    const total = payload[0].payload.totalHours
    const percentage = total > 0 ? ((data.hours / total) * 100).toFixed(1) : 0

    return (
      <div className='rounded-lg border bg-background p-2 shadow-md'>
        <p className='font-medium'>{data.name}</p>
        <p className='text-sm text-muted-foreground'>
          {percentage}% ({data.hours}h)
        </p>
      </div>
    )
  }
  return null
}

export function WorkTimeChart() {
  // Calculate remote vs office hours from mock data
  const remoteHours = mockTimeEntries
    .filter(entry => entry.location === 'home')
    .reduce((sum, entry) => sum + entry.hours, 0)

  const officeHours = mockTimeEntries
    .filter(entry => entry.location === 'office')
    .reduce((sum, entry) => sum + entry.hours, 0)

  const totalHours = remoteHours + officeHours

  const data: WorkTimeData[] = [
    {
      name: 'Remote Work',
      value: remoteHours,
      hours: remoteHours,
      color: COLORS.remote,
      totalHours,
    },
    {
      name: 'Office Work',
      value: officeHours,
      hours: officeHours,
      color: COLORS.office,
      totalHours,
    },
  ]

  // Calculate percentages for display
  const remotePercentage = totalHours > 0 ? ((remoteHours / totalHours) * 100).toFixed(1) : 0
  const officePercentage = totalHours > 0 ? ((officeHours / totalHours) * 100).toFixed(1) : 0

  if (totalHours === 0) {
    return (
      <div className='flex h-[200px] items-center justify-center text-muted-foreground'>
        No work time data available
      </div>
    )
  }

  return (
    <div className='space-y-4'>
      <ResponsiveContainer width='100%' height={200}>
        <PieChart>
          <Pie
            data={data}
            cx='50%'
            cy='50%'
            innerRadius={40}
            outerRadius={80}
            paddingAngle={2}
            dataKey='value'
          >
            {data.map((entry, index) => (
              <Cell key={`cell-${index}`} fill={entry.color} />
            ))}
          </Pie>
          <Tooltip content={<CustomTooltip />} />
        </PieChart>
      </ResponsiveContainer>

      {/* Summary stats */}
      <div className='space-y-2 text-center text-sm'>
        <div className='flex items-center justify-between'>
          <div className='flex items-center gap-2'>
            <div
              className='h-3 w-3 rounded-full'
              style={{ backgroundColor: COLORS.remote }}
            />
            <span>Remote Work</span>
          </div>
          <span className='font-medium'>{remotePercentage}% ({remoteHours}h)</span>
        </div>
        <div className='flex items-center justify-between'>
          <div className='flex items-center gap-2'>
            <div
              className='h-3 w-3 rounded-full'
              style={{ backgroundColor: COLORS.office }}
            />
            <span>Office Work</span>
          </div>
          <span className='font-medium'>{officePercentage}% ({officeHours}h)</span>
        </div>
        <div className='border-t pt-2 font-medium'>
          Total: {totalHours}h
        </div>
      </div>
    </div>
  )
}
