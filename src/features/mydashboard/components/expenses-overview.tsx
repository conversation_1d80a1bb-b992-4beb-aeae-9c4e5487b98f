import { Receipt, DollarSign, Clock, CheckCircle, XCircle, AlertCircle } from 'lucide-react'
import { Badge } from '@/components/ui/badge'
import { mockExpenseItems } from '@/features/expenses/data/mock-data'

export function ExpensesOverview() {
  // Calculate summary statistics
  const totalAmount = mockExpenseItems.reduce((sum, item) => sum + item.totalAmount, 0)
  const totalItems = mockExpenseItems.length
  
  // Group by status
  const statusCounts = mockExpenseItems.reduce((acc, item) => {
    acc[item.status] = (acc[item.status] || 0) + 1
    return acc
  }, {} as Record<string, number>)

  // Get recent expense items (last 5)
  const recentExpenses = mockExpenseItems
    .sort((a, b) => new Date(b.createdAt || '').getTime() - new Date(a.createdAt || '').getTime())
    .slice(0, 5)

  // Status configuration
  const statusConfig = {
    draft: { 
      label: 'Draft', 
      icon: Clock, 
      color: 'text-gray-600', 
      bgColor: 'bg-gray-100',
      variant: 'secondary' as const
    },
    submitted: { 
      label: 'Submitted', 
      icon: AlertCircle, 
      color: 'text-blue-600', 
      bgColor: 'bg-blue-100',
      variant: 'default' as const
    },
    approved: { 
      label: 'Approved', 
      icon: CheckCircle, 
      color: 'text-green-600', 
      bgColor: 'bg-green-100',
      variant: 'default' as const
    },
    rejected: { 
      label: 'Rejected', 
      icon: XCircle, 
      color: 'text-red-600', 
      bgColor: 'bg-red-100',
      variant: 'destructive' as const
    },
  }

  return (
    <div className='space-y-4'>
      {/* Summary Stats */}
      <div className='grid grid-cols-2 gap-4'>
        <div className='rounded-lg border p-3 text-center'>
          <div className='flex items-center justify-center gap-2 mb-1'>
            <DollarSign className='h-4 w-4 text-green-600' />
            <span className='text-sm font-medium'>Total Amount</span>
          </div>
          <p className='text-2xl font-bold'>CHF {totalAmount.toLocaleString()}</p>
          <p className='text-xs text-muted-foreground'>All expenses</p>
        </div>
        <div className='rounded-lg border p-3 text-center'>
          <div className='flex items-center justify-center gap-2 mb-1'>
            <Receipt className='h-4 w-4 text-blue-600' />
            <span className='text-sm font-medium'>Total Items</span>
          </div>
          <p className='text-2xl font-bold'>{totalItems}</p>
          <p className='text-xs text-muted-foreground'>Expense items</p>
        </div>
      </div>

      {/* Status Breakdown */}
      <div className='space-y-3'>
        <h4 className='font-medium'>Status Breakdown</h4>
        <div className='grid grid-cols-2 gap-2'>
          {Object.entries(statusConfig).map(([status, config]) => {
            const count = statusCounts[status] || 0
            const Icon = config.icon
            return (
              <div key={status} className='flex items-center justify-between rounded-lg border p-2'>
                <div className='flex items-center gap-2'>
                  <div className={`rounded p-1 ${config.bgColor}`}>
                    <Icon className={`h-3 w-3 ${config.color}`} />
                  </div>
                  <span className='text-sm'>{config.label}</span>
                </div>
                <span className='font-medium'>{count}</span>
              </div>
            )
          })}
        </div>
      </div>

      {/* Recent Expenses */}
      <div className='space-y-3'>
        <h4 className='font-medium'>Recent Expenses</h4>
        <div className='space-y-2'>
          {recentExpenses.map((expense) => {
            const config = statusConfig[expense.status]
            return (
              <div key={expense.id} className='flex items-center justify-between rounded-lg border p-3'>
                <div className='space-y-1 flex-1 min-w-0'>
                  <div className='flex items-center gap-2'>
                    <h5 className='font-medium text-sm truncate'>{expense.title}</h5>
                    <Badge variant={config.variant} className='text-xs'>
                      {config.label}
                    </Badge>
                  </div>
                  <div className='flex items-center gap-2 text-xs text-muted-foreground'>
                    <span>{expense.client}</span>
                    <span>•</span>
                    <span>{expense.expenseCount} expense{expense.expenseCount > 1 ? 's' : ''}</span>
                  </div>
                  <p className='text-xs text-muted-foreground'>
                    {new Date(expense.startDate).toLocaleDateString()} - {new Date(expense.endDate).toLocaleDateString()}
                  </p>
                </div>
                <div className='text-right'>
                  <p className='font-medium text-sm'>CHF {expense.totalAmount.toLocaleString()}</p>
                </div>
              </div>
            )
          })}
        </div>
      </div>

      {/* No data state */}
      {recentExpenses.length === 0 && (
        <div className='text-center py-6 text-muted-foreground'>
          <Receipt className='h-8 w-8 mx-auto mb-2 opacity-50' />
          <p className='text-sm'>No expense items found</p>
        </div>
      )}
    </div>
  )
}
