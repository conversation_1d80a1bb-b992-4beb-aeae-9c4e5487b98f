import { useParams, useNavigate } from '@tanstack/react-router'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Separator } from '@/components/ui/separator'
import { IconArrowLeft, IconPlus, IconReceipt, IconCalendar, IconBuilding, IconFileText } from '@tabler/icons-react'
import { Header } from '@/components/layout/header'
import { Main } from '@/components/layout/main'
import { ProfileDropdown } from '@/components/profile-dropdown'
import { Search } from '@/components/search'
import { ThemeSwitch } from '@/components/theme-switch'
import { mockExpenseItems, mockExpenses, expenseCategories } from '../data/mock-data'
import { ExpenseMutateDrawer } from '../components/expense-mutate-drawer'
import { useExpenses } from '../context/expenses-context'
import ExpensesProvider from '../context/expenses-context'

const statusColors = {
  draft: 'secondary',
  submitted: 'default',
  approved: 'default',
  rejected: 'destructive',
} as const

const statusLabels = {
  draft: 'Draft',
  submitted: 'Submitted',
  approved: 'Approved',
  rejected: 'Rejected',
}

function ExpenseItemDetailsContent() {
  const { itemId } = useParams({ from: '/_authenticated/expenses/$itemId' })
  const navigate = useNavigate()
  const { open, setOpen, setSelectedItemId } = useExpenses()

  // Find the expense item
  const expenseItem = mockExpenseItems.find(item => item.id === itemId)
  
  if (!expenseItem) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <h3 className="text-lg font-semibold">Expense Item Not Found</h3>
          <p className="text-muted-foreground mt-2">The requested expense item could not be found.</p>
          <Button 
            onClick={() => navigate({ to: '/expenses' })} 
            className="mt-4"
          >
            Back to Expenses
          </Button>
        </div>
      </div>
    )
  }

  const itemExpenses = mockExpenses.filter(expense => expense.itemId === expenseItem.id)

  const handleAddExpense = () => {
    setSelectedItemId(expenseItem.id)
    setOpen('create-expense')
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      weekday: 'short',
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    })
  }

  const getCategoryLabel = (category: string) => {
    return expenseCategories.find(cat => cat.value === category)?.label || category
  }

  return (
    <>
      <Header fixed>
        <div className="flex items-center gap-4">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => navigate({ to: '/expenses' })}
            className="flex items-center gap-2"
          >
            <IconArrowLeft className="h-4 w-4" />
            Back to Expenses
          </Button>
          <Separator orientation="vertical" className="h-6" />
          <Search />
        </div>
        <div className='ml-auto flex items-center space-x-4'>
          <ThemeSwitch />
          <ProfileDropdown />
        </div>
      </Header>

      <Main>
        <div className="space-y-6">
          {/* Header Section */}
          <div className="flex items-start justify-between gap-4">
            <div className="flex-1 min-w-0">
              <div className="flex items-center gap-3 mb-2">
                <h1 className="text-2xl font-bold tracking-tight truncate">
                  {expenseItem.title}
                </h1>
                <Badge variant={statusColors[expenseItem.status]} className="flex-shrink-0">
                  {statusLabels[expenseItem.status]}
                </Badge>
              </div>
              <p className="text-muted-foreground">
                Manage expenses for this item
              </p>
            </div>
            <Button onClick={handleAddExpense} className="flex-shrink-0">
              <IconPlus className="h-4 w-4 mr-2" />
              Add Expense
            </Button>
          </div>

          {/* Item Details */}
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-4 p-4 bg-muted/50 rounded-lg">
            <div className="flex items-start space-x-3">
              <IconBuilding className="h-5 w-5 text-muted-foreground mt-0.5 flex-shrink-0" />
              <div className="min-w-0 flex-1">
                <p className="text-sm font-medium">Client</p>
                <p className="text-sm text-muted-foreground truncate">{expenseItem.client}</p>
              </div>
            </div>
            <div className="flex items-start space-x-3">
              <IconCalendar className="h-5 w-5 text-muted-foreground mt-0.5 flex-shrink-0" />
              <div className="min-w-0 flex-1">
                <p className="text-sm font-medium">Period</p>
                <p className="text-sm text-muted-foreground">
                  {formatDate(expenseItem.startDate)}
                  {expenseItem.startDate !== expenseItem.endDate && (
                    <> - {formatDate(expenseItem.endDate)}</>
                  )}
                </p>
              </div>
            </div>
            <div className="flex items-start space-x-3">
              <IconReceipt className="h-5 w-5 text-muted-foreground mt-0.5 flex-shrink-0" />
              <div className="min-w-0 flex-1">
                <p className="text-sm font-medium">Total Amount</p>
                <p className="text-lg font-semibold text-green-600">
                  {expenseItem.totalAmount.toLocaleString('de-CH', {
                    style: 'currency',
                    currency: 'CHF',
                  })}
                </p>
              </div>
            </div>
          </div>

          {/* Description */}
          {expenseItem.description && (
            <div className="flex items-start space-x-3 p-4 bg-blue-50 dark:bg-blue-950/20 rounded-lg border border-blue-200 dark:border-blue-800">
              <IconFileText className="h-5 w-5 text-blue-600 dark:text-blue-400 mt-0.5 flex-shrink-0" />
              <div className="min-w-0 flex-1">
                <p className="text-sm font-medium text-blue-900 dark:text-blue-100">Description</p>
                <p className="text-sm text-blue-700 dark:text-blue-300 mt-1 leading-relaxed">
                  {expenseItem.description}
                </p>
              </div>
            </div>
          )}

          <Separator />

          {/* Expenses Section */}
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <h2 className="text-lg font-semibold">Expenses</h2>
                <p className="text-sm text-muted-foreground">
                  {itemExpenses.length} {itemExpenses.length === 1 ? 'expense' : 'expenses'} recorded
                </p>
              </div>
            </div>

            <div className="space-y-3">
              {itemExpenses.length > 0 ? (
                itemExpenses.map((expense) => (
                  <div
                    key={expense.id}
                    className="border rounded-lg p-4 hover:bg-muted/30 transition-colors"
                  >
                    <div className="space-y-3">
                      <div className="flex items-start justify-between gap-4">
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center gap-2 mb-2">
                            <h4 className="font-medium truncate">{expense.designation}</h4>
                            <Badge variant="outline" className="flex-shrink-0">
                              {getCategoryLabel(expense.category)}
                            </Badge>
                          </div>
                        </div>
                      </div>
                      
                      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3 text-sm">
                        <div className="flex items-center space-x-2">
                          <IconCalendar className="h-4 w-4 text-muted-foreground flex-shrink-0" />
                          <div>
                            <span className="font-medium">Date:</span>
                            <span className="ml-1 text-muted-foreground">{formatDate(expense.date)}</span>
                          </div>
                        </div>
                        
                        <div className="flex items-center space-x-2">
                          <IconReceipt className="h-4 w-4 text-muted-foreground flex-shrink-0" />
                          <div>
                            <span className="font-medium">Amount:</span>
                            <span className="ml-1 font-semibold">
                              {expense.amount.toLocaleString('de-CH', {
                                style: 'currency',
                                currency: expense.currency,
                              })}
                            </span>
                            {expense.amountCHF && expense.currency !== 'CHF' && (
                              <div className="text-xs text-green-600 mt-0.5">
                                ≈ {expense.amountCHF.toLocaleString('de-CH', {
                                  style: 'currency',
                                  currency: 'CHF',
                                })}
                              </div>
                            )}
                          </div>
                        </div>

                        {expense.receiptUrl && (
                          <div className="flex items-center space-x-2">
                            <IconFileText className="h-4 w-4 text-muted-foreground flex-shrink-0" />
                            <div>
                              <span className="font-medium">Receipt:</span>
                              <Button variant="link" size="sm" className="h-auto p-0 ml-1 text-blue-600">
                                View File
                              </Button>
                            </div>
                          </div>
                        )}
                      </div>

                      {expense.comment && (
                        <div className="pt-2 border-t border-muted">
                          <p className="text-sm text-muted-foreground italic leading-relaxed">
                            "{expense.comment}"
                          </p>
                        </div>
                      )}
                    </div>
                  </div>
                ))
              ) : (
                <div className="text-center py-12 text-muted-foreground border-2 border-dashed border-muted rounded-lg">
                  <IconReceipt className="h-16 w-16 mx-auto mb-4 opacity-50" />
                  <h4 className="text-lg font-medium mb-2">No expenses yet</h4>
                  <p className="text-sm mb-4">Start by adding your first expense to this item</p>
                  <Button onClick={handleAddExpense} variant="outline" size="sm">
                    <IconPlus className="h-4 w-4 mr-2" />
                    Add First Expense
                  </Button>
                </div>
              )}
            </div>
          </div>
        </div>
      </Main>

      {/* Add Expense Drawer */}
      <ExpenseMutateDrawer
        key="expense-create"
        open={open === 'create-expense'}
        onOpenChange={(isOpen) => setOpen(isOpen ? 'create-expense' : null)}
        itemId={expenseItem.id}
      />
    </>
  )
}

export default function ExpenseItemDetails() {
  return (
    <ExpensesProvider>
      <ExpenseItemDetailsContent />
    </ExpensesProvider>
  )
}
