import { toast } from '@/hooks/use-toast'
import { ConfirmDialog } from '@/components/confirm-dialog'
import { useExpenses } from '../context/expenses-context'
import { ExpenseItemMutateDrawer } from './expense-item-mutate-drawer'
import { ExpenseMutateDrawer } from './expense-mutate-drawer'

export function ExpensesDialogs() {
  const {
    open,
    setOpen,
    currentItem,
    setCurrentItem,
    currentExpense,
    setCurrentExpense,
    selectedItemId
  } = useExpenses()

  const handleDeleteItem = () => {
    if (!currentItem) return

    // TODO: Implement actual delete API call
    console.log('Deleting expense item:', currentItem.id)

    toast({
      title: 'Expense item deleted',
      description: `Successfully deleted expense item "${currentItem.title}".`,
    })

    setCurrentItem(null)
    setOpen(null)
  }

  const handleDeleteExpense = () => {
    if (!currentExpense) return

    // TODO: Implement actual delete API call
    console.log('Deleting expense:', currentExpense.id)

    toast({
      title: 'Expense deleted',
      description: `Successfully deleted expense "${currentExpense.designation}".`,
    })

    setCurrentExpense(null)
    setOpen(null)
  }

  return (
    <>
      {/* Create Expense Item */}
      <ExpenseItemMutateDrawer
        key='expense-item-create'
        open={open === 'create-item'}
        onOpenChange={(isOpen) => setOpen(isOpen ? 'create-item' : null)}
      />

      {/* Update Expense Item */}
      {currentItem && (
        <ExpenseItemMutateDrawer
          key={`expense-item-update-${currentItem.id}`}
          open={open === 'update-item'}
          onOpenChange={(isOpen) => {
            setOpen(isOpen ? 'update-item' : null)
            if (!isOpen) {
              setTimeout(() => {
                setCurrentItem(null)
              }, 500)
            }
          }}
          currentItem={currentItem}
        />
      )}



      {/* Delete Expense Item Confirmation */}
      {currentItem && (
        <ConfirmDialog
          open={open === 'delete-item'}
          onOpenChange={(isOpen) => setOpen(isOpen ? 'delete-item' : null)}
          handleConfirm={handleDeleteItem}
          title='Delete Expense Item'
          desc={`Are you sure you want to delete "${currentItem.title}"? This will also delete all associated expenses. This action cannot be undone.`}
        />
      )}

      {/* Create Expense */}
      <ExpenseMutateDrawer
        key='expense-create'
        open={open === 'create-expense'}
        onOpenChange={(isOpen) => setOpen(isOpen ? 'create-expense' : null)}
        itemId={selectedItemId || undefined}
      />

      {/* Update Expense */}
      {currentExpense && (
        <ExpenseMutateDrawer
          key={`expense-update-${currentExpense.id}`}
          open={open === 'update-expense'}
          onOpenChange={(isOpen) => {
            setOpen(isOpen ? 'update-expense' : null)
            if (!isOpen) {
              setTimeout(() => {
                setCurrentExpense(null)
              }, 500)
            }
          }}
          currentExpense={currentExpense}
          itemId={currentExpense.itemId}
        />
      )}

      {/* Delete Expense Confirmation */}
      {currentExpense && (
        <ConfirmDialog
          open={open === 'delete-expense'}
          onOpenChange={(isOpen) => setOpen(isOpen ? 'delete-expense' : null)}
          handleConfirm={handleDeleteExpense}
          title='Delete Expense'
          desc={`Are you sure you want to delete the expense "${currentExpense.designation}"? This action cannot be undone.`}
        />
      )}
    </>
  )
}
