import { ColumnDef } from '@tanstack/react-table'
import { useNavigate } from '@tanstack/react-router'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Checkbox } from '@/components/ui/checkbox'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { IconDots, IconEdit, IconTrash, IconEye, IconReceipt } from '@tabler/icons-react'
import { ExpenseItem } from '../data/schema'
import { useExpenses } from '../context/expenses-context'

const statusColors = {
  draft: 'secondary',
  submitted: 'default',
  approved: 'default',
  rejected: 'destructive',
} as const

const statusLabels = {
  draft: 'Draft',
  submitted: 'Submitted',
  approved: 'Approved',
  rejected: 'Rejected',
}

export const expenseItemsColumns: ColumnDef<ExpenseItem>[] = [
  {
    id: 'select',
    header: ({ table }) => (
      <Checkbox
        checked={
          table.getIsAllPageRowsSelected() ||
          (table.getIsSomePageRowsSelected() && 'indeterminate')
        }
        onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
        aria-label='Select all'
        className='translate-y-[2px]'
      />
    ),
    cell: ({ row }) => (
      <Checkbox
        checked={row.getIsSelected()}
        onCheckedChange={(value) => row.toggleSelected(!!value)}
        aria-label='Select row'
        className='translate-y-[2px]'
      />
    ),
    enableSorting: false,
    enableHiding: false,
  },
  {
    accessorKey: 'title',
    header: 'Title',
    cell: ({ row }) => (
      <div className='font-medium max-w-[200px] truncate'>
        {row.getValue('title')}
      </div>
    ),
  },
  {
    accessorKey: 'client',
    header: 'Client',
    cell: ({ row }) => (
      <div className='font-medium'>{row.getValue('client')}</div>
    ),
  },
  {
    accessorKey: 'startDate',
    header: 'Period',
    cell: ({ row }) => {
      const startDate = new Date(row.getValue('startDate'))
      const endDate = new Date(row.original.endDate)
      const isSameDay = startDate.toDateString() === endDate.toDateString()

      return (
        <div className='text-sm'>
          {isSameDay ? (
            startDate.toLocaleDateString('en-US', {
              month: 'short',
              day: 'numeric',
              year: 'numeric',
            })
          ) : (
            <>
              {startDate.toLocaleDateString('en-US', {
                month: 'short',
                day: 'numeric',
              })} - {endDate.toLocaleDateString('en-US', {
                month: 'short',
                day: 'numeric',
                year: 'numeric',
              })}
            </>
          )}
        </div>
      )
    },
  },
  {
    accessorKey: 'totalAmount',
    header: 'Total Amount',
    cell: ({ row }) => {
      const amount = parseFloat(row.getValue('totalAmount'))
      return (
        <div className='font-medium'>
          {amount.toLocaleString('de-CH', {
            style: 'currency',
            currency: 'CHF',
          })}
        </div>
      )
    },
  },
  {
    accessorKey: 'expenseCount',
    header: 'Expenses',
    cell: ({ row }) => {
      const count = row.getValue('expenseCount') as number
      return (
        <div className='flex items-center space-x-1'>
          <IconReceipt className='h-4 w-4 text-muted-foreground' />
          <span>{count}</span>
        </div>
      )
    },
  },
  {
    accessorKey: 'status',
    header: 'Status',
    cell: ({ row }) => {
      const status = row.getValue('status') as keyof typeof statusColors
      return (
        <Badge variant={statusColors[status]}>
          {statusLabels[status]}
        </Badge>
      )
    },
  },
  {
    id: 'actions',
    enableHiding: false,
    cell: ({ row }) => {
      const expenseItem = row.original
      // eslint-disable-next-line react-hooks/rules-of-hooks
      const { setOpen, setCurrentItem } = useExpenses()
      // eslint-disable-next-line react-hooks/rules-of-hooks
      const navigate = useNavigate()

      return (
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant='ghost' className='h-8 w-8 p-0'>
              <span className='sr-only'>Open menu</span>
              <IconDots className='h-4 w-4' />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align='end'>
            <DropdownMenuLabel>Actions</DropdownMenuLabel>
            <DropdownMenuSeparator />
            <DropdownMenuItem
              onClick={() => {
                navigate({ to: '/expenses/$itemId', params: { itemId: expenseItem.id } })
              }}
            >
              <IconEye className='mr-2 h-4 w-4' />
              View Details
            </DropdownMenuItem>
            <DropdownMenuItem
              onClick={() => {
                setCurrentItem(expenseItem)
                setOpen('update-item')
              }}
            >
              <IconEdit className='mr-2 h-4 w-4' />
              Edit
            </DropdownMenuItem>
            <DropdownMenuItem
              onClick={() => {
                setCurrentItem(expenseItem)
                setOpen('delete-item')
              }}
              className='text-destructive'
            >
              <IconTrash className='mr-2 h-4 w-4' />
              Delete
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      )
    },
  },
]
