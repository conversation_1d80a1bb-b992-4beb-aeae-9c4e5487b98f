import React, { useState } from 'react'
import useDialogState from '@/hooks/use-dialog-state'
import { ExpenseItem, Expense } from '../data/schema'

type ExpensesDialogType = 'create-item' | 'update-item' | 'delete-item' | 'create-expense' | 'update-expense' | 'delete-expense'

interface ExpensesContextType {
  open: ExpensesDialogType | null
  setOpen: (str: ExpensesDialogType | null) => void
  currentItem: ExpenseItem | null
  setCurrentItem: React.Dispatch<React.SetStateAction<ExpenseItem | null>>
  currentExpense: Expense | null
  setCurrentExpense: React.Dispatch<React.SetStateAction<Expense | null>>
  selectedItemId: string | null
  setSelectedItemId: React.Dispatch<React.SetStateAction<string | null>>
}

const ExpensesContext = React.createContext<ExpensesContextType | null>(null)

interface Props {
  children: React.ReactNode
}

export default function ExpensesProvider({ children }: Props) {
  const [open, setOpen] = useDialogState<ExpensesDialogType>(null)
  const [currentItem, setCurrentItem] = useState<ExpenseItem | null>(null)
  const [currentExpense, setCurrentExpense] = useState<Expense | null>(null)
  const [selectedItemId, setSelectedItemId] = useState<string | null>(null)

  return (
    <ExpensesContext value={{
      open,
      setOpen,
      currentItem,
      setCurrentItem,
      currentExpense,
      setCurrentExpense,
      selectedItemId,
      setSelectedItemId
    }}>
      {children}
    </ExpensesContext>
  )
}

// eslint-disable-next-line react-refresh/only-export-components
export const useExpenses = () => {
  const expensesContext = React.useContext(ExpensesContext)

  if (!expensesContext) {
    throw new Error('useExpenses has to be used within <ExpensesContext>')
  }

  return expensesContext
}
