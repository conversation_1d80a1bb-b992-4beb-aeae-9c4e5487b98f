import {
  IconBrowser<PERSON>heck,
  IconHelp,
  IconNotification,
  IconPalette,
  IconSettings,
  IconTool,
  IconUserCog,
  IconUsers,
  IconBuilding,
} from '@tabler/icons-react'
import { AudioWaveform, Command, GalleryVerticalEnd, Clock, Receipt, CalendarOff, PieChart } from 'lucide-react'
import type { SidebarData, NavGroup } from '../types'
import i18n from '@/lib/i18n'
import type { User, UserPermissions } from '@/types/user'

/**
 * Generate dynamic sidebar data based on user permissions
 */
export const generateSidebarData = (
  user: User | null,
  permissions: UserPermissions,
  isAuthenticated: boolean
): SidebarData => {
  // Get translation function
  const t = i18n.getFixedT(i18n.language)

  // Default user data (fallback)
  const userData = user ? {
    name: user.fullName,
    email: user.email,
    avatar: user.avatar || '/avatars/default.jpg',
  } : {
    name: 'Guest User',
    email: '<EMAIL>',
    avatar: '/avatars/default.jpg',
  }

  // Company/team data - could be dynamic based on user's company
  const teams = [
    {
      name: user?.consultant?.firstName ? `${user.consultant.firstName}'s Workspace` : 'ERP System',
      logo: Command,
      plan: user?.isAdmin ? 'Admin Access' : 'Consultant Access',
    },
    {
      name: 'FSLI Group',
      logo: GalleryVerticalEnd,
      plan: 'Enterprise',
    },
    {
      name: 'Consultant Portal',
      logo: AudioWaveform,
      plan: 'Professional',
    },
  ]

  // Generate navigation groups based on permissions
  const navGroups: NavGroup[] = []

  // General section - always visible for authenticated users
  if (isAuthenticated) {
    const generalItems = [
      {
        title: t('common:navigation.mydashboard'),
        url: '/' as const,
        icon: PieChart,
      },
      {
        title: t('common:navigation.timesheet'),
        url: '/timesheet' as const,
        icon: Clock,
      },
      {
        title: t('common:navigation.expenses'),
        url: '/expenses' as const,
        icon: Receipt,
      },
      {
        title: t('common:navigation.non-working-days'),
        url: '/non-working-days' as const,
        icon: CalendarOff,
      },
    ]

    // Removed: Trips, Apps, and Chats as they are not needed

    navGroups.push({
      title: 'General',
      items: generalItems,
    })
  }

  // Management section - only for users with management permissions
  if (isAuthenticated && (permissions.canViewUsers || permissions.canManageClients || user?.isAdmin)) {
    const managementItems = []

    if (permissions.canViewUsers) {
      managementItems.push({
        title: t('common:navigation.users'),
        url: '/users' as const,
        icon: IconUsers,
      })
    }

    if (permissions.canManageClients) {
      managementItems.push({
        title: t('common:navigation.clients'),
        url: '/clients' as const,
        icon: IconBuilding,
      })
    }

    if (managementItems.length > 0) {
      navGroups.push({
        title: 'Management',
        items: managementItems,
      })
    }
  }

  // Removed: Pages section as users don't need direct access to auth/error pages

  // Other section - always visible
  navGroups.push({
    title: 'Other',
    items: [
      {
        title: t('common:navigation.settings'),
        icon: IconSettings,
        items: [
          {
            title: 'Profile',
            url: '/settings',
            icon: IconUserCog,
          },
          {
            title: 'Account',
            url: '/settings/account',
            icon: IconTool,
          },
          {
            title: 'Appearance',
            url: '/settings/appearance',
            icon: IconPalette,
          },
          {
            title: 'Notifications',
            url: '/settings/notifications',
            icon: IconNotification,
          },
          {
            title: 'Display',
            url: '/settings/display',
            icon: IconBrowserCheck,
          },
        ],
      },
      {
        title: t('common:navigation.help-center'),
        url: '/help-center' as const,
        icon: IconHelp,
      },
    ],
  })

  return {
    user: userData,
    teams,
    navGroups,
  }
}
