import { useI18n } from '@/context/i18n-context'
import { Badge } from '@/components/ui/badge'

export function LanguageStatus() {
  const { language, availableLanguages } = useI18n()
  
  const currentLang = availableLanguages.find(lang => lang.code === language)
  
  return (
    <div className="fixed bottom-4 right-4 z-50">
      <Badge variant="outline" className="bg-background/80 backdrop-blur-sm">
        🌐 {currentLang?.nativeName || language.toUpperCase()}
      </Badge>
    </div>
  )
}
