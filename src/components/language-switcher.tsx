import { IconCheck, IconLanguage } from '@tabler/icons-react'
import { cn } from '@/lib/utils'
import { useI18n } from '@/context/i18n-context'
import { Button } from '@/components/ui/button'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'

export function LanguageSwitcher() {
  const { language, changeLanguage, availableLanguages, isLoading } = useI18n()

  return (
    <DropdownMenu modal={false}>
      <DropdownMenuTrigger asChild>
        <Button 
          variant='ghost' 
          size='icon' 
          className='scale-95 rounded-full'
          disabled={isLoading}
        >
          <IconLanguage className='size-[1.2rem]' />
          <span className='sr-only'>Change language</span>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align='end'>
        {availableLanguages.map((lang) => (
          <DropdownMenuItem 
            key={lang.code}
            onClick={() => changeLanguage(lang.code)}
            disabled={isLoading}
          >
            {lang.nativeName}
            <IconCheck
              size={14}
              className={cn('ml-auto', language !== lang.code && 'hidden')}
            />
          </DropdownMenuItem>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  )
}
