import React from 'react'
import { useUser } from '@/context/user-context'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Button } from '@/components/ui/button'
import { Link } from '@tanstack/react-router'
import { IconLock, IconUserOff } from '@tabler/icons-react'
import { Skeleton } from '@/components/ui/skeleton'
import type { UserPermissions } from '@/types/user'

interface RouteGuardProps {
  children: React.ReactNode
  requiresAuth?: boolean
  requiresAdmin?: boolean
  requiresPermission?: keyof UserPermissions
  fallback?: React.ReactNode
}

export function RouteGuard({
  children,
  requiresAuth = false,
  requiresAdmin = false,
  requiresPermission,
  fallback,
}: RouteGuardProps) {
  const { user, permissions, isAuthenticated, isLoading } = useUser()

  // Show loading state while checking authentication
  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="space-y-4 w-full max-w-md">
          <Skeleton className="h-8 w-full" />
          <Skeleton className="h-4 w-3/4" />
          <Skeleton className="h-4 w-1/2" />
        </div>
      </div>
    )
  }

  // Check authentication requirement
  if (requiresAuth && !isAuthenticated) {
    return (
      fallback || (
        <div className="flex items-center justify-center min-h-[400px]">
          <Alert className="max-w-md">
            <IconLock className="h-4 w-4" />
            <AlertDescription className="space-y-4">
              <div>
                <p className="font-medium">Authentication Required</p>
                <p className="text-sm text-muted-foreground">
                  You need to be logged in to access this page.
                </p>
              </div>
              <Button asChild>
                <Link to="/sign-in-2">Sign In</Link>
              </Button>
            </AlertDescription>
          </Alert>
        </div>
      )
    )
  }

  // Check admin requirement
  if (requiresAdmin && !user?.isAdmin) {
    return (
      fallback || (
        <div className="flex items-center justify-center min-h-[400px]">
          <Alert variant="destructive" className="max-w-md">
            <IconUserOff className="h-4 w-4" />
            <AlertDescription className="space-y-4">
              <div>
                <p className="font-medium">Admin Access Required</p>
                <p className="text-sm">
                  You don't have permission to access this page. Admin privileges are required.
                </p>
              </div>
              <Button variant="outline" asChild>
                <Link to="/">Go to Dashboard</Link>
              </Button>
            </AlertDescription>
          </Alert>
        </div>
      )
    )
  }

  // Check specific permission requirement
  if (requiresPermission && !permissions[requiresPermission]) {
    return (
      fallback || (
        <div className="flex items-center justify-center min-h-[400px]">
          <Alert variant="destructive" className="max-w-md">
            <IconUserOff className="h-4 w-4" />
            <AlertDescription className="space-y-4">
              <div>
                <p className="font-medium">Insufficient Permissions</p>
                <p className="text-sm">
                  You don't have the required permissions to access this page.
                </p>
              </div>
              <Button variant="outline" asChild>
                <Link to="/">Go to Dashboard</Link>
              </Button>
            </AlertDescription>
          </Alert>
        </div>
      )
    )
  }

  // All checks passed, render children
  return <>{children}</>
}

// Convenience components for common use cases
export function AdminRoute({ children, fallback }: { children: React.ReactNode; fallback?: React.ReactNode }) {
  return (
    <RouteGuard requiresAuth requiresAdmin fallback={fallback}>
      {children}
    </RouteGuard>
  )
}

export function AuthenticatedRoute({ children, fallback }: { children: React.ReactNode; fallback?: React.ReactNode }) {
  return (
    <RouteGuard requiresAuth fallback={fallback}>
      {children}
    </RouteGuard>
  )
}

export function PermissionRoute({
  children,
  permission,
  fallback,
}: {
  children: React.ReactNode
  permission: keyof UserPermissions
  fallback?: React.ReactNode
}) {
  return (
    <RouteGuard requiresAuth requiresPermission={permission} fallback={fallback}>
      {children}
    </RouteGuard>
  )
}
