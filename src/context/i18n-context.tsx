import React, { createContext, useContext, useEffect, useState } from 'react'
import { useTranslation } from 'react-i18next'
import i18n from '@/lib/i18n'

interface I18nContextType {
  language: string
  changeLanguage: (lng: string) => Promise<void>
  isLoading: boolean
  availableLanguages: Array<{
    code: string
    name: string
    nativeName: string
  }>
}

const I18nContext = createContext<I18nContextType | undefined>(undefined)

const availableLanguages = [
  { code: 'fr', name: 'French', nativeName: 'Français' },
  { code: 'en', name: 'English', nativeName: 'English' },
]

interface I18nProviderProps {
  children: React.ReactNode
}

export function I18nProvider({ children }: I18nProviderProps) {
  const { i18n: i18nInstance } = useTranslation()
  const [isLoading, setIsLoading] = useState(false)
  const [language, setLanguage] = useState(i18nInstance.language || 'fr')

  const changeLanguage = async (lng: string) => {
    setIsLoading(true)
    try {
      await i18nInstance.changeLanguage(lng)
      setLanguage(lng)
      // Store the language preference
      localStorage.setItem('i18nextLng', lng)
      // Update document language attribute
      document.documentElement.lang = lng
    } catch (error) {
      console.error('Failed to change language:', error)
    } finally {
      setIsLoading(false)
    }
  }

  useEffect(() => {
    // Listen for language changes
    const handleLanguageChange = (lng: string) => {
      setLanguage(lng)
      document.documentElement.lang = lng
    }

    i18nInstance.on('languageChanged', handleLanguageChange)

    // Set initial language
    document.documentElement.lang = i18nInstance.language

    return () => {
      i18nInstance.off('languageChanged', handleLanguageChange)
    }
  }, [i18nInstance])

  const value: I18nContextType = {
    language,
    changeLanguage,
    isLoading,
    availableLanguages,
  }

  return <I18nContext.Provider value={value}>{children}</I18nContext.Provider>
}

export function useI18n() {
  const context = useContext(I18nContext)
  if (context === undefined) {
    throw new Error('useI18n must be used within an I18nProvider')
  }
  return context
}

// Custom hook for translations with namespace support
export function useTranslations(namespace?: string) {
  const { t, i18n } = useTranslation(namespace)
  const { language, changeLanguage, isLoading } = useI18n()

  return {
    t,
    language,
    changeLanguage,
    isLoading,
    i18n,
  }
}

// Helper hook for common translations
export function useCommonTranslations() {
  return useTranslations('common')
}

// Helper hook for auth translations
export function useAuthTranslations() {
  return useTranslations('auth')
}

// Helper hook for error translations
export function useErrorTranslations() {
  return useTranslations('errors')
}

export default I18nContext
