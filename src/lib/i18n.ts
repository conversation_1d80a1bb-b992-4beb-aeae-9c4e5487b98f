import i18n from 'i18next'
import { initReactI18next } from 'react-i18next'
import LanguageDetector from 'i18next-browser-languagedetector'
import Backend from 'i18next-http-backend'

i18n
  .use(Backend)
  .use(LanguageDetector)
  .use(initReactI18next)
  .init({
    lng: 'fr', // Default language is French
    fallbackLng: 'fr', // Fallback to French if translation is missing
    debug: import.meta.env.DEV,

    interpolation: {
      escapeValue: false, // React already does escaping
    },

    detection: {
      order: ['localStorage', 'navigator', 'htmlTag'],
      lookupLocalStorage: 'i18nextLng',
      caches: ['localStorage'],
    },

    backend: {
      loadPath: '/locales/{{lng}}/{{ns}}.json',
    },

    ns: [
      'common',
      'auth',
      'dashboard',
      'mydashboard',
      'expenses',
      'timesheet',
      'non-working-days',
      'clients',
      'consultants',
      'users',
      'settings',
      'errors',
    ],
    defaultNS: 'common',

    react: {
      useSuspense: false,
    },
  })

export default i18n
