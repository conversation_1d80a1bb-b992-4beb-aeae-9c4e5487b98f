import { apiClient } from '@/lib/api'
import type {
  LeaveRequest,
  LeaveBalance,
  CreateLeaveRequestRequest,
  UpdateLeaveRequestRequest,
} from '@/features/non-working-days/data/schema'

/**
 * Get all leave requests (admin only)
 * @returns Promise<LeaveRequest[]>
 */
export const getAllLeaveRequests = async (): Promise<LeaveRequest[]> => {
  const response = await apiClient.get<LeaveRequest[]>('/v1/leaves')
  return response.data
}

/**
 * Create a new leave request
 * @param leaveData - Leave request data
 * @returns Promise<LeaveRequest>
 */
export const createLeaveRequest = async (leaveData: CreateLeaveRequestRequest): Promise<LeaveRequest> => {
  const response = await apiClient.post<LeaveRequest>('/v1/leaves', leaveData)
  return response.data
}

/**
 * Get all pending leave requests (admin only)
 * @returns Promise<LeaveRequest[]>
 */
export const getPendingLeaveRequests = async (): Promise<LeaveRequest[]> => {
  const response = await apiClient.get<LeaveRequest[]>('/v1/leaves/pending')
  return response.data
}

/**
 * Get a specific leave request by ID
 * @param id - Leave request ID
 * @returns Promise<LeaveRequest>
 */
export const getLeaveRequestById = async (id: number): Promise<LeaveRequest> => {
  const response = await apiClient.get<LeaveRequest>(`/v1/leaves/${id}`)
  return response.data
}

/**
 * Update an existing leave request
 * @param id - Leave request ID
 * @param leaveData - Updated leave request data
 * @returns Promise<LeaveRequest>
 */
export const updateLeaveRequest = async (id: number, leaveData: UpdateLeaveRequestRequest): Promise<LeaveRequest> => {
  const response = await apiClient.put<LeaveRequest>(`/v1/leaves/${id}`, leaveData)
  return response.data
}

/**
 * Delete a leave request
 * @param id - Leave request ID
 * @returns Promise<void>
 */
export const deleteLeaveRequest = async (id: number): Promise<void> => {
  await apiClient.delete(`/v1/leaves/${id}`)
}

/**
 * Get leave requests for a specific consultant
 * @param consultantId - Consultant ID
 * @returns Promise<LeaveRequest[]>
 */
export const getConsultantLeaveRequests = async (consultantId: number): Promise<LeaveRequest[]> => {
  const response = await apiClient.get<LeaveRequest[]>(`/v1/leaves/consultant/${consultantId}`)
  return response.data
}

/**
 * Approve a leave request (admin only)
 * @param id - Leave request ID
 * @returns Promise<LeaveRequest>
 */
export const approveLeaveRequest = async (id: number): Promise<LeaveRequest> => {
  const response = await apiClient.put<LeaveRequest>(`/v1/leaves/${id}/approve`)
  return response.data
}

/**
 * Reject a leave request (admin only)
 * @param id - Leave request ID
 * @param rejectionReason - Optional reason for rejection
 * @returns Promise<LeaveRequest>
 */
export const rejectLeaveRequest = async (id: number, rejectionReason?: string): Promise<LeaveRequest> => {
  const response = await apiClient.put<LeaveRequest>(`/v1/leaves/${id}/reject`, {
    rejectionReason
  })
  return response.data
}

/**
 * Get leave balance for a consultant
 * @param consultantId - Consultant ID
 * @returns Promise<LeaveBalance>
 */
export const getLeaveBalance = async (consultantId: number): Promise<LeaveBalance> => {
  const response = await apiClient.get<LeaveBalance>(`/v1/leaves/balance/${consultantId}`)
  return response.data
}

/**
 * Get current user's leave requests
 * @returns Promise<LeaveRequest[]>
 */
export const getMyLeaveRequests = async (): Promise<LeaveRequest[]> => {
  // Get current user info from localStorage
  const consultantInfo = localStorage.getItem('consultantInfo')
  if (!consultantInfo) {
    throw new Error('User information not found')
  }
  
  const { id } = JSON.parse(consultantInfo)
  return getConsultantLeaveRequests(id)
}

/**
 * Get current user's leave balance
 * @returns Promise<LeaveBalance>
 */
export const getMyLeaveBalance = async (): Promise<LeaveBalance> => {
  // Get current user info from localStorage
  const consultantInfo = localStorage.getItem('consultantInfo')
  if (!consultantInfo) {
    throw new Error('User information not found')
  }
  
  const { id } = JSON.parse(consultantInfo)
  return getLeaveBalance(id)
}
