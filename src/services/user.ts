import { apiClient } from '@/lib/api'
import { jwtDecode } from 'jwt-decode'
import type { Consultant, User, JwtPayload } from '@/types/user'
import { getInitials, getFullName, isAdmin, isConsultant } from '@/types/user'

/**
 * Get current consultant information from API
 */
export const getCurrentConsultant = async (): Promise<Consultant> => {
  const response = await apiClient.get<Consultant>('/v1/consultants/me')
  return response.data
}

/**
 * Create User object from consultant data and JWT token
 */
export const createUserFromConsultant = (consultant: Consultant, token: string): User => {
  const decoded = jwtDecode<JwtPayload>(token)
  
  const user: User = {
    id: consultant.id,
    consultant,
    roles: decoded.roles || [],
    email: consultant.email,
    isAdmin: decoded.roles?.includes('ROLE_ADMIN') ?? false,
    isConsultant: decoded.roles?.includes('ROLE_USER') ?? false,
    fullName: getFull<PERSON><PERSON>(consultant.firstName, consultant.lastName),
    initials: getInitials(consultant.firstName, consultant.lastName),
  }

  return user
}

/**
 * Get current user with full profile data
 */
export const getCurrentUser = async (): Promise<User> => {
  const token = localStorage.getItem('token')
  
  if (!token) {
    throw new Error('No authentication token found')
  }

  try {
    // Verify token is still valid
    const decoded = jwtDecode<JwtPayload>(token)
    const now = Date.now() / 1000
    
    if (decoded.exp < now) {
      throw new Error('Token has expired')
    }

    // Fetch consultant data
    const consultant = await getCurrentConsultant()
    
    // Create user object
    const user = createUserFromConsultant(consultant, token)
    
    // Store user data in localStorage for quick access
    localStorage.setItem('currentUser', JSON.stringify(user))
    
    return user
  } catch (error) {
    // Clear invalid token and user data
    localStorage.removeItem('token')
    localStorage.removeItem('roles')
    localStorage.removeItem('consultantInfo')
    localStorage.removeItem('currentUser')
    throw error
  }
}

/**
 * Get user from localStorage (for quick access)
 */
export const getCachedUser = (): User | null => {
  try {
    const userStr = localStorage.getItem('currentUser')
    if (!userStr) return null
    
    const user = JSON.parse(userStr) as User
    
    // Verify token is still valid
    const token = localStorage.getItem('token')
    if (!token) return null
    
    const decoded = jwtDecode<JwtPayload>(token)
    const now = Date.now() / 1000
    
    if (decoded.exp < now) {
      // Token expired, clear cache
      localStorage.removeItem('currentUser')
      return null
    }
    
    return user
  } catch (error) {
    console.error('Error getting cached user:', error)
    return null
  }
}

/**
 * Update user profile
 */
export const updateUserProfile = async (id: number, data: Partial<Consultant>): Promise<Consultant> => {
  const response = await apiClient.put<Consultant>(`/v1/consultants/${id}`, data)
  
  // Update cached user data
  const currentUser = getCachedUser()
  if (currentUser && currentUser.id === id) {
    const updatedUser: User = {
      ...currentUser,
      consultant: { ...currentUser.consultant, ...response.data },
      fullName: getFullName(response.data.firstName, response.data.lastName),
      initials: getInitials(response.data.firstName, response.data.lastName),
    }
    localStorage.setItem('currentUser', JSON.stringify(updatedUser))
  }
  
  return response.data
}

/**
 * Clear user data (for logout)
 */
export const clearUserData = (): void => {
  localStorage.removeItem('token')
  localStorage.removeItem('roles')
  localStorage.removeItem('consultantInfo')
  localStorage.removeItem('currentUser')
}

/**
 * Check if user has specific role
 */
export const userHasRole = (role: string): boolean => {
  const user = getCachedUser()
  return user?.roles.includes(role) ?? false
}

/**
 * Check if current user is admin
 */
export const isCurrentUserAdmin = (): boolean => {
  const user = getCachedUser()
  return isAdmin(user)
}

/**
 * Check if current user is consultant
 */
export const isCurrentUserConsultant = (): boolean => {
  const user = getCachedUser()
  return isConsultant(user)
}

/**
 * Get user's display name
 */
export const getUserDisplayName = (): string => {
  const user = getCachedUser()
  return user?.fullName ?? 'Unknown User'
}

/**
 * Get user's initials for avatar
 */
export const getUserInitials = (): string => {
  const user = getCachedUser()
  return user?.initials ?? 'U'
}

/**
 * Refresh user data from API
 */
export const refreshUserData = async (): Promise<User> => {
  const user = await getCurrentUser()
  return user
}
