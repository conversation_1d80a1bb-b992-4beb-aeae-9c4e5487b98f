import { apiClient } from '@/lib/api'
import type {
  WorkTimePeriod,
  ConsultantInfo,
  CreateWorkTimePeriodRequest,
  UpdateWorkTimePeriodRequest,
  WorkTimeStatistics,
  Client,
  TimeEntry // Legacy support
} from '@/features/timesheet/types'


// Use real API endpoints only

// API-compliant service functions

/**
 * Get all work time periods (admin only)
 */
export const getAllWorkTimePeriods = async (): Promise<WorkTimePeriod[]> => {
  const response = await apiClient.get<WorkTimePeriod[]>('/v1/work-time')
  return response.data
}

/**
 * Create a new work time period with activities
 */
export const createWorkTimePeriod = async (data: CreateWorkTimePeriodRequest): Promise<WorkTimePeriod> => {
  const response = await apiClient.post<WorkTimePeriod>('/v1/work-time', data)
  return response.data
}

/**
 * Get a specific work time period by ID
 */
export const getWorkTimePeriod = async (id: number): Promise<WorkTimePeriod> => {
  const response = await apiClient.get<WorkTimePeriod>(`/v1/work-time/${id}`)
  return response.data
}

/**
 * Update an existing work time period
 */
export const updateWorkTimePeriod = async (data: UpdateWorkTimePeriodRequest): Promise<WorkTimePeriod> => {
  const { id, ...updateData } = data
  const response = await apiClient.put<WorkTimePeriod>(`/v1/work-time/${id}`, updateData)
  return response.data
}

/**
 * Delete a work time period
 */
export const deleteWorkTimePeriod = async (id: number): Promise<void> => {
  await apiClient.delete(`/v1/work-time/${id}`)
}

/**
 * Get work time periods for a specific consultant
 */
export const getConsultantWorkTimePeriods = async (consultantId: number): Promise<WorkTimePeriod[]> => {
  const response = await apiClient.get<WorkTimePeriod[]>(`/v1/work-time/consultant/${consultantId}`)
  return response.data
}

/**
 * Get work time periods for a consultant within a date range
 */
export const getConsultantWorkTimePeriodsInRange = async (
  consultantId: number,
  startDate: string,
  endDate: string
): Promise<WorkTimePeriod[]> => {
  const response = await apiClient.get<WorkTimePeriod[]>(
    `/v1/work-time/consultant/${consultantId}?start=${startDate}&end=${endDate}`
  )
  return response.data
}

/**
 * Get work time statistics for a consultant
 */
export const getWorkTimeStatistics = async (consultantId: number): Promise<WorkTimeStatistics> => {
  const response = await apiClient.get<WorkTimeStatistics>(`/v1/work-time/statistics/${consultantId}`)
  return response.data
}

/**
 * Validate a work time period (admin only)
 */
export const validateWorkTimePeriod = async (id: number): Promise<WorkTimePeriod> => {
  const response = await apiClient.post<WorkTimePeriod>(`/v1/work-time/${id}/validate`)
  return response.data
}

/**
 * Get all clients for dropdown/selection purposes
 */
export const getClients = async (): Promise<Client[]> => {
  const response = await apiClient.get<Client[]>('/v1/clients')
  return response.data
}

/**
 * Get consultant information from localStorage
 */
export const getConsultantInfo = (): ConsultantInfo | null => {
  try {
    const consultantInfoStr = localStorage.getItem('consultantInfo')
    if (!consultantInfoStr) {
      return null
    }
    return JSON.parse(consultantInfoStr) as ConsultantInfo
  } catch (error) {
    console.error('Error parsing consultant info:', error)
    return null
  }
}

/**
 * Calculate statistics from work time periods
 */
export const calculateWorkTimeStats = (periods: WorkTimePeriod[]): WorkTimeStatistics => {
  const totalHours = periods.reduce((sum, period) => sum + period.totalHours, 0)
  const totalRemoteHours = periods.reduce((sum, period) => {
    return sum + (period.totalHours * period.remoteWorkPercentage / 100)
  }, 0)
  const totalOnsiteHours = totalHours - totalRemoteHours

  return {
    totalHours,
    remoteHours: totalRemoteHours,
    onsiteHours: totalOnsiteHours,
    totalPeriods: periods.length,
    remotePercentage: totalHours > 0 ? (totalRemoteHours / totalHours) * 100 : 0,
    onsitePercentage: totalHours > 0 ? (totalOnsiteHours / totalHours) * 100 : 0,
    averageHoursPerPeriod: periods.length > 0 ? totalHours / periods.length : 0,
  }
}

/**
 * Helper function to format period identifier
 */
export const formatPeriodIdentifier = (startDate: string, endDate: string): string => {
  return `${startDate} to ${endDate}`
}

/**
 * Helper function to calculate duration in days
 */
export const calculateDurationInDays = (startDate: string, endDate: string): number => {
  const start = new Date(startDate)
  const end = new Date(endDate)
  const diffTime = Math.abs(end.getTime() - start.getTime())
  return Math.ceil(diffTime / (1000 * 60 * 60 * 24)) + 1 // +1 to include both start and end dates
}

// Legacy functions for backward compatibility during migration

export interface CreateTimeEntryData {
  date: string
  activity: string
  comment?: string
  hours: number
  remoteWork: boolean
  consultantId: string
}

export interface UpdateTimeEntryData extends Partial<CreateTimeEntryData> {
  id: string
}

/**
 * Legacy: Add a new time tracking entry (deprecated - use createWorkTimePeriod instead)
 */
export const addTimeEntry = async (data: CreateTimeEntryData): Promise<TimeEntry> => {
  // Convert legacy format to new API format
  const periodData: CreateWorkTimePeriodRequest = {
    consultantId: parseInt(data.consultantId),
    startDate: data.date,
    endDate: data.date,
    totalHours: data.hours,
    remoteWorkPercentage: data.remoteWork ? 100 : 0,
    notes: data.comment,
    activities: [{
      activityName: data.activity,
      hours: data.hours,
      description: data.comment,
      isBillable: true,
    }]
  }

  const period = await createWorkTimePeriod(periodData)

  // Convert back to legacy format for compatibility
  return {
    id: period.id.toString(),
    date: data.date,
    hours: data.hours,
    location: data.remoteWork ? 'home' : 'office',
    client: period.activities[0]?.client?.name || '',
    description: data.activity,
    consultantId: data.consultantId,
  }
}

/**
 * Legacy: Get all time entries for a specific consultant (deprecated)
 */
export const getConsultantTimeEntries = async (consultantId: string): Promise<TimeEntry[]> => {
  const periods = await getConsultantWorkTimePeriods(parseInt(consultantId))

  // Convert periods to legacy time entries format
  const entries: TimeEntry[] = []
  periods.forEach(period => {
    period.activities.forEach(activity => {
      entries.push({
        id: `${period.id}-${activity.id}`,
        date: period.startDate,
        hours: activity.hours,
        location: period.remoteWorkPercentage > 50 ? 'home' : 'office',
        client: activity.client?.name || '',
        description: activity.activityName,
        consultantId: consultantId,
      })
    })
  })

  return entries
}

/**
 * Legacy: Get time entries for a consultant within a date range (deprecated)
 */
export const getConsultantTimeEntriesInRange = async (
  consultantId: string,
  startDate: string,
  endDate: string
): Promise<TimeEntry[]> => {
  const periods = await getConsultantWorkTimePeriodsInRange(parseInt(consultantId), startDate, endDate)

  // Convert periods to legacy time entries format
  const entries: TimeEntry[] = []
  periods.forEach(period => {
    period.activities.forEach(activity => {
      entries.push({
        id: `${period.id}-${activity.id}`,
        date: period.startDate,
        hours: activity.hours,
        location: period.remoteWorkPercentage > 50 ? 'home' : 'office',
        client: activity.client?.name || '',
        description: activity.activityName,
        consultantId: consultantId,
      })
    })
  })

  return entries
}

/**
 * Legacy: Update an existing time entry (deprecated)
 */
export const updateTimeEntry = async (_data: UpdateTimeEntryData): Promise<TimeEntry> => {
  // This is complex to implement with the new API structure
  // For now, throw an error suggesting to use the new period-based approach
  throw new Error('updateTimeEntry is deprecated. Please use updateWorkTimePeriod instead.')
}

/**
 * Legacy: Delete a time entry (deprecated)
 */
export const deleteTimeEntry = async (id: string): Promise<void> => {
  // Extract period ID from legacy format
  const periodId = parseInt(id.split('-')[0])
  await deleteWorkTimePeriod(periodId)
}
