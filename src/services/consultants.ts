import { apiClient } from '@/lib/api'
import type {
  Consultant,
  CreateConsultantRequest,
  UpdateConsultantRequest,
} from '@/features/consultants/data/schema'

/**
 * Get all consultants (admin only)
 * @returns Promise<Consultant[]>
 */
export const getAllConsultants = async (): Promise<Consultant[]> => {
  const response = await apiClient.get<Consultant[]>('/v1/consultants')
  return response.data
}

/**
 * Get a specific consultant by ID
 * @param id - Consultant ID
 * @returns Promise<Consultant>
 */
export const getConsultantById = async (id: number): Promise<Consultant> => {
  const response = await apiClient.get<Consultant>(`/v1/consultants/${id}`)
  return response.data
}

/**
 * Get current consultant's profile
 * @returns Promise<Consultant>
 */
export const getCurrentConsultant = async (): Promise<Consultant> => {
  const response = await apiClient.get<Consultant>('/v1/consultants/me')
  return response.data
}

/**
 * Create a new consultant (admin only)
 * @param consultantData - Consultant data to create
 * @returns Promise<Consultant>
 */
export const createConsultant = async (consultantData: CreateConsultantRequest): Promise<Consultant> => {
  const response = await apiClient.post<Consultant>('/v1/consultants', consultantData)
  return response.data
}

/**
 * Update an existing consultant
 * @param id - Consultant ID
 * @param consultantData - Updated consultant data
 * @returns Promise<Consultant>
 */
export const updateConsultant = async (id: number, consultantData: UpdateConsultantRequest): Promise<Consultant> => {
  const response = await apiClient.put<Consultant>(`/v1/consultants/${id}`, consultantData)
  return response.data
}

/**
 * Delete a consultant (admin only)
 * @param id - Consultant ID
 * @returns Promise<void>
 */
export const deleteConsultant = async (id: number): Promise<void> => {
  await apiClient.delete(`/v1/consultants/${id}`)
}

/**
 * Get active consultants only (for dropdowns/selections)
 * @returns Promise<Consultant[]>
 */
export const getActiveConsultants = async (): Promise<Consultant[]> => {
  const allConsultants = await getAllConsultants()
  return allConsultants.filter(consultant => consultant.isActive)
}

/**
 * Search consultants by name or email
 * @param query - Search query
 * @returns Promise<Consultant[]>
 */
export const searchConsultants = async (query: string): Promise<Consultant[]> => {
  const allConsultants = await getAllConsultants()
  const lowercaseQuery = query.toLowerCase()

  return allConsultants.filter(consultant =>
    consultant.firstName.toLowerCase().includes(lowercaseQuery) ||
    consultant.lastName.toLowerCase().includes(lowercaseQuery) ||
    consultant.email.toLowerCase().includes(lowercaseQuery)
  )
}

/**
 * Toggle consultant active status
 * @param id - Consultant ID
 * @param isActive - New active status
 * @returns Promise<Consultant>
 */
export const toggleConsultantStatus = async (id: number, isActive: boolean): Promise<Consultant> => {
  const response = await apiClient.put<Consultant>(`/v1/consultants/${id}`, { isActive })
  return response.data
}

// Error handling utilities
export const handleConsultantError = (error: any): string => {
  if (error.response?.data?.message) {
    return error.response.data.message
  }

  if (error.response?.status === 404) {
    return 'Consultant not found'
  }

  if (error.response?.status === 403) {
    return 'You do not have permission to perform this action'
  }

  if (error.response?.status === 409) {
    return 'A consultant with this email already exists'
  }

  if (error.response?.status === 422) {
    return 'Invalid consultant data provided'
  }

  if (error.message) {
    return error.message
  }

  return 'An unexpected error occurred'
}

// Type guards and validation
export const isValidEmail = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  return emailRegex.test(email)
}

export const isValidPhoneNumber = (phone: string): boolean => {
  // Basic phone number validation - adjust regex as needed
  const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/
  return phoneRegex.test(phone.replace(/[\s\-\(\)]/g, ''))
}

export const formatPhoneNumber = (phone: string): string => {
  // Remove all non-digit characters except +
  return phone.replace(/[^\d\+]/g, '')
}

export const formatCurrency = (amount: number, currency = 'EUR'): string => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency,
  }).format(amount)
}

// Date formatting utilities
export const formatDate = (dateString: string): string => {
  if (!dateString) return ''
  return new Date(dateString).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
  })
}

export const isDateInFuture = (dateString: string): boolean => {
  if (!dateString) return false
  return new Date(dateString) > new Date()
}

export const isDateInPast = (dateString: string): boolean => {
  if (!dateString) return false
  return new Date(dateString) < new Date()
}
