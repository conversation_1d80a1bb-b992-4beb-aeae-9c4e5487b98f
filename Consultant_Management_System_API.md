


Consultant Management System API






{"spec":{"openapi":"3.0.0","info":{"title":"Consultant Management System API","description":"A comprehensive RESTful API for efficient consultant management, time tracking, expense reporting, and leave management","contact":{"name":"API Support","url":"https://consultantmanagementsystem.com/support","email":"<EMAIL>"},"version":"1.0.0"},"paths":{"/api/v1/auth/login":{"post":{"tags":["Authentication"],"summary":"Login to get a JWT token","operationId":"post\_api\_login","requestBody":{"description":"Login credentials","required":true,"content":{"application/json":{"schema":{"$ref":"#/components/schemas/LoginRequest"}}}},"responses":{"200":{"description":"Returns JWT token and user information","content":{"application/json":{"schema":{"$ref":"#/components/schemas/LoginResponse"}}}},"401":{"description":"Invalid credentials","content":{"application/json":{"schema":{"properties":{"error":{"type":"string","example":"Invalid credentials"}},"type":"object"}}}}}}},"/api/v1/auth/register":{"post":{"tags":["Authentication"],"summary":"Register a new user","operationId":"post\_api\_register","requestBody":{"description":"Registration data","required":true,"content":{"application/json":{"schema":{"$ref":"#/components/schemas/RegistrationRequest"}}}},"responses":{"201":{"description":"User registered successfully","content":{"application/json":{"schema":{"$ref":"#/components/schemas/RegistrationResponse"}}}},"400":{"description":"Invalid input data","content":{"application/json":{"schema":{"properties":{"error":{"type":"string","example":"Email already in use"}},"type":"object"}}}}}}},"/api/v1/clients":{"get":{"tags":["Clients"],"summary":"Get all clients","operationId":"get\_app\_client\_index","parameters":[{"name":"active","in":"query","description":"Filter by active status","required":false,"schema":{"type":"boolean"}}],"responses":{"200":{"description":"List of clients","content":{"application/json":{"schema":{"type":"array","items":{"properties":{"id":{"type":"integer","example":1},"name":{"type":"string","example":"Acme Corporation"},"code":{"type":"string","example":"ACME"},"isActive":{"type":"boolean","example":true},"address":{"type":"string","example":"123 Main St","nullable":true},"phone":{"type":"string","example":"+1234567890","nullable":true},"email":{"type":"string","example":"<EMAIL>","nullable":true},"website":{"type":"string","example":"https://acme.com","nullable":true}},"type":"object"}}}}}}},"post":{"tags":["Clients"],"summary":"Create a new client (admin only)","operationId":"post\_app\_client\_create","requestBody":{"description":"Client data","required":true,"content":{"application/json":{"schema":{"required":["name","code"],"properties":{"name":{"type":"string","example":"Acme Corporation"},"code":{"type":"string","example":"ACME"},"address":{"type":"string","example":"123 Main St","nullable":true},"phone":{"type":"string","example":"+1234567890","nullable":true},"email":{"type":"string","example":"<EMAIL>","nullable":true},"website":{"type":"string","example":"https://acme.com","nullable":true},"isActive":{"type":"boolean","example":true}},"type":"object"}}}},"responses":{"201":{"description":"Client created successfully"},"400":{"description":"Invalid data"}}}},"/api/v1/clients/{id}":{"get":{"tags":["Clients"],"summary":"Get a specific client","operationId":"get\_app\_client\_show","parameters":[{"name":"id","in":"path","required":true,"schema":{"type":"string"}}],"responses":{"200":{"description":"Client details","content":{"application/json":{"schema":{"properties":{"id":{"type":"integer","example":1},"name":{"type":"string","example":"Acme Corporation"},"code":{"type":"string","example":"ACME"},"isActive":{"type":"boolean","example":true},"address":{"type":"string","example":"123 Main St","nullable":true},"phone":{"type":"string","example":"+1234567890","nullable":true},"email":{"type":"string","example":"<EMAIL>","nullable":true},"website":{"type":"string","example":"https://acme.com","nullable":true}},"type":"object"}}}},"404":{"description":"Client not found"}}},"put":{"tags":["Clients"],"summary":"Update a client (admin only)","operationId":"put\_app\_client\_update","parameters":[{"name":"id","in":"path","description":"Client ID","required":true,"schema":{"type":"integer"}}],"requestBody":{"description":"Client data for updating an existing client. Only administrators can update client information.","required":true,"content":{"application/json":{"schema":{"properties":{"name":{"type":"string","example":"Updated Acme Corporation"},"code":{"type":"string","example":"ACME\_NEW"},"address":{"type":"string","example":"456 Updated St","nullable":true},"phone":{"type":"string","example":"+1987654321","nullable":true},"email":{"type":"string","example":"<EMAIL>","nullable":true},"website":{"type":"string","example":"https://updated.acme.com","nullable":true},"isActive":{"type":"boolean","example":false}},"type":"object"}}}},"responses":{"200":{"description":"Client updated successfully","content":{"application/json":{"schema":{"properties":{"id":{"type":"integer","example":1},"name":{"type":"string","example":"Updated Acme Corporation"},"code":{"type":"string","example":"ACME\_NEW"},"isActive":{"type":"boolean","example":false},"address":{"type":"string","example":"456 Updated St","nullable":true},"phone":{"type":"string","example":"+1987654321","nullable":true},"email":{"type":"string","example":"<EMAIL>","nullable":true},"website":{"type":"string","example":"https://updated.acme.com","nullable":true},"success":{"type":"boolean","example":true}},"type":"object"}}}},"400":{"description":"Invalid data provided"},"401":{"description":"Authentication required"},"403":{"description":"Access denied - Admin role required"},"404":{"description":"Client not found"}}},"delete":{"tags":["Clients"],"summary":"Delete a client (admin only)","operationId":"delete\_app\_client\_delete","parameters":[{"name":"id","in":"path","description":"Client ID","required":true,"schema":{"type":"integer"}}],"responses":{"200":{"description":"Client deleted successfully","content":{"application/json":{"schema":{"properties":{"id":{"type":"integer","example":1},"name":{"type":"string","example":"Acme Corporation"},"message":{"type":"string","example":"Client successfully deleted"},"success":{"type":"boolean","example":true}},"type":"object"}}}},"400":{"description":"Cannot delete client with associated data"},"401":{"description":"Authentication required"},"403":{"description":"Access denied - Admin role required"},"404":{"description":"Client not found"}}}},"/api/v1/company":{"get":{"tags":["Company"],"summary":"Get company information","operationId":"get\_app\_company\_show","responses":{"200":{"description":"Returns company information","content":{"application/json":{"schema":{"properties":{"id":{"type":"integer","example":1},"name":{"type":"string","example":"TechConsult Solutions"},"address":{"type":"string","example":"15 Avenue des Champs-\u00c9lys\u00e9es, 75008 Paris, France","nullable":true},"phone":{"type":"string","example":"+33 1 42 86 83 00","nullable":true},"email":{"type":"string","example":"<EMAIL>","nullable":true},"website":{"type":"string","example":"https://www.techconsult-solutions.fr","nullable":true},"siret":{"type":"string","example":"85234567890123","nullable":true},"annualLeaveDays":{"description":"Annual leave days for consultants","type":"integer","example":25},"workHoursPerDay":{"description":"Standard work hours per day","type":"number","format":"float","example":7.5},"maxRemoteWorkPercentage":{"description":"Maximum allowed remote work percentage","type":"number","format":"float","example":60},"defaultRemoteWorkPercentage":{"description":"Default remote work percentage for new entries","type":"number","format":"float","example":30},"workTimeValidationRequired":{"description":"Whether work time entries require admin validation","type":"boolean","example":false}},"type":"object"}}}}}},"put":{"tags":["Company"],"summary":"Update company information (admin only)","operationId":"put\_app\_company\_update","requestBody":{"description":"Company information to update","required":true,"content":{"application/json":{"schema":{"required":["name"],"properties":{"name":{"type":"string","example":"TechConsult Solutions"},"address":{"type":"string","example":"15 Avenue des Champs-\u00c9lys\u00e9es, 75008 Paris, France","nullable":true},"phone":{"type":"string","example":"+33 1 42 86 83 00","nullable":true},"email":{"type":"string","example":"<EMAIL>","nullable":true},"website":{"type":"string","example":"https://www.techconsult-solutions.fr","nullable":true},"siret":{"type":"string","example":"85234567890123","nullable":true},"annualLeaveDays":{"description":"Annual leave days for consultants","type":"integer","example":25},"workHoursPerDay":{"description":"Standard work hours per day","type":"number","format":"float","example":7.5},"maxRemoteWorkPercentage":{"description":"Maximum allowed remote work percentage","type":"number","format":"float","example":60},"defaultRemoteWorkPercentage":{"description":"Default remote work percentage for new entries","type":"number","format":"float","example":30},"workTimeValidationRequired":{"description":"Set to true to require admin validation for work time entries","type":"boolean","example":true}},"type":"object"}}}},"responses":{"200":{"description":"Company information updated successfully","content":{"application/json":{"schema":{"properties":{"id":{"type":"integer","example":1},"name":{"type":"string","example":"TechConsult Solutions"},"address":{"type":"string","example":"15 Avenue des Champs-\u00c9lys\u00e9es, 75008 Paris, France","nullable":true},"phone":{"type":"string","example":"+33 1 42 86 83 00","nullable":true},"email":{"type":"string","example":"<EMAIL>","nullable":true},"website":{"type":"string","example":"https://www.techconsult-solutions.fr","nullable":true},"siret":{"type":"string","example":"85234567890123","nullable":true},"annualLeaveDays":{"type":"integer","example":25},"workHoursPerDay":{"type":"number","format":"float","example":7.5},"maxRemoteWorkPercentage":{"type":"number","format":"float","example":60},"defaultRemoteWorkPercentage":{"type":"number","format":"float","example":30},"workTimeValidationRequired":{"type":"boolean","example":true}},"type":"object"}}}},"400":{"description":"Invalid input data","content":{"application/json":{"schema":{"properties":{"error":{"type":"string","example":"Company name is required"},"success":{"type":"boolean","example":false}},"type":"object"}}}},"403":{"description":"Access denied - Admin role required","content":{"application/json":{"schema":{"properties":{"error":{"type":"string","example":"Admin access required"},"success":{"type":"boolean","example":false}},"type":"object"}}}}}}},"/api/v1/consultants":{"get":{"tags":["Consultants"],"summary":"Get all consultants (admin only)","operationId":"get\_app\_consultant\_index","responses":{"200":{"description":"Returns the list of all consultants","content":{"application/json":{"schema":{"type":"array","items":{"$ref":"#/components/schemas/Consultant"}}}}},"403":{"description":"Access denied - Admin role required"}}},"post":{"tags":["Consultants"],"summary":"Create a new consultant (admin only)","operationId":"post\_app\_consultant\_create","requestBody":{"description":"Consultant data","required":true,"content":{"application/json":{"schema":{"required":["email","password","firstName","lastName"],"properties":{"email":{"type":"string","format":"email","example":"<EMAIL>"},"password":{"type":"string","format":"password","example":"securePassword123"},"firstName":{"type":"string","example":"John"},"lastName":{"type":"string","example":"Doe"},"phone":{"type":"string","example":"+33123456789"},"isAdmin":{"type":"boolean","example":false}},"type":"object"}}}},"responses":{"201":{"description":"Consultant created successfully","content":{"application/json":{"schema":{"properties":{"message":{"type":"string","example":"Consultant created successfully"},"id":{"type":"integer","example":1}},"type":"object"}}}},"400":{"description":"Invalid input data"},"403":{"description":"Access denied - Admin role required"}}}},"/api/v1/consultants/me":{"get":{"tags":["Consultants"],"summary":"Get the current consultant's profile","operationId":"get\_app\_consultant\_current","responses":{"200":{"description":"Returns the current consultant's profile","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Consultant"}}}},"401":{"description":"User not authenticated"}}}},"/api/v1/consultants/{id}":{"get":{"tags":["Consultants"],"summary":"Get a specific consultant","operationId":"get\_app\_consultant\_show","parameters":[{"name":"id","in":"path","description":"The consultant ID","required":true,"schema":{"type":"integer"}}],"responses":{"200":{"description":"Returns the consultant details","content":{"application/json":{"schema":{"$ref":"#/components/schemas/Consultant"}}}},"403":{"description":"Access denied"},"404":{"description":"Consultant not found"}}},"put":{"tags":["Consultants"],"summary":"Update an existing consultant","operationId":"put\_app\_consultant\_update","parameters":[{"name":"id","in":"path","description":"The consultant ID","required":true,"schema":{"type":"integer"}}],"requestBody":{"description":"Updated consultant data","required":true,"content":{"application/json":{"schema":{"properties":{"email":{"type":"string","format":"email","example":"<EMAIL>"},"password":{"type":"string","format":"password","example":"newPassword123"},"firstName":{"type":"string","example":"John"},"lastName":{"type":"string","example":"Doe"},"phone":{"type":"string","example":"+33123456789"},"isAdmin":{"type":"boolean","example":false}},"type":"object"}}}},"responses":{"200":{"description":"Consultant updated successfully","content":{"application/json":{"schema":{"properties":{"message":{"type":"string","example":"Consultant updated successfully"},"id":{"type":"integer","example":1}},"type":"object"}}}},"400":{"description":"Invalid input data"},"403":{"description":"Access denied"},"404":{"description":"Consultant not found"}}},"delete":{"tags":["Consultants"],"summary":"Delete a consultant (admin only)","operationId":"delete\_app\_consultant\_delete","parameters":[{"name":"id","in":"path","description":"The consultant ID","required":true,"schema":{"type":"integer"}}],"responses":{"200":{"description":"Consultant deleted successfully","content":{"application/json":{"schema":{"properties":{"message":{"type":"string","example":"Consultant deleted successfully"}},"type":"object"}}}},"403":{"description":"Access denied - Admin role required"},"404":{"description":"Consultant not found"}}}},"/api/v1/dashboard/consultant/{consultantId}":{"get":{"tags":["Dashboard"],"summary":"Get dashboard data for a specific consultant","operationId":"get\_app\_dashboard\_consultant","parameters":[{"name":"consultantId","in":"path","description":"The consultant ID","required":true,"schema":{"type":"integer"}},{"name":"debut","in":"query","description":"Start date (format: Y-m-d)","required":false,"schema":{"type":"string","format":"date"}},{"name":"fin","in":"query","description":"End date (format: Y-m-d)","required":false,"schema":{"type":"string","format":"date"}}],"responses":{"200":{"description":"Returns dashboard data for the consultant","content":{"application/json":{"schema":{"properties":{"consultant":{"properties":{"id":{"type":"integer","example":1},"lastName":{"type":"string","example":"Doe"},"firstName":{"type":"string","example":"John"},"email":{"type":"string","format":"email","example":"<EMAIL>"}},"type":"object"},"period":{"properties":{"start":{"type":"string","format":"date","example":"2023-06-01"},"end":{"type":"string","format":"date","example":"2023-06-30"}},"type":"object"},"workTime":{"properties":{"totalHours":{"type":"number","format":"float","example":160},"remoteHours":{"type":"number","format":"float","example":80},"remoteWorkPercentage":{"type":"number","format":"float","example":50}},"type":"object"},"expenses":{"properties":{"totalEur":{"type":"number","format":"float","example":450.75}},"type":"object"},"leaves":{"properties":{"remainingDays":{"type":"number","format":"float","example":15.5},"year":{"type":"integer","example":2023}},"type":"object"}},"type":"object"}}}},"400":{"description":"Invalid input data","content":{"application/json":{"schema":{"properties":{"message":{"type":"string","example":"Invalid date format"},"success":{"type":"boolean","example":false}},"type":"object"}}}},"403":{"description":"Access denied","content":{"application/json":{"schema":{"properties":{"message":{"type":"string","example":"You can only access your own dashboard"},"success":{"type":"boolean","example":false}},"type":"object"}}}},"404":{"description":"Consultant not found","content":{"application/json":{"schema":{"properties":{"message":{"type":"string","example":"Consultant not found"},"success":{"type":"boolean","example":false}},"type":"object"}}}}}}},"/api/v1/dashboard/admin":{"get":{"tags":["Dashboard"],"summary":"Get admin dashboard data (admin only)","operationId":"get\_app\_dashboard\_admin","parameters":[{"name":"debut","in":"query","description":"Start date (format: Y-m-d)","required":false,"schema":{"type":"string","format":"date"}},{"name":"fin","in":"query","description":"End date (format: Y-m-d)","required":false,"schema":{"type":"string","format":"date"}}],"responses":{"200":{"description":"Returns admin dashboard data","content":{"application/json":{"schema":{"properties":{"period":{"properties":{"start":{"type":"string","format":"date","example":"2023-06-01"},"end":{"type":"string","format":"date","example":"2023-06-30"}},"type":"object"},"global":{"properties":{"consultantCount":{"type":"integer","example":10},"totalHours":{"type":"number","format":"float","example":1600},"remoteHours":{"type":"number","format":"float","example":800},"remoteWorkPercentage":{"type":"number","format":"float","example":50},"totalExpenses":{"type":"number","format":"float","example":4500.75}},"type":"object"},"consultants":{"type":"array","items":{"properties":{"consultant":{"properties":{"id":{"type":"integer","example":1},"lastName":{"type":"string","example":"Doe"},"firstName":{"type":"string","example":"John"}},"type":"object"},"totalHours":{"type":"number","format":"float","example":160},"remoteHours":{"type":"number","format":"float","example":80},"remoteWorkPercentage":{"type":"number","format":"float","example":50},"totalExpenses":{"type":"number","format":"float","example":450.75}},"type":"object"}}},"type":"object"}}}},"400":{"description":"Invalid input data","content":{"application/json":{"schema":{"properties":{"message":{"type":"string","example":"Invalid date format"},"success":{"type":"boolean","example":false}},"type":"object"}}}},"403":{"description":"Access denied - Admin role required","content":{"application/json":{"schema":{"properties":{"message":{"type":"string","example":"Admin access required"},"success":{"type":"boolean","example":false}},"type":"object"}}}}}}},"/api/v1/expense-categories":{"get":{"tags":["Expense Categories"],"summary":"Get all expense categories","operationId":"get\_app\_expense\_category\_index","parameters":[{"name":"active","in":"query","description":"Filter by active status","required":false,"schema":{"type":"boolean"}},{"name":"root\_only","in":"query","description":"Get only root categories (no parent)","required":false,"schema":{"type":"boolean"}}],"responses":{"200":{"description":"List of expense categories","content":{"application/json":{"schema":{"type":"array","items":{"properties":{"id":{"type":"integer","example":1},"name":{"type":"string","example":"Transportation"},"code":{"type":"string","example":"TRANSPORT"},"description":{"type":"string","example":"All transportation related expenses","nullable":true},"requiresReceipt":{"type":"boolean","example":true},"isActive":{"type":"boolean","example":true},"fullPath":{"type":"string","example":"Transportation \u003E Flight Tickets"},"isRootCategory":{"type":"boolean","example":true},"parentCategory":{"properties":{"id":{"type":"integer","example":1},"name":{"type":"string","example":"Transportation"},"code":{"type":"string","example":"TRANSPORT"}},"type":"object","nullable":true},"subcategories":{"type":"array","items":{"properties":{"id":{"type":"integer","example":2},"name":{"type":"string","example":"Flight Tickets"},"code":{"type":"string","example":"FLIGHT"}},"type":"object"}}},"type":"object"}}}}}}},"post":{"tags":["Expense Categories"],"summary":"Create a new expense category (admin only)","operationId":"post\_app\_expense\_category\_create","requestBody":{"description":"Expense category data for creating a new category. Categories can be root categories or subcategories with a parent.","required":true,"content":{"application/json":{"schema":{"required":["name","code"],"properties":{"name":{"description":"Category name","type":"string","example":"Transportation"},"code":{"description":"Unique category code (uppercase, no spaces)","type":"string","example":"TRANSPORT"},"description":{"description":"Category description","type":"string","example":"All transportation related expenses","nullable":true},"requiresReceipt":{"description":"Whether expenses in this category require receipts","type":"boolean","example":true},"parentCategoryId":{"description":"ID of parent category (null for root categories)","type":"integer","example":1,"nullable":true},"isActive":{"description":"Whether the category is active","type":"boolean","example":true}},"type":"object"}}}},"responses":{"201":{"description":"Expense category created successfully","content":{"application/json":{"schema":{"properties":{"id":{"type":"integer","example":1},"name":{"type":"string","example":"Transportation"},"code":{"type":"string","example":"TRANSPORT"},"description":{"type":"string","example":"All transportation related expenses","nullable":true},"requiresReceipt":{"type":"boolean","example":true},"isActive":{"type":"boolean","example":true},"fullPath":{"type":"string","example":"Transportation"},"isRootCategory":{"type":"boolean","example":true},"parentCategory":{"properties":{"id":{"type":"integer","example":1},"name":{"type":"string","example":"Transportation"},"code":{"type":"string","example":"TRANSPORT"}},"type":"object","nullable":true},"success":{"type":"boolean","example":true}},"type":"object"}}}},"400":{"description":"Invalid data provided"},"401":{"description":"Authentication required"},"403":{"description":"Admin access required"}}}},"/api/v1/expense-categories/{id}":{"get":{"tags":["Expense Categories"],"summary":"Get a specific expense category","operationId":"get\_app\_expense\_category\_show","parameters":[{"name":"id","in":"path","required":true,"schema":{"type":"string"}}],"responses":{"200":{"description":"Expense category details"},"404":{"description":"Category not found"}}},"put":{"tags":["Expense Categories"],"summary":"Update an expense category (admin only)","operationId":"put\_app\_expense\_category\_update","parameters":[{"name":"id","in":"path","description":"Expense category ID","required":true,"schema":{"type":"integer"}}],"requestBody":{"description":"Updated expense category data","required":true,"content":{"application/json":{"schema":{"properties":{"name":{"type":"string","example":"Transportation"},"code":{"type":"string","example":"TRANSPORT"},"description":{"type":"string","example":"All transportation related expenses","nullable":true},"requiresReceipt":{"type":"boolean","example":true},"parentCategoryId":{"type":"integer","example":1,"nullable":true},"isActive":{"type":"boolean","example":true}},"type":"object"}}}},"responses":{"200":{"description":"Category updated successfully"},"400":{"description":"Invalid data"},"404":{"description":"Category not found"},"403":{"description":"Admin access required"}}},"delete":{"tags":["Expense Categories"],"summary":"Delete an expense category (admin only)","operationId":"delete\_app\_expense\_category\_delete","parameters":[{"name":"id","in":"path","description":"Expense category ID","required":true,"schema":{"type":"integer"}}],"responses":{"200":{"description":"Category deleted successfully","content":{"application/json":{"schema":{"properties":{"id":{"type":"integer","example":1},"name":{"type":"string","example":"Transportation"},"message":{"type":"string","example":"Expense category successfully deleted"},"success":{"type":"boolean","example":true}},"type":"object"}}}},"404":{"description":"Category not found"},"400":{"description":"Cannot delete category with subcategories or expenses"},"403":{"description":"Admin access required"}}}},"/api/v1/expense-categories/{id}/subcategories":{"get":{"tags":["Expense Categories"],"summary":"Get subcategories of a parent category","operationId":"get\_app\_expense\_category\_subcategories","parameters":[{"name":"id","in":"path","required":true,"schema":{"type":"string"}}],"responses":{"200":{"description":"List of subcategories","content":{"application/json":{"schema":{"type":"array","items":{"properties":{"id":{"type":"integer","example":2},"name":{"type":"string","example":"Flight Tickets"},"code":{"type":"string","example":"FLIGHT"},"description":{"type":"string","example":"Airline tickets and fees","nullable":true},"requiresReceipt":{"type":"boolean","example":true},"isActive":{"type":"boolean","example":true}},"type":"object"}}}}},"404":{"description":"Category not found"}}}},"/api/v1/expenses":{"get":{"tags":["Expenses"],"summary":"Get all expenses (admin only)","operationId":"get\_app\_expense\_index","parameters":[{"name":"start","in":"query","description":"Start date (format: Y-m-d)","required":false,"schema":{"type":"string","format":"date"}},{"name":"end","in":"query","description":"End date (format: Y-m-d)","required":false,"schema":{"type":"string","format":"date"}}],"responses":{"200":{"description":"Returns all expenses","content":{"application/json":{"schema":{"type":"array","items":{"properties":{"id":{"type":"integer","example":1},"date":{"type":"string","format":"date","example":"2023-06-15"},"description":{"type":"string","example":"Train ticket to client site"},"amount":{"type":"string","example":"125.50"},"currency":{"type":"string","example":"EUR"},"amountEur":{"type":"string","example":"125.50"},"exchangeRate":{"type":"string","example":"1.0000"},"receipt":{"type":"string","example":"receipts/2023/06/15/receipt-123.pdf","nullable":true},"validated":{"type":"boolean","example":false},"consultant":{"properties":{"id":{"type":"integer","example":1},"firstName":{"type":"string","example":"John"},"lastName":{"type":"string","example":"Doe"}},"type":"object"}},"type":"object"}}}}},"403":{"description":"Access denied - Admin role required"}}},"post":{"tags":["Expenses"],"summary":"Create a new expense","description":"This endpoint allows consultants to record business expenses with comprehensive categorization and trip association.\n\n## Travel Expense Workflow:\n1. **Create a Trip** (optional but recommended): Use POST /api/v1/trips to create a trip container\n2. **Add Individual Expenses**: Link each expense to the trip using tripId\n3. **Automatic Client Assignment**: If trip has a client, it's automatically assigned to the expense\n4. **Category Selection**: Choose appropriate expense category for proper reporting\n5. **Receipt Management**: Upload receipts for categories that require them\n\n## Example Travel Expenses:\n- Flight: categoryId=5 (Flight Tickets), requires receipt\n- Hotel: categoryId=8 (Hotel), requires receipt\n- Taxi: categoryId=7 (Taxi & Rideshare), requires receipt\n- Meals: categoryId=12 (Business Meals), requires receipt\n- Parking: categoryId=9 (Parking), receipt optional","operationId":"post\_app\_expense\_create","requestBody":{"description":"Expense data for recording business expenses. Can be linked to trips and clients for better organization. When tripId is provided and the trip has a client, the client is automatically assigned to the expense.","required":true,"content":{"application/json":{"schema":{"required":["consultantId","description","amount","currency"],"properties":{"consultantId":{"description":"ID of the consultant recording the expense","type":"integer","example":1},"clientId":{"description":"ID of the client (optional, auto-assigned if trip has client)","type":"integer","example":1,"nullable":true},"tripId":{"description":"ID of the trip to associate this expense with (recommended for travel expenses)","type":"integer","example":1,"nullable":true},"categoryId":{"description":"ID of the expense category (e.g., 5=Flight, 7=Taxi, 8=Hotel, 12=Business Meals)","type":"integer","example":5,"nullable":true},"date":{"description":"Date when the expense occurred (format: Y-m-d)","type":"string","format":"date","example":"2023-06-15"},"description":{"description":"Detailed description of the expense","type":"string","example":"Round-trip flight Paris-Zurich for client meeting"},"amount":{"description":"Expense amount in the specified currency","type":"string","example":"450.00"},"currency":{"description":"Currency code (EUR, USD, CHF, etc.)","type":"string","example":"EUR"},"receipt":{"description":"Path to uploaded receipt file (required for some categories)","type":"string","example":"receipts/flight-AF1234.pdf","nullable":true}},"type":"object"}}}},"responses":{"201":{"description":"Expense created successfully","content":{"application/json":{"schema":{"properties":{"id":{"type":"integer","example":1},"date":{"type":"string","format":"date","example":"2023-06-15"},"description":{"type":"string","example":"Train ticket to client site"},"amount":{"type":"string","example":"125.50"},"currency":{"type":"string","example":"EUR"},"amountEur":{"type":"string","example":"125.50"},"exchangeRate":{"type":"string","example":"1.0000"},"receipt":{"type":"string","example":"receipts/2023/06/15/receipt-123.pdf","nullable":true},"validated":{"type":"boolean","example":false},"consultant":{"properties":{"id":{"type":"integer","example":1},"firstName":{"type":"string","example":"John"},"lastName":{"type":"string","example":"Doe"}},"type":"object"},"client":{"properties":{"id":{"type":"integer","example":1},"name":{"type":"string","example":"Acme Corporation"},"code":{"type":"string","example":"ACME"}},"type":"object","nullable":true},"trip":{"properties":{"id":{"type":"integer","example":1},"title":{"type":"string","example":"Client Meeting in Paris"},"destination":{"type":"string","example":"Paris, France"}},"type":"object","nullable":true},"category":{"properties":{"id":{"type":"integer","example":1},"name":{"type":"string","example":"Transportation"},"code":{"type":"string","example":"TRANSPORT"}},"type":"object","nullable":true}},"type":"object"}}}},"400":{"description":"Invalid input data","content":{"application/json":{"schema":{"properties":{"error":{"type":"string","example":"Description is required"},"success":{"type":"boolean","example":false}},"type":"object"}}}},"404":{"description":"Consultant not found","content":{"application/json":{"schema":{"properties":{"error":{"type":"string","example":"Consultant not found"},"message":{"type":"string","example":"The consultant with ID 999 was not found"},"success":{"type":"boolean","example":false}},"type":"object"}}}}}}},"/api/v1/expenses/non-validated":{"get":{"tags":["Expenses"],"summary":"Get all non-validated expenses (admin only)","operationId":"get\_app\_expense\_non\_validated","responses":{"200":{"description":"Returns all non-validated expenses","content":{"application/json":{"schema":{"type":"array","items":{"properties":{"id":{"type":"integer","example":1},"date":{"type":"string","format":"date","example":"2023-06-15"},"description":{"type":"string","example":"Train ticket to client site"},"amount":{"type":"string","example":"125.50"},"currency":{"type":"string","example":"EUR"},"amountEur":{"type":"string","example":"125.50"},"exchangeRate":{"type":"string","example":"1.0000"},"receipt":{"type":"string","example":"receipts/2023/06/15/receipt-123.pdf","nullable":true},"validated":{"type":"boolean","example":false},"consultant":{"properties":{"id":{"type":"integer","example":1},"firstName":{"type":"string","example":"John"},"lastName":{"type":"string","example":"Doe"}},"type":"object"}},"type":"object"}}}}},"403":{"description":"Access denied - Admin role required"}}}},"/api/v1/expenses/my":{"get":{"tags":["Expenses"],"summary":"Get all expenses for the current user","description":"This endpoint allows consultants to view their own expenses without specifying their consultant ID.\nSupports the same filtering options as the consultant-specific endpoint for detailed expense reporting.\n\n## Travel Expense Filtering Examples:\n- Get all expenses for a trip: `/my?tripId=1`\n- Get all flight expenses: `/my?categoryId=5`\n- Get expenses for a specific client: `/my?clientId=1`\n- Get expenses for a date range: `/my?start=2023-06-01&end=2023-06-30`","operationId":"get\_app\_expense\_my","parameters":[{"name":"start","in":"query","description":"Start date filter (format: Y-m-d)","required":false,"schema":{"type":"string","format":"date"}},{"name":"end","in":"query","description":"End date filter (format: Y-m-d)","required":false,"schema":{"type":"string","format":"date"}},{"name":"tripId","in":"query","description":"Filter by trip ID","required":false,"schema":{"type":"integer"}},{"name":"clientId","in":"query","description":"Filter by client ID","required":false,"schema":{"type":"integer"}},{"name":"categoryId","in":"query","description":"Filter by expense category ID","required":false,"schema":{"type":"integer"}}],"responses":{"200":{"description":"List of current user expenses","content":{"application/json":{"schema":{"type":"array","items":{"properties":{"id":{"type":"integer","example":1},"date":{"type":"string","format":"date","example":"2023-06-15"},"description":{"type":"string","example":"Flight to Paris"},"amount":{"type":"string","example":"450.00"},"currency":{"type":"string","example":"EUR"},"amountEur":{"type":"string","example":"450.00"},"exchangeRate":{"type":"string","example":"1.0000","nullable":true},"receipt":{"type":"string","example":"receipt\_123.pdf","nullable":true},"validated":{"type":"boolean","example":false}},"type":"object"}}}}},"401":{"description":"Authentication required"}}}},"/api/v1/expenses/{id}":{"get":{"tags":["Expenses"],"summary":"Get a specific expense by ID","operationId":"get\_app\_expense\_show","parameters":[{"name":"id","in":"path","description":"Expense ID","required":true,"schema":{"type":"integer"}}],"responses":{"200":{"description":"Returns the expense details","content":{"application/json":{"schema":{"properties":{"id":{"type":"integer","example":1},"date":{"type":"string","format":"date","example":"2023-06-15"},"description":{"type":"string","example":"Train ticket to client site"},"amount":{"type":"string","example":"125.50"},"currency":{"type":"string","example":"EUR"},"amountEur":{"type":"string","example":"125.50"},"exchangeRate":{"type":"string","example":"1.0000"},"receipt":{"type":"string","example":"receipts/2023/06/15/receipt-123.pdf","nullable":true},"validated":{"type":"boolean","example":false},"consultant":{"properties":{"id":{"type":"integer","example":1},"firstName":{"type":"string","example":"John"},"lastName":{"type":"string","example":"Doe"}},"type":"object"}},"type":"object"}}}},"404":{"description":"Expense not found","content":{"application/json":{"schema":{"properties":{"error":{"type":"string","example":"Expense not found"},"message":{"type":"string","example":"The expense with ID 999 was not found"},"success":{"type":"boolean","example":false}},"type":"object"}}}}}},"put":{"tags":["Expenses"],"summary":"Update an existing expense","operationId":"put\_app\_expense\_update","parameters":[{"name":"id","in":"path","description":"Expense ID","required":true,"schema":{"type":"integer"}}],"requestBody":{"description":"Updated expense data","required":true,"content":{"application/json":{"schema":{"properties":{"date":{"type":"string","format":"date","example":"2023-06-15"},"description":{"type":"string","example":"Train ticket to client site"},"amount":{"type":"string","example":"125.50"},"currency":{"type":"string","example":"EUR"},"receipt":{"type":"string","example":"receipts/2023/06/15/receipt-123.pdf","nullable":true}},"type":"object"}}}},"responses":{"200":{"description":"Expense updated successfully","content":{"application/json":{"schema":{"properties":{"id":{"type":"integer","example":1},"date":{"type":"string","format":"date","example":"2023-06-15"},"description":{"type":"string","example":"Train ticket to client site"},"amount":{"type":"string","example":"125.50"},"currency":{"type":"string","example":"EUR"},"amountEur":{"type":"string","example":"125.50"},"exchangeRate":{"type":"string","example":"1.0000"},"receipt":{"type":"string","example":"receipts/2023/06/15/receipt-123.pdf","nullable":true},"validated":{"type":"boolean","example":false},"consultant":{"properties":{"id":{"type":"integer","example":1},"firstName":{"type":"string","example":"John"},"lastName":{"type":"string","example":"Doe"}},"type":"object"}},"type":"object"}}}},"404":{"description":"Expense not found","content":{"application/json":{"schema":{"properties":{"error":{"type":"string","example":"Expense not found"},"message":{"type":"string","example":"The expense with ID 999 was not found"},"success":{"type":"boolean","example":false}},"type":"object"}}}},"403":{"description":"Access denied","content":{"application/json":{"schema":{"properties":{"error":{"type":"string","example":"You cannot modify a validated expense"},"success":{"type":"boolean","example":false}},"type":"object"}}}}}},"delete":{"tags":["Expenses"],"summary":"Delete an expense","operationId":"delete\_app\_expense\_delete","parameters":[{"name":"id","in":"path","description":"Expense ID","required":true,"schema":{"type":"integer"}}],"responses":{"200":{"description":"Expense deleted successfully","content":{"application/json":{"schema":{"properties":{"id":{"type":"integer","example":1},"date":{"type":"string","format":"date","example":"2023-06-15"},"message":{"type":"string","example":"Expense successfully deleted"},"success":{"type":"boolean","example":true}},"type":"object"}}}},"404":{"description":"Expense not found","content":{"application/json":{"schema":{"properties":{"error":{"type":"string","example":"Expense not found"},"message":{"type":"string","example":"The expense with ID 999 was not found"},"success":{"type":"boolean","example":false}},"type":"object"}}}},"403":{"description":"Access denied","content":{"application/json":{"schema":{"properties":{"error":{"type":"string","example":"You cannot delete a validated expense"},"success":{"type":"boolean","example":false}},"type":"object"}}}}}}},"/api/v1/expenses/consultant/{consultantId}":{"get":{"tags":["Expenses"],"summary":"Get expenses for a specific consultant","description":"This endpoint allows consultants to view their own expenses or administrators to view any consultant's expenses.\nSupports filtering by date range, trip, client, and category for detailed expense reporting.\n\n## Travel Expense Filtering Examples:\n- Get all expenses for a trip: `/consultant/1?tripId=1`\n- Get all flight expenses: `/consultant/1?categoryId=5`\n- Get expenses for a specific client: `/consultant/1?clientId=1`\n- Get expenses for a date range: `/consultant/1?start=2023-06-01&end=2023-06-30`","operationId":"get\_app\_expense\_by\_consultant","parameters":[{"name":"consultantId","in":"path","description":"Consultant ID - consultants can only access their own expenses, admins can access any","required":true,"schema":{"type":"integer"},"example":1},{"name":"start","in":"query","description":"Start date filter (format: Y-m-d) - useful for monthly/quarterly reports","required":false,"schema":{"type":"string","format":"date"},"example":"2023-06-01"},{"name":"end","in":"query","description":"End date filter (format: Y-m-d) - useful for monthly/quarterly reports","required":false,"schema":{"type":"string","format":"date"},"example":"2023-06-30"},{"name":"tripId","in":"query","description":"Filter by specific trip ID - get all expenses for a particular business trip","required":false,"schema":{"type":"integer"},"example":1},{"name":"clientId","in":"query","description":"Filter by specific client ID - useful for client-specific expense reports","required":false,"schema":{"type":"integer"},"example":1},{"name":"categoryId","in":"query","description":"Filter by expense category - useful for viewing specific types of expenses (e.g., all flights, hotels)","required":false,"schema":{"type":"integer"},"example":5}],"responses":{"200":{"description":"Returns expenses for the consultant with full relationship information including trip, client, and category details","content":{"application/json":{"schema":{"type":"array","items":{"properties":{"id":{"type":"integer","example":1},"date":{"type":"string","format":"date","example":"2023-06-15"},"description":{"type":"string","example":"Round-trip flight Paris-Zurich"},"amount":{"type":"string","example":"450.00"},"currency":{"type":"string","example":"EUR"},"amountEur":{"type":"string","example":"450.00"},"exchangeRate":{"type":"string","example":"1.0000"},"receipt":{"type":"string","example":"receipts/flight-AF1234.pdf","nullable":true},"validated":{"type":"boolean","example":false},"consultant":{"properties":{"id":{"type":"integer","example":1},"firstName":{"type":"string","example":"John"},"lastName":{"type":"string","example":"Smith"}},"type":"object"},"client":{"properties":{"id":{"type":"integer","example":1},"name":{"type":"string","example":"Acme Corporation"},"code":{"type":"string","example":"ACME"}},"type":"object","nullable":true},"trip":{"properties":{"id":{"type":"integer","example":1},"title":{"type":"string","example":"Client Meeting in Paris"},"destination":{"type":"string","example":"Paris, France"}},"type":"object","nullable":true},"category":{"properties":{"id":{"type":"integer","example":5},"name":{"type":"string","example":"Flight Tickets"},"code":{"type":"string","example":"FLIGHT"}},"type":"object","nullable":true}},"type":"object"}}}}},"404":{"description":"Consultant not found","content":{"application/json":{"schema":{"properties":{"error":{"type":"string","example":"Consultant not found"},"message":{"type":"string","example":"The consultant with ID 999 was not found"},"success":{"type":"boolean","example":false}},"type":"object"}}}}}}},"/api/v1/expenses/{id}/validate":{"put":{"tags":["Expenses"],"summary":"Validate an expense (admin only)","operationId":"put\_app\_expense\_validate","parameters":[{"name":"id","in":"path","description":"Expense ID","required":true,"schema":{"type":"integer"}}],"responses":{"200":{"description":"Expense validated successfully","content":{"application/json":{"schema":{"properties":{"id":{"type":"integer","example":1},"date":{"type":"string","format":"date","example":"2023-06-15"},"description":{"type":"string","example":"Train ticket to client site"},"amount":{"type":"string","example":"125.50"},"currency":{"type":"string","example":"EUR"},"amountEur":{"type":"string","example":"125.50"},"exchangeRate":{"type":"string","example":"1.0000"},"receipt":{"type":"string","example":"receipts/2023/06/15/receipt-123.pdf","nullable":true},"validated":{"type":"boolean","example":true},"consultant":{"properties":{"id":{"type":"integer","example":1},"firstName":{"type":"string","example":"John"},"lastName":{"type":"string","example":"Doe"}},"type":"object"}},"type":"object"}}}},"404":{"description":"Expense not found","content":{"application/json":{"schema":{"properties":{"error":{"type":"string","example":"Expense not found"},"message":{"type":"string","example":"The expense with ID 999 was not found"},"success":{"type":"boolean","example":false}},"type":"object"}}}},"403":{"description":"Access denied - Admin role required","content":{"application/json":{"schema":{"properties":{"error":{"type":"string","example":"Admin access required"},"success":{"type":"boolean","example":false}},"type":"object"}}}}}}},"/api/v1/expenses/total/{consultantId}":{"get":{"tags":["Expenses"],"summary":"Calculate total expenses for a consultant","operationId":"get\_app\_expense\_total","parameters":[{"name":"consultantId","in":"path","description":"Consultant ID","required":true,"schema":{"type":"integer"}},{"name":"start","in":"query","description":"Start date (format: Y-m-d)","required":false,"schema":{"type":"string","format":"date"}},{"name":"end","in":"query","description":"End date (format: Y-m-d)","required":false,"schema":{"type":"string","format":"date"}}],"responses":{"200":{"description":"Returns the total expenses amount in EUR","content":{"application/json":{"schema":{"properties":{"total":{"type":"number","format":"float","example":1250.75}},"type":"object"}}}},"404":{"description":"Consultant not found","content":{"application/json":{"schema":{"properties":{"error":{"type":"string","example":"Consultant not found"},"message":{"type":"string","example":"The consultant with ID 999 was not found"},"success":{"type":"boolean","example":false}},"type":"object"}}}}}}},"/api/v1/leaves":{"get":{"tags":["Leaves"],"summary":"Get all leave requests (admin only)","operationId":"get\_app\_leave\_index","responses":{"200":{"description":"Returns all leave requests","content":{"application/json":{"schema":{"type":"array","items":{"properties":{"id":{"type":"integer","example":1},"startDate":{"type":"string","format":"date","example":"2023-07-10"},"endDate":{"type":"string","format":"date","example":"2023-07-15"},"numberOfDays":{"type":"number","format":"float","example":5},"type":{"type":"string","example":"annual"},"comment":{"type":"string","example":"Summer vacation","nullable":true},"status":{"type":"string","example":"pending"},"consultant":{"properties":{"id":{"type":"integer","example":1},"firstName":{"type":"string","example":"John"},"lastName":{"type":"string","example":"Doe"}},"type":"object"}},"type":"object"}}}}},"403":{"description":"Access denied - Admin role required"}}},"post":{"tags":["Leaves"],"summary":"Create a new leave request","operationId":"post\_app\_leave\_create","requestBody":{"description":"Leave request data","required":true,"content":{"application/json":{"schema":{"required":["consultantId","startDate","endDate","numberOfDays","type"],"properties":{"consultantId":{"type":"integer","example":1},"startDate":{"type":"string","format":"date","example":"2023-07-10"},"endDate":{"type":"string","format":"date","example":"2023-07-15"},"numberOfDays":{"type":"number","format":"float","example":5},"type":{"type":"string","example":"annual"},"comment":{"type":"string","example":"Summer vacation","nullable":true}},"type":"object"}}}},"responses":{"201":{"description":"Leave request created successfully","content":{"application/json":{"schema":{"properties":{"id":{"type":"integer","example":1},"startDate":{"type":"string","format":"date","example":"2023-07-10"},"endDate":{"type":"string","format":"date","example":"2023-07-15"},"numberOfDays":{"type":"number","format":"float","example":5},"type":{"type":"string","example":"annual"},"comment":{"type":"string","example":"Summer vacation","nullable":true},"status":{"type":"string","example":"pending"},"consultant":{"properties":{"id":{"type":"integer","example":1},"firstName":{"type":"string","example":"John"},"lastName":{"type":"string","example":"Doe"}},"type":"object"}},"type":"object"}}}},"400":{"description":"Invalid input data","content":{"application/json":{"schema":{"properties":{"error":{"type":"string","example":"Start date and end date are required"},"success":{"type":"boolean","example":false}},"type":"object"}}}},"404":{"description":"Consultant not found","content":{"application/json":{"schema":{"properties":{"error":{"type":"string","example":"Consultant not found"},"message":{"type":"string","example":"The consultant with ID 999 was not found"},"success":{"type":"boolean","example":false}},"type":"object"}}}},"403":{"description":"Access denied","content":{"application/json":{"schema":{"properties":{"error":{"type":"string","example":"You can only access your own data"},"success":{"type":"boolean","example":false}},"type":"object"}}}}}}},"/api/v1/leaves/pending":{"get":{"tags":["Leaves"],"summary":"Get all pending leave requests (admin only)","operationId":"get\_app\_leave\_pending","responses":{"200":{"description":"Returns all pending leave requests","content":{"application/json":{"schema":{"type":"array","items":{"properties":{"id":{"type":"integer","example":1},"startDate":{"type":"string","format":"date","example":"2023-07-10"},"endDate":{"type":"string","format":"date","example":"2023-07-15"},"numberOfDays":{"type":"number","format":"float","example":5},"type":{"type":"string","example":"annual"},"comment":{"type":"string","example":"Summer vacation","nullable":true},"status":{"type":"string","example":"pending"},"consultant":{"properties":{"id":{"type":"integer","example":1},"firstName":{"type":"string","example":"John"},"lastName":{"type":"string","example":"Doe"}},"type":"object"}},"type":"object"}}}}},"403":{"description":"Access denied - Admin role required"}}}},"/api/v1/leaves/{id}":{"get":{"tags":["Leaves"],"summary":"Get a specific leave request by ID","operationId":"get\_app\_leave\_show","parameters":[{"name":"id","in":"path","description":"Leave request ID","required":true,"schema":{"type":"integer"}}],"responses":{"200":{"description":"Returns the leave request details","content":{"application/json":{"schema":{"properties":{"id":{"type":"integer","example":1},"startDate":{"type":"string","format":"date","example":"2023-07-10"},"endDate":{"type":"string","format":"date","example":"2023-07-15"},"numberOfDays":{"type":"number","format":"float","example":5},"type":{"type":"string","example":"annual"},"comment":{"type":"string","example":"Summer vacation","nullable":true},"status":{"type":"string","example":"pending"},"consultant":{"properties":{"id":{"type":"integer","example":1},"firstName":{"type":"string","example":"John"},"lastName":{"type":"string","example":"Doe"}},"type":"object"}},"type":"object"}}}},"404":{"description":"Leave request not found","content":{"application/json":{"schema":{"properties":{"error":{"type":"string","example":"Leave request not found"},"message":{"type":"string","example":"The leave request with ID 999 was not found"},"success":{"type":"boolean","example":false}},"type":"object"}}}}}},"put":{"tags":["Leaves"],"summary":"Update an existing leave request","operationId":"put\_app\_leave\_update","parameters":[{"name":"id","in":"path","description":"Leave request ID","required":true,"schema":{"type":"integer"}}],"requestBody":{"description":"Updated leave request data","required":true,"content":{"application/json":{"schema":{"properties":{"startDate":{"type":"string","format":"date","example":"2023-07-10"},"endDate":{"type":"string","format":"date","example":"2023-07-15"},"numberOfDays":{"type":"number","format":"float","example":5},"type":{"type":"string","example":"annual"},"comment":{"type":"string","example":"Summer vacation","nullable":true}},"type":"object"}}}},"responses":{"200":{"description":"Leave request updated successfully","content":{"application/json":{"schema":{"properties":{"id":{"type":"integer","example":1},"startDate":{"type":"string","format":"date","example":"2023-07-10"},"endDate":{"type":"string","format":"date","example":"2023-07-15"},"numberOfDays":{"type":"number","format":"float","example":5},"type":{"type":"string","example":"annual"},"comment":{"type":"string","example":"Summer vacation","nullable":true},"status":{"type":"string","example":"pending"},"consultant":{"properties":{"id":{"type":"integer","example":1},"firstName":{"type":"string","example":"John"},"lastName":{"type":"string","example":"Doe"}},"type":"object"}},"type":"object"}}}},"400":{"description":"Invalid input data","content":{"application/json":{"schema":{"properties":{"error":{"type":"string","example":"Not enough leave days available"},"success":{"type":"boolean","example":false}},"type":"object"}}}},"404":{"description":"Leave request not found","content":{"application/json":{"schema":{"properties":{"error":{"type":"string","example":"Leave request not found"},"message":{"type":"string","example":"The leave request with ID 999 was not found"},"success":{"type":"boolean","example":false}},"type":"object"}}}},"403":{"description":"Access denied","content":{"application/json":{"schema":{"properties":{"error":{"type":"string","example":"You cannot modify a leave that is not pending"},"success":{"type":"boolean","example":false}},"type":"object"}}}}}},"delete":{"tags":["Leaves"],"summary":"Delete a leave request","operationId":"delete\_app\_leave\_delete","parameters":[{"name":"id","in":"path","description":"Leave request ID","required":true,"schema":{"type":"integer"}}],"responses":{"200":{"description":"Leave request deleted successfully","content":{"application/json":{"schema":{"properties":{"id":{"type":"integer","example":1},"startDate":{"type":"string","format":"date","example":"2023-07-10"},"message":{"type":"string","example":"Leave request successfully deleted"},"success":{"type":"boolean","example":true}},"type":"object"}}}},"404":{"description":"Leave request not found","content":{"application/json":{"schema":{"properties":{"error":{"type":"string","example":"Leave request not found"},"message":{"type":"string","example":"The leave request with ID 999 was not found"},"success":{"type":"boolean","example":false}},"type":"object"}}}},"403":{"description":"Access denied","content":{"application/json":{"schema":{"properties":{"error":{"type":"string","example":"You cannot delete a leave that is not pending"},"success":{"type":"boolean","example":false}},"type":"object"}}}}}}},"/api/v1/leaves/consultant/{consultantId}":{"get":{"tags":["Leaves"],"summary":"Get leave requests for a specific consultant","operationId":"get\_app\_leave\_by\_consultant","parameters":[{"name":"consultantId","in":"path","description":"Consultant ID","required":true,"schema":{"type":"integer"}},{"name":"start","in":"query","description":"Start date (format: Y-m-d)","required":false,"schema":{"type":"string","format":"date"}},{"name":"end","in":"query","description":"End date (format: Y-m-d)","required":false,"schema":{"type":"string","format":"date"}}],"responses":{"200":{"description":"Returns leave requests for the consultant","content":{"application/json":{"schema":{"type":"array","items":{"properties":{"id":{"type":"integer","example":1},"startDate":{"type":"string","format":"date","example":"2023-07-10"},"endDate":{"type":"string","format":"date","example":"2023-07-15"},"numberOfDays":{"type":"number","format":"float","example":5},"type":{"type":"string","example":"annual"},"comment":{"type":"string","example":"Summer vacation","nullable":true},"status":{"type":"string","example":"pending"}},"type":"object"}}}}},"404":{"description":"Consultant not found","content":{"application/json":{"schema":{"properties":{"error":{"type":"string","example":"Consultant not found"},"message":{"type":"string","example":"The consultant with ID 999 was not found"},"success":{"type":"boolean","example":false}},"type":"object"}}}},"403":{"description":"Access denied","content":{"application/json":{"schema":{"properties":{"error":{"type":"string","example":"You can only access your own data"},"success":{"type":"boolean","example":false}},"type":"object"}}}}}}},"/api/v1/leaves/{id}/approve":{"put":{"tags":["Leaves"],"summary":"Approve a leave request (admin only)","operationId":"put\_app\_leave\_approve","parameters":[{"name":"id","in":"path","description":"Leave request ID","required":true,"schema":{"type":"integer"}}],"responses":{"200":{"description":"Leave request approved successfully","content":{"application/json":{"schema":{"properties":{"id":{"type":"integer","example":1},"startDate":{"type":"string","format":"date","example":"2023-07-10"},"endDate":{"type":"string","format":"date","example":"2023-07-15"},"numberOfDays":{"type":"number","format":"float","example":5},"type":{"type":"string","example":"annual"},"comment":{"type":"string","example":"Summer vacation","nullable":true},"status":{"type":"string","example":"approved"},"consultant":{"properties":{"id":{"type":"integer","example":1},"firstName":{"type":"string","example":"John"},"lastName":{"type":"string","example":"Doe"}},"type":"object"}},"type":"object"}}}},"404":{"description":"Leave request not found","content":{"application/json":{"schema":{"properties":{"error":{"type":"string","example":"Leave request not found"},"message":{"type":"string","example":"The leave request with ID 999 was not found"},"success":{"type":"boolean","example":false}},"type":"object"}}}},"400":{"description":"Invalid operation","content":{"application/json":{"schema":{"properties":{"error":{"type":"string","example":"Only pending leaves can be approved"},"success":{"type":"boolean","example":false}},"type":"object"}}}},"403":{"description":"Access denied - Admin role required"}}}},"/api/v1/leaves/{id}/reject":{"put":{"tags":["Leaves"],"summary":"Reject a leave request (admin only)","operationId":"put\_app\_leave\_reject","parameters":[{"name":"id","in":"path","description":"Leave request ID","required":true,"schema":{"type":"integer"}}],"requestBody":{"description":"Rejection reason","required":false,"content":{"application/json":{"schema":{"properties":{"reason":{"type":"string","example":"Resource constraints"}},"type":"object"}}}},"responses":{"200":{"description":"Leave request rejected successfully","content":{"application/json":{"schema":{"properties":{"id":{"type":"integer","example":1},"startDate":{"type":"string","format":"date","example":"2023-07-10"},"endDate":{"type":"string","format":"date","example":"2023-07-15"},"numberOfDays":{"type":"number","format":"float","example":5},"type":{"type":"string","example":"annual"},"comment":{"type":"string","example":"Summer vacation\\n\\nRejection reason: Resource constraints","nullable":true},"status":{"type":"string","example":"rejected"},"consultant":{"properties":{"id":{"type":"integer","example":1},"firstName":{"type":"string","example":"John"},"lastName":{"type":"string","example":"Doe"}},"type":"object"}},"type":"object"}}}},"404":{"description":"Leave request not found","content":{"application/json":{"schema":{"properties":{"error":{"type":"string","example":"Leave request not found"},"message":{"type":"string","example":"The leave request with ID 999 was not found"},"success":{"type":"boolean","example":false}},"type":"object"}}}},"400":{"description":"Invalid operation","content":{"application/json":{"schema":{"properties":{"error":{"type":"string","example":"Only pending leaves can be rejected"},"success":{"type":"boolean","example":false}},"type":"object"}}}},"403":{"description":"Access denied - Admin role required"}}}},"/api/v1/leaves/balance/{consultantId}":{"get":{"tags":["Leaves"],"summary":"Calculate the remaining leave balance for a consultant","operationId":"get\_app\_leave\_balance","parameters":[{"name":"consultantId","in":"path","description":"Consultant ID","required":true,"schema":{"type":"integer"}}],"responses":{"200":{"description":"Returns the remaining leave balance","content":{"application/json":{"schema":{"properties":{"balance":{"type":"number","format":"float","example":15.5}},"type":"object"}}}},"404":{"description":"Consultant not found","content":{"application/json":{"schema":{"properties":{"error":{"type":"string","example":"Consultant not found"},"message":{"type":"string","example":"The consultant with ID 999 was not found"},"success":{"type":"boolean","example":false}},"type":"object"}}}},"403":{"description":"Access denied","content":{"application/json":{"schema":{"properties":{"error":{"type":"string","example":"You can only access your own data"},"success":{"type":"boolean","example":false}},"type":"object"}}}}}}},"/api/v1/trips":{"get":{"tags":["Trips"],"summary":"Get all trips (admin) or consultant's trips","operationId":"get\_app\_trip\_index","parameters":[{"name":"start","in":"query","description":"Start date filter (format: Y-m-d)","required":false,"schema":{"type":"string","format":"date"}},{"name":"end","in":"query","description":"End date filter (format: Y-m-d)","required":false,"schema":{"type":"string","format":"date"}}],"responses":{"200":{"description":"List of trips","content":{"application/json":{"schema":{"type":"array","items":{"properties":{"id":{"type":"integer","example":1},"title":{"type":"string","example":"Client Meeting in Paris"},"destination":{"type":"string","example":"Paris, France","nullable":true},"purpose":{"type":"string","example":"Client consultation","nullable":true},"startDate":{"type":"string","format":"date","example":"2023-06-15"},"endDate":{"type":"string","format":"date","example":"2023-06-17"},"notes":{"type":"string","example":"Important project kickoff","nullable":true},"totalExpenses":{"type":"number","format":"float","example":1250.5},"durationInDays":{"type":"integer","example":3},"consultant":{"properties":{"id":{"type":"integer","example":1},"firstName":{"type":"string","example":"John"},"lastName":{"type":"string","example":"Doe"}},"type":"object"},"client":{"properties":{"id":{"type":"integer","example":1},"name":{"type":"string","example":"Acme Corporation"},"code":{"type":"string","example":"ACME"}},"type":"object","nullable":true}},"type":"object"}}}}}}},"post":{"tags":["Trips"],"summary":"Create a new trip","operationId":"post\_app\_trip\_create","requestBody":{"description":"Trip data for creating a new business trip. This endpoint allows consultants to create trips that can group multiple related expenses together for better organization and reporting.","required":true,"content":{"application/json":{"schema":{"required":["title","startDate","endDate"],"properties":{"title":{"description":"Descriptive title for the trip","type":"string","example":"Client Meeting in Paris"},"destination":{"description":"Trip destination city/country","type":"string","example":"Paris, France","nullable":true},"purpose":{"description":"Business purpose of the trip","type":"string","example":"Quarterly business review with client","nullable":true},"startDate":{"description":"Trip start date (format: Y-m-d)","type":"string","format":"date","example":"2023-06-15"},"endDate":{"description":"Trip end date (format: Y-m-d)","type":"string","format":"date","example":"2023-06-17"},"clientId":{"description":"ID of the client associated with this trip (optional)","type":"integer","example":1,"nullable":true},"notes":{"description":"Additional notes about the trip","type":"string","example":"Important Q2 review meeting with key stakeholders","nullable":true}},"type":"object"}}}},"responses":{"201":{"description":"Trip created successfully. The response includes the complete trip information with calculated fields like duration and total expenses (initially 0).","content":{"application/json":{"schema":{"properties":{"id":{"type":"integer","example":1},"title":{"type":"string","example":"Client Meeting in Paris"},"destination":{"type":"string","example":"Paris, France","nullable":true},"purpose":{"type":"string","example":"Quarterly business review with client","nullable":true},"startDate":{"type":"string","format":"date","example":"2023-06-15"},"endDate":{"type":"string","format":"date","example":"2023-06-17"},"notes":{"type":"string","example":"Important Q2 review meeting","nullable":true},"totalExpenses":{"type":"number","format":"float","example":0},"durationInDays":{"type":"integer","example":3},"consultant":{"properties":{"id":{"type":"integer","example":1},"firstName":{"type":"string","example":"John"},"lastName":{"type":"string","example":"Smith"}},"type":"object"},"client":{"properties":{"id":{"type":"integer","example":1},"name":{"type":"string","example":"Acme Corporation"},"code":{"type":"string","example":"ACME"}},"type":"object","nullable":true},"success":{"type":"boolean","example":true}},"type":"object"}}}},"400":{"description":"Invalid data provided"},"401":{"description":"Authentication required"},"403":{"description":"Access denied"}}}},"/api/v1/trips/{id}":{"get":{"tags":["Trips"],"summary":"Get a specific trip","operationId":"get\_app\_trip\_show","parameters":[{"name":"id","in":"path","required":true,"schema":{"type":"string"}}],"responses":{"200":{"description":"Trip details"},"404":{"description":"Trip not found"},"403":{"description":"Access denied"}}},"put":{"tags":["Trips"],"summary":"Update a trip","operationId":"put\_app\_trip\_update","parameters":[{"name":"id","in":"path","description":"Trip ID","required":true,"schema":{"type":"integer"}}],"requestBody":{"description":"Trip data for updating an existing business trip. Consultants can only update their own trips, while administrators can update any trip.","required":true,"content":{"application/json":{"schema":{"properties":{"title":{"description":"Descriptive title for the trip","type":"string","example":"Updated Client Meeting in Paris"},"destination":{"description":"Trip destination city/country","type":"string","example":"Paris, France","nullable":true},"purpose":{"description":"Business purpose of the trip","type":"string","example":"Updated quarterly business review with client","nullable":true},"startDate":{"description":"Trip start date (format: Y-m-d)","type":"string","format":"date","example":"2023-06-15"},"endDate":{"description":"Trip end date (format: Y-m-d)","type":"string","format":"date","example":"2023-06-17"},"clientId":{"description":"ID of the client associated with this trip (optional)","type":"integer","example":1,"nullable":true},"notes":{"description":"Additional notes or comments about the trip","type":"string","example":"Updated notes about the trip","nullable":true}},"type":"object"}}}},"responses":{"200":{"description":"Trip updated successfully","content":{"application/json":{"schema":{"properties":{"id":{"type":"integer","example":1},"title":{"type":"string","example":"Updated Client Meeting in Paris"},"destination":{"type":"string","example":"Paris, France","nullable":true},"purpose":{"type":"string","example":"Updated quarterly business review","nullable":true},"startDate":{"type":"string","format":"date","example":"2023-06-15"},"endDate":{"type":"string","format":"date","example":"2023-06-17"},"notes":{"type":"string","example":"Updated notes","nullable":true},"totalExpenses":{"type":"string","example":"0.00"},"durationInDays":{"type":"integer","example":3},"consultant":{"properties":{"id":{"type":"integer","example":1},"firstName":{"type":"string","example":"John"},"lastName":{"type":"string","example":"Doe"}},"type":"object"},"client":{"properties":{"id":{"type":"integer","example":1},"name":{"type":"string","example":"Acme Corporation"},"code":{"type":"string","example":"ACME"}},"type":"object","nullable":true},"success":{"type":"boolean","example":true}},"type":"object"}}}},"400":{"description":"Invalid data provided"},"401":{"description":"Authentication required"},"403":{"description":"Access denied - can only update own trips"},"404":{"description":"Trip not found"}}},"delete":{"tags":["Trips"],"summary":"Delete a trip","operationId":"delete\_app\_trip\_delete","parameters":[{"name":"id","in":"path","description":"Trip ID","required":true,"schema":{"type":"integer"}}],"responses":{"200":{"description":"Trip deleted successfully","content":{"application/json":{"schema":{"properties":{"id":{"type":"integer","example":1},"title":{"type":"string","example":"Client Meeting in Paris"},"message":{"type":"string","example":"Trip successfully deleted"},"success":{"type":"boolean","example":true}},"type":"object"}}}},"404":{"description":"Trip not found","content":{"application/json":{"schema":{"properties":{"error":{"type":"string","example":"Trip not found"},"message":{"type":"string","example":"The trip with ID 999 was not found"},"success":{"type":"boolean","example":false}},"type":"object"}}}},"403":{"description":"Access denied","content":{"application/json":{"schema":{"properties":{"error":{"type":"string","example":"You can only delete your own trips"},"success":{"type":"boolean","example":false}},"type":"object"}}}},"400":{"description":"Cannot delete trip with expenses","content":{"application/json":{"schema":{"properties":{"error":{"type":"string","example":"Cannot delete trip with associated expenses"},"message":{"type":"string","example":"Please delete or reassign expenses first"},"success":{"type":"boolean","example":false}},"type":"object"}}}}}}},"/api/v1/trips/my":{"get":{"tags":["Trips"],"summary":"Get all trips for the current user","description":"This endpoint allows consultants to view their own trips without specifying their consultant ID.\nSupports the same filtering options as the main trips endpoint.","operationId":"get\_app\_trip\_my","parameters":[{"name":"start","in":"query","description":"Start date filter (format: Y-m-d)","required":false,"schema":{"type":"string","format":"date"}},{"name":"end","in":"query","description":"End date filter (format: Y-m-d)","required":false,"schema":{"type":"string","format":"date"}}],"responses":{"200":{"description":"List of current user trips","content":{"application/json":{"schema":{"type":"array","items":{"properties":{"id":{"type":"integer","example":1},"title":{"type":"string","example":"Client Meeting in Paris"},"destination":{"type":"string","example":"Paris, France","nullable":true},"purpose":{"type":"string","example":"Client consultation","nullable":true},"startDate":{"type":"string","format":"date","example":"2023-06-15"},"endDate":{"type":"string","format":"date","example":"2023-06-17"},"notes":{"type":"string","example":"Important project kickoff","nullable":true},"totalExpenses":{"type":"number","format":"float","example":1250.5},"durationInDays":{"type":"integer","example":3},"consultant":{"properties":{"id":{"type":"integer","example":1},"firstName":{"type":"string","example":"John"},"lastName":{"type":"string","example":"Doe"}},"type":"object"},"client":{"properties":{"id":{"type":"integer","example":1},"name":{"type":"string","example":"Acme Corporation"},"code":{"type":"string","example":"ACME"}},"type":"object","nullable":true}},"type":"object"}}}}},"401":{"description":"Authentication required"}}}},"/api/v1/work-time":{"get":{"tags":["Work Time"],"summary":"Get all work time entries (admin only)","operationId":"get\_app\_work\_time\_index","parameters":[{"name":"start","in":"query","description":"Start date (format: Y-m-d)","required":false,"schema":{"type":"string","format":"date"}},{"name":"end","in":"query","description":"End date (format: Y-m-d)","required":false,"schema":{"type":"string","format":"date"}}],"responses":{"200":{"description":"Returns all work time entries","content":{"application/json":{"schema":{"type":"array","items":{"properties":{"id":{"type":"integer","example":1},"startDate":{"type":"string","format":"date","example":"2024-01-01"},"endDate":{"type":"string","format":"date","example":"2024-01-31"},"totalHours":{"type":"number","format":"float","example":168},"remoteWorkPercentage":{"type":"number","format":"float","example":40},"notes":{"type":"string","example":"January 2024 work period"},"isValidated":{"type":"boolean","example":true},"validationStatus":{"type":"string","example":"Validated"},"periodIdentifier":{"type":"string","example":"2024-01-01 to 2024-01-31"},"durationInDays":{"type":"integer","example":31},"activities":{"type":"array","items":{"properties":{"id":{"type":"integer","example":1},"activityName":{"type":"string","example":"Backend Development"},"hours":{"type":"number","format":"float","example":120},"description":{"type":"string","example":"API development"},"isBillable":{"type":"boolean","example":true},"percentageOfTotal":{"type":"number","format":"float","example":71.4}},"type":"object"}},"consultant":{"properties":{"id":{"type":"integer","example":1},"firstName":{"type":"string","example":"Marie"},"lastName":{"type":"string","example":"Dubois"}},"type":"object"}},"type":"object"}}}}},"403":{"description":"Access denied - Admin role required"}}},"post":{"tags":["Work Time"],"summary":"Create a new work time entry with period and activities","operationId":"post\_app\_work\_time\_create","requestBody":{"description":"Work time data with period and activities","required":true,"content":{"application/json":{"schema":{"required":["startDate","endDate","totalHours"],"properties":{"consultantId":{"description":"Optional: Only for admins. If not provided, current user's ID will be used","type":"integer","example":1},"startDate":{"description":"Start date of the work period","type":"string","format":"date","example":"2024-01-01"},"endDate":{"description":"End date of the work period","type":"string","format":"date","example":"2024-01-31"},"totalHours":{"description":"Total hours worked during the period","type":"number","format":"float","example":168},"remoteWorkPercentage":{"description":"Percentage of remote work (0-100)","type":"number","format":"float","example":40},"notes":{"description":"Additional notes about the work period","type":"string","example":"January 2024 work period - focused on API development and client meetings"},"activities":{"type":"array","items":{"properties":{"activityName":{"description":"Name of the activity","type":"string","example":"Backend Development"},"hours":{"description":"Hours spent on this activity","type":"number","format":"float","example":120},"description":{"description":"Detailed description of the activity","type":"string","example":"REST API development and database optimization"},"isBillable":{"description":"Whether this activity is billable to a client","type":"boolean","example":true},"clientId":{"description":"ID of the client this activity is for (optional)","type":"integer","example":1,"nullable":true}},"type":"object"}}},"type":"object"}}}},"responses":{"201":{"description":"Work time entry created successfully","content":{"application/json":{"schema":{"properties":{"id":{"type":"integer","example":1},"startDate":{"type":"string","format":"date","example":"2024-01-01"},"endDate":{"type":"string","format":"date","example":"2024-01-31"},"totalHours":{"type":"number","format":"float","example":168},"remoteWorkPercentage":{"type":"number","format":"float","example":40},"notes":{"type":"string","example":"January 2024 work period - focused on API development and client meetings"},"isValidated":{"description":"Auto-validated if company setting allows","type":"boolean","example":true},"validationStatus":{"type":"string","example":"Validated"},"periodIdentifier":{"type":"string","example":"2024-01-01 to 2024-01-31"},"durationInDays":{"type":"integer","example":31},"activities":{"type":"array","items":{"properties":{"id":{"type":"integer","example":1},"activityName":{"type":"string","example":"Backend Development"},"hours":{"type":"number","format":"float","example":120},"description":{"type":"string","example":"REST API development and database optimization"},"isBillable":{"type":"boolean","example":true},"percentageOfTotal":{"type":"number","format":"float","example":71.4},"client":{"properties":{"id":{"type":"integer","example":1},"name":{"type":"string","example":"TechCorp Solutions"},"code":{"type":"string","example":"TECH"}},"type":"object","nullable":true}},"type":"object"}},"consultant":{"properties":{"id":{"type":"integer","example":1},"firstName":{"type":"string","example":"Marie"},"lastName":{"type":"string","example":"Dubois"}},"type":"object"}},"type":"object"}}}},"400":{"description":"Invalid input data","content":{"application/json":{"schema":{"properties":{"error":{"type":"string","example":"Start date cannot be after end date"},"success":{"type":"boolean","example":false}},"type":"object"}}}},"403":{"description":"Access denied","content":{"application/json":{"schema":{"properties":{"error":{"type":"string","example":"You must be logged in as a consultant to create work time entries"}},"type":"object"}}}}}}},"/api/v1/work-time/{id}":{"get":{"tags":["Work Time"],"summary":"Get a specific work time entry","operationId":"get\_app\_work\_time\_show","parameters":[{"name":"id","in":"path","description":"Work time entry ID","required":true,"schema":{"type":"integer"}}],"responses":{"200":{"description":"Returns the work time entry","content":{"application/json":{"schema":{"properties":{"id":{"type":"integer","example":1},"startDate":{"type":"string","format":"date","example":"2024-01-01"},"endDate":{"type":"string","format":"date","example":"2024-01-31"},"totalHours":{"type":"number","format":"float","example":168},"remoteWorkPercentage":{"type":"number","format":"float","example":40},"notes":{"type":"string","example":"January 2024 work period - focused on API development and client meetings"},"isValidated":{"type":"boolean","example":true},"validationStatus":{"type":"string","example":"Validated"},"periodIdentifier":{"type":"string","example":"2024-01-01 to 2024-01-31"},"durationInDays":{"type":"integer","example":31},"activities":{"type":"array","items":{"properties":{"id":{"type":"integer","example":1},"activityName":{"type":"string","example":"Backend Development"},"hours":{"type":"number","format":"float","example":120},"description":{"type":"string","example":"REST API development and database optimization"},"isBillable":{"type":"boolean","example":true},"percentageOfTotal":{"type":"number","format":"float","example":71.4},"client":{"properties":{"id":{"type":"integer","example":1},"name":{"type":"string","example":"TechCorp Solutions"},"code":{"type":"string","example":"TECH"}},"type":"object","nullable":true}},"type":"object"}},"consultant":{"properties":{"id":{"type":"integer","example":1},"firstName":{"type":"string","example":"Marie"},"lastName":{"type":"string","example":"Dubois"}},"type":"object"}},"type":"object"}}}},"404":{"description":"Work time entry not found","content":{"application/json":{"schema":{"properties":{"error":{"type":"string","example":"Work time entry not found"}},"type":"object"}}}}}},"put":{"tags":["Work Time"],"summary":"Update an existing work time entry","operationId":"put\_app\_work\_time\_update","parameters":[{"name":"id","in":"path","description":"Work time entry ID","required":true,"schema":{"type":"integer"}}],"requestBody":{"description":"Updated work time data with period and activities","required":true,"content":{"application/json":{"schema":{"properties":{"startDate":{"description":"Updated start date","type":"string","format":"date","example":"2024-01-01"},"endDate":{"description":"Updated end date","type":"string","format":"date","example":"2024-01-31"},"totalHours":{"description":"Updated total hours","type":"number","format":"float","example":175},"remoteWorkPercentage":{"description":"Updated remote work percentage","type":"number","format":"float","example":45},"notes":{"description":"Updated notes","type":"string","example":"Updated notes: January work completed with additional overtime"},"activities":{"description":"Updated activities list","type":"array","items":{"properties":{"activityName":{"type":"string","example":"Frontend Development"},"hours":{"type":"number","format":"float","example":100},"description":{"type":"string","example":"React components and UI improvements"},"isBillable":{"type":"boolean","example":true},"clientId":{"type":"integer","example":2,"nullable":true}},"type":"object"}}},"type":"object"}}}},"responses":{"200":{"description":"Work time entry updated successfully","content":{"application/json":{"schema":{"properties":{"id":{"type":"integer","example":1},"startDate":{"type":"string","format":"date","example":"2024-01-01"},"endDate":{"type":"string","format":"date","example":"2024-01-31"},"totalHours":{"type":"number","format":"float","example":175},"remoteWorkPercentage":{"type":"number","format":"float","example":45},"notes":{"type":"string","example":"Updated notes: January work completed with additional overtime"},"isValidated":{"type":"boolean","example":true},"validationStatus":{"type":"string","example":"Validated"},"periodIdentifier":{"type":"string","example":"2024-01-01 to 2024-01-31"},"durationInDays":{"type":"integer","example":31},"activities":{"type":"array","items":{"properties":{"id":{"type":"integer","example":2},"activityName":{"type":"string","example":"Frontend Development"},"hours":{"type":"number","format":"float","example":100},"description":{"type":"string","example":"React components and UI improvements"},"isBillable":{"type":"boolean","example":true},"percentageOfTotal":{"type":"number","format":"float","example":57.1},"client":{"properties":{"id":{"type":"integer","example":2},"name":{"type":"string","example":"Digital Innovations Ltd"},"code":{"type":"string","example":"DIGI"}},"type":"object","nullable":true}},"type":"object"}},"consultant":{"properties":{"id":{"type":"integer","example":1},"firstName":{"type":"string","example":"Marie"},"lastName":{"type":"string","example":"Dubois"}},"type":"object"}},"type":"object"}}}},"404":{"description":"Work time entry not found","content":{"application/json":{"schema":{"properties":{"error":{"type":"string","example":"Work time entry not found"}},"type":"object"}}}}}},"delete":{"tags":["Work Time"],"summary":"Delete a work time entry","operationId":"delete\_app\_work\_time\_delete","parameters":[{"name":"id","in":"path","description":"Work time entry ID","required":true,"schema":{"type":"integer"}}],"responses":{"200":{"description":"Work time entry deleted successfully","content":{"application/json":{"schema":{"properties":{"id":{"type":"integer","example":1},"periodIdentifier":{"type":"string","example":"2024-01-01 to 2024-01-31"},"message":{"type":"string","example":"Work time entry successfully deleted"},"success":{"type":"boolean","example":true}},"type":"object"}}}},"404":{"description":"Work time entry not found","content":{"application/json":{"schema":{"properties":{"error":{"type":"string","example":"Work time entry not found"}},"type":"object"}}}}}}},"/api/v1/work-time/consultant/{consultantId}":{"get":{"tags":["Work Time"],"summary":"Get work time entries for a specific consultant","operationId":"get\_app\_work\_time\_by\_consultant","parameters":[{"name":"consultantId","in":"path","description":"Consultant ID","required":true,"schema":{"type":"integer"}},{"name":"start","in":"query","description":"Start date (format: Y-m-d)","required":false,"schema":{"type":"string","format":"date"}},{"name":"end","in":"query","description":"End date (format: Y-m-d)","required":false,"schema":{"type":"string","format":"date"}}],"responses":{"200":{"description":"Returns work time entries for the consultant","content":{"application/json":{"schema":{"type":"array","items":{"properties":{"id":{"type":"integer","example":1},"startDate":{"type":"string","format":"date","example":"2024-01-01"},"endDate":{"type":"string","format":"date","example":"2024-01-31"},"totalHours":{"type":"number","format":"float","example":168},"remoteWorkPercentage":{"type":"number","format":"float","example":40},"notes":{"type":"string","example":"January 2024 work period"},"isValidated":{"type":"boolean","example":true},"validationStatus":{"type":"string","example":"Validated"},"periodIdentifier":{"type":"string","example":"2024-01-01 to 2024-01-31"},"durationInDays":{"type":"integer","example":31},"activities":{"type":"array","items":{"properties":{"id":{"type":"integer","example":1},"activityName":{"type":"string","example":"Backend Development"},"hours":{"type":"number","format":"float","example":120},"description":{"type":"string","example":"API development"},"isBillable":{"type":"boolean","example":true},"percentageOfTotal":{"type":"number","format":"float","example":71.4}},"type":"object"}},"consultant":{"properties":{"id":{"type":"integer","example":1},"firstName":{"type":"string","example":"Marie"},"lastName":{"type":"string","example":"Dubois"}},"type":"object"}},"type":"object"}}}}},"404":{"description":"Consultant not found","content":{"application/json":{"schema":{"properties":{"error":{"type":"string","example":"Consultant not found"}},"type":"object"}}}}}}},"/api/v1/work-time/statistics/{consultantId}":{"get":{"tags":["Work Time"],"summary":"Get work time statistics for a consultant","operationId":"get\_app\_work\_time\_statistics","parameters":[{"name":"consultantId","in":"path","description":"Consultant ID","required":true,"schema":{"type":"integer"}},{"name":"start","in":"query","description":"Start date (format: Y-m-d)","required":false,"schema":{"type":"string","format":"date"}},{"name":"end","in":"query","description":"End date (format: Y-m-d)","required":false,"schema":{"type":"string","format":"date"}}],"responses":{"200":{"description":"Returns work time statistics","content":{"application/json":{"schema":{"properties":{"totalHours":{"description":"Total hours worked in the period","type":"number","format":"float","example":343},"remoteHours":{"description":"Hours worked remotely","type":"number","format":"float","example":137.2},"remotePercentage":{"description":"Percentage of remote work","type":"number","format":"float","example":40},"averageHoursPerDay":{"description":"Average hours per working day","type":"number","format":"float","example":7.8},"daysWorked":{"description":"Number of working days","type":"integer","example":44},"periodsCount":{"description":"Number of work time periods","type":"integer","example":2},"activitiesBreakdown":{"description":"Breakdown of hours by activity type","type":"object","example":{"Backend Development":200,"Frontend Development":100,"Meetings":30,"Documentation":13}}},"type":"object"}}}},"404":{"description":"Consultant not found","content":{"application/json":{"schema":{"properties":{"error":{"type":"string","example":"Consultant not found"}},"type":"object"}}}}}}},"/api/v1/work-time/{id}/validate":{"post":{"tags":["Work Time"],"summary":"Validate a work time entry (admin only)","operationId":"post\_app\_work\_time\_validate","parameters":[{"name":"id","in":"path","description":"Work time entry ID","required":true,"schema":{"type":"integer"}}],"responses":{"200":{"description":"Work time entry validated successfully","content":{"application/json":{"schema":{"properties":{"id":{"type":"integer","example":1},"isValidated":{"type":"boolean","example":true},"validatedAt":{"type":"string","format":"datetime","example":"2024-02-01T14:30:00Z"},"validatedBy":{"properties":{"id":{"type":"integer","example":2},"firstName":{"type":"string","example":"Pierre"},"lastName":{"type":"string","example":"Martin"}},"type":"object"},"message":{"type":"string","example":"Work time entry validated successfully"}},"type":"object"}}}}}}}},"components":{"schemas":{"Consultant":{"description":"Consultant model","required":["id","email","firstName","lastName","isAdmin","roles","createdAt","updatedAt"],"properties":{"id":{"description":"Consultant ID","type":"integer","example":1},"email":{"description":"Consultant email","type":"string","example":"<EMAIL>"},"firstName":{"description":"Consultant first name","type":"string","example":"John"},"lastName":{"description":"Consultant last name","type":"string","example":"Doe"},"phone":{"description":"Consultant phone number","type":"string","example":"+33123456789","nullable":true},"isAdmin":{"description":"Whether the consultant is an admin","type":"boolean","example":false},"roles":{"description":"Consultant roles","type":"array","items":{"type":"string"},"example":["ROLE\_USER"]},"createdAt":{"description":"Creation date","type":"string","format":"date-time","example":"2023-01-01T12:00:00+00:00"},"updatedAt":{"description":"Last update date","type":"string","format":"date-time","example":"2023-01-01T12:00:00+00:00"}},"type":"object"},"RegistrationUserInfo":{"description":"User information after registration","required":["id","email","firstName","lastName"],"properties":{"id":{"description":"User ID","type":"integer","example":1},"email":{"description":"User email","type":"string","example":"<EMAIL>"},"firstName":{"description":"User first name","type":"string","example":"John"},"lastName":{"description":"User last name","type":"string","example":"Doe"}},"type":"object"},"RegistrationResponse":{"description":"Registration response model","required":["message","user"],"properties":{"message":{"description":"Success message","type":"string","example":"User registered successfully"},"user":{"$ref":"#/components/schemas/RegistrationUserInfo"}},"type":"object"},"RegistrationRequest":{"description":"Registration request model","required":["email","password","firstName","lastName"],"properties":{"email":{"description":"User email","type":"string","example":"<EMAIL>"},"password":{"description":"User password","type":"string","example":"password123"},"firstName":{"description":"User first name","type":"string","example":"John"},"lastName":{"description":"User last name","type":"string","example":"Doe"},"phone":{"description":"User phone number","type":"string","example":"+33123456789","nullable":true}},"type":"object"},"UserInfo":{"description":"User information","required":["id","email","firstName","lastName","roles","isAdmin"],"properties":{"id":{"description":"User ID","type":"integer","example":1},"email":{"description":"User email","type":"string","example":"<EMAIL>"},"firstName":{"description":"User first name","type":"string","example":"John"},"lastName":{"description":"User last name","type":"string","example":"Doe"},"roles":{"description":"User roles","type":"array","items":{"type":"string"},"example":["ROLE\_USER"]},"isAdmin":{"description":"Whether the user is an admin","type":"boolean","example":false}},"type":"object"},"LoginResponse":{"description":"Login response model","required":["token","user"],"properties":{"token":{"description":"JWT token","type":"string","example":"eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiJ9..."},"user":{"$ref":"#/components/schemas/UserInfo"}},"type":"object"},"LoginRequest":{"description":"Login request model","required":["email","password"],"properties":{"email":{"description":"User email","type":"string","example":"<EMAIL>"},"password":{"description":"User password","type":"string","example":"password123"}},"type":"object"}},"securitySchemes":{"Bearer":{"type":"http","description":"Enter JWT token in the format: Bearer {token}","bearerFormat":"JWT","scheme":"bearer"}}},"security":[{"Bearer":[]}],"tags":[{"name":"Authentication","description":"User authentication and token management"},{"name":"Consultants","description":"Consultant profile management"},{"name":"Work Time","description":"Work time tracking and management"},{"name":"Expenses","description":"Expense reporting and management"},{"name":"Leaves","description":"Leave request management"},{"name":"Company","description":"Company information management"},{"name":"Dashboard","description":"Analytics and reporting"},{"name":"Clients","description":"Client management and information"},{"name":"Trips","description":"Business trip planning and tracking"},{"name":"Expense Categories","description":"Expense category management and hierarchy"}]}}































#  Consultant Management System API



API 1.0.0


OAS 3.0




A comprehensive RESTful API for efficient consultant management, time tracking, expense reporting, and leave management




###  About


This API provides efficient consultant management, time tracking, expense reporting, and leave management capabilities.




###  Quick Links



 [Home](https://staging.api.erp.fsli-group.com/)
 [Authenticate](https://staging.api.erp.fsli-group.com/api/v1/doc#)



###  API Details


`Base URL: /api/v1`







.logo\_small\_svg\_\_cls-2{fill:#fff}.logo\_small\_svg\_\_cls-3{fill:#85ea2d}Explore## Consultant Management System API
```
 1.0.0 
```

```
OAS 3.0
```

A comprehensive RESTful API for efficient consultant management, time tracking, expense reporting, and leave management

[API Support - Website](https://consultantmanagementsystem.com/support)[Send email to API Support](mailto:<EMAIL>)Authorize### AuthenticationUser authentication and token management

 POST/api/v1/auth/loginLogin to get a JWT tokenPOST/api/v1/auth/registerRegister a new user ### ConsultantsConsultant profile management

 GET/api/v1/consultantsGet all consultants (admin only)POST/api/v1/consultantsCreate a new consultant (admin only)GET/api/v1/consultants/meGet the current consultant's profileGET/api/v1/consultants/{id}Get a specific consultantPUT/api/v1/consultants/{id}Update an existing consultantDELETE/api/v1/consultants/{id}Delete a consultant (admin only) ### Work TimeWork time tracking and management

 GET/api/v1/work-timeGet all work time entries (admin only)POST/api/v1/work-timeCreate a new work time entry with period and activities #### Parameters

Try it out No parameters

#### Request body

application/jsonWork time data with period and activities

* Example Value
* Schema


```
{
 "consultantId": 1,
 "startDate": "2024-01-01",
 "endDate": "2024-01-31",
 "totalHours": 168,
 "remoteWorkPercentage": 40,
 "notes": "January 2024 work period - focused on API development and client meetings",
 "activities": [
    {
 "activityName": "Backend Development",
 "hours": 120,
 "description": "REST API development and database optimization",
 "isBillable": true,
 "clientId": 1
    }
  ]
}
```
#### Responses



| Code | Description | Links |
| 201 | Work time entry created successfullyMedia typeapplication/jsonControls `Accept` header.* Example Value
* Schema


```
{
 "id": 1,
 "startDate": "2024-01-01",
 "endDate": "2024-01-31",
 "totalHours": 168,
 "remoteWorkPercentage": 40,
 "notes": "January 2024 work period - focused on API development and client meetings",
 "isValidated": true,
 "validationStatus": "Validated",
 "periodIdentifier": "2024-01-01 to 2024-01-31",
 "durationInDays": 31,
 "activities": [
    {
 "id": 1,
 "activityName": "Backend Development",
 "hours": 120,
 "description": "REST API development and database optimization",
 "isBillable": true,
 "percentageOfTotal": 71.4,
 "client": {
 "id": 1,
 "name": "TechCorp Solutions",
 "code": "TECH"
      }
    }
  ],
 "consultant": {
 "id": 1,
 "firstName": "Marie",
 "lastName": "Dubois"
  }
}
```
 | *No links* |
| 400 | Invalid input dataMedia typeapplication/json* Example Value
* Schema


```
{
 "error": "Start date cannot be after end date",
 "success": false
}
```
 | *No links* |
| 403 | Access deniedMedia typeapplication/json* Example Value
* Schema


```
{
 "error": "You must be logged in as a consultant to create work time entries"
}
```
 | *No links* |

 GET/api/v1/work-time/{id}Get a specific work time entryPUT/api/v1/work-time/{id}Update an existing work time entryDELETE/api/v1/work-time/{id}Delete a work time entryGET/api/v1/work-time/consultant/{consultantId}Get work time entries for a specific consultantGET/api/v1/work-time/statistics/{consultantId}Get work time statistics for a consultantPOST/api/v1/work-time/{id}/validateValidate a work time entry (admin only) ### ExpensesExpense reporting and management

 GET/api/v1/expensesGet all expenses (admin only)POST/api/v1/expensesCreate a new expenseGET/api/v1/expenses/non-validatedGet all non-validated expenses (admin only)GET/api/v1/expenses/myGet all expenses for the current userGET/api/v1/expenses/{id}Get a specific expense by IDPUT/api/v1/expenses/{id}Update an existing expenseDELETE/api/v1/expenses/{id}Delete an expenseGET/api/v1/expenses/consultant/{consultantId}Get expenses for a specific consultantPUT/api/v1/expenses/{id}/validateValidate an expense (admin only)GET/api/v1/expenses/total/{consultantId}Calculate total expenses for a consultant ### LeavesLeave request management

 GET/api/v1/leavesGet all leave requests (admin only)POST/api/v1/leavesCreate a new leave requestGET/api/v1/leaves/pendingGet all pending leave requests (admin only)GET/api/v1/leaves/{id}Get a specific leave request by IDPUT/api/v1/leaves/{id}Update an existing leave requestDELETE/api/v1/leaves/{id}Delete a leave requestGET/api/v1/leaves/consultant/{consultantId}Get leave requests for a specific consultantPUT/api/v1/leaves/{id}/approveApprove a leave request (admin only)PUT/api/v1/leaves/{id}/rejectReject a leave request (admin only)GET/api/v1/leaves/balance/{consultantId}Calculate the remaining leave balance for a consultant ### CompanyCompany information management

 GET/api/v1/companyGet company informationPUT/api/v1/companyUpdate company information (admin only) ### DashboardAnalytics and reporting

 GET/api/v1/dashboard/consultant/{consultantId}Get dashboard data for a specific consultantGET/api/v1/dashboard/adminGet admin dashboard data (admin only) ### ClientsClient management and information

 GET/api/v1/clientsGet all clientsPOST/api/v1/clientsCreate a new client (admin only)GET/api/v1/clients/{id}Get a specific clientPUT/api/v1/clients/{id}Update a client (admin only)DELETE/api/v1/clients/{id}Delete a client (admin only) ### TripsBusiness trip planning and tracking

 GET/api/v1/tripsGet all trips (admin) or consultant's tripsPOST/api/v1/tripsCreate a new tripGET/api/v1/trips/{id}Get a specific tripPUT/api/v1/trips/{id}Update a tripDELETE/api/v1/trips/{id}Delete a tripGET/api/v1/trips/myGet all trips for the current user ### Expense CategoriesExpense category management and hierarchy

 GET/api/v1/expense-categoriesGet all expense categoriesPOST/api/v1/expense-categoriesCreate a new expense category (admin only)GET/api/v1/expense-categories/{id}Get a specific expense categoryPUT/api/v1/expense-categories/{id}Update an expense category (admin only)DELETE/api/v1/expense-categories/{id}Delete an expense category (admin only)GET/api/v1/expense-categories/{id}/subcategoriesGet subcategories of a parent category #### Schemas

 Consultant RegistrationUserInfo RegistrationResponse RegistrationRequest UserInfo LoginResponse LoginRequest  


© 2025 FSLI Group. All rights reserved.



[Home](https://staging.api.erp.fsli-group.com/)
[Terms of Service](https://staging.api.erp.fsli-group.com/api/v1/doc#)
[Privacy Policy](https://staging.api.erp.fsli-group.com/api/v1/doc#)






 function authorizeModal() {
 // Find and click the authorize button
 const authorizeBtn = document.querySelector('.swagger-ui .auth-wrapper .authorize');
 if (authorizeBtn) {
 authorizeBtn.click();
 }
 }

 // Add custom initialization after Swagger UI loads
 window.addEventListener('load', function() {
 // Add some delay to ensure Swagger UI is fully loaded
 setTimeout(function() {
 // Expand all operations by default
 const expandButtons = document.querySelectorAll('.opblock-tag');
 expandButtons.forEach(function(button) {
 if (button.getAttribute('aria-expanded') === 'false') {
 button.click();
 }
 });

 // Hide the redundant info section under the header
 const infoSection = document.querySelector('.swagger-ui .info');
 if (infoSection) {
 infoSection.style.display = 'none';
 }

 // Fine-tune the body padding based on actual header height
 const header = document.querySelector('.custom-swagger-header');

 if (header) {
 // Get the header height
 const headerHeight = header.offsetHeight;

 // Adjust body padding to match exact header height
 document.body.style.paddingTop = headerHeight + 'px';
 }
 }, 1000);
 });
 


 window.onload = () => {
 loadSwaggerUI([]);
 };
 
